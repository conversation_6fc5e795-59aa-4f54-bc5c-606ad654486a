# Contract Testing Setup Guide

## Quick Start

This guide will help you set up contract testing for local development in under 10 minutes.

### Prerequisites Checklist

- [ ] Node.js 16+ installed
- [ ] npm or yarn package manager
- [ ] Git repository cloned locally
- [ ] IDE/editor with JavaScript/TypeScript support

### Installation Steps

#### 1. Verify Environment

```bash
# Check Node.js version
node --version  # Should be 16+

# Check npm version
npm --version

# Verify you're in the project root
ls -la | grep package.json
```

#### 2. Install Dependencies

The Pact.js dependencies are already included in the project. Verify installation:

```bash
# Install all dependencies
npm install

# Verify Pact installation
npm list @pact-foundation/pact
```

#### 3. Verify Contract Test Setup

```bash
# Run contract tests to verify setup
npm run test:contracts

# If the above command doesn't exist, use:
npm test -- --testPathPattern=contracts
```

#### 4. Generate Your First Contract

```bash
# Run a specific contract test
npm test -- tests/contracts/hotels-api/properties.contract.test.js

# Check generated contracts
ls -la pacts/
```

### Directory Structure Setup

The contract testing structure is already configured:

```
qantas-hotels/
├── tests/
│   ├── contracts/
│   │   ├── hotels-api/           # Hotels API contract tests
│   │   ├── auth-api/             # Authentication API tests
│   │   ├── sanity-cms/           # Content management tests
│   │   └── payment-services/     # Payment service tests
│   ├── helpers/
│   │   ├── contract-test-helpers.js
│   │   ├── mock-data-generators.js
│   │   └── schema-validators.js
│   └── config/
│       └── pact-config.js
├── pacts/                        # Generated contract files
├── logs/                         # Pact logs
└── docs/                         # Documentation
```

### Configuration Files

#### Jest Configuration

Contract tests use the existing Jest configuration with additional Pact setup:

```javascript
// jest/jest.config.contracts.ts
module.exports = {
  ...require('./jest.config.base'),
  testMatch: ['**/tests/contracts/**/*.test.js'],
  setupFilesAfterEnv: ['<rootDir>/tests/helpers/contract-test-setup.js'],
  testTimeout: 30000
};
```

#### Pact Configuration

```javascript
// tests/config/pact-config.js
const path = require('path');

const pactConfig = {
  consumer: 'qantas-hotels-frontend',
  logLevel: process.env.PACT_LOG_LEVEL || 'INFO',
  log: path.resolve(process.cwd(), 'logs', 'pact.log'),
  dir: path.resolve(process.cwd(), 'pacts'),
  spec: 2
};

module.exports = pactConfig;
```

### Environment Variables

Create a `.env.contracts` file for contract testing configuration:

```bash
# .env.contracts
PACT_LOG_LEVEL=INFO
PACT_BROKER_URL=http://localhost:9292
PACT_BROKER_USERNAME=
PACT_BROKER_PASSWORD=
CONTRACT_TESTS_ENABLED=true
```

### NPM Scripts

Add these scripts to your `package.json` if not already present:

```json
{
  "scripts": {
    "test:contracts": "jest --config jest/jest.config.contracts.ts",
    "test:contracts:watch": "jest --config jest/jest.config.contracts.ts --watch",
    "contracts:generate": "npm run test:contracts",
    "contracts:verify": "pact-broker can-i-deploy --pacticipant qantas-hotels-frontend",
    "contracts:publish": "pact-broker publish pacts --consumer-app-version $BUILD_NUMBER"
  }
}
```

## Local Development Workflow

### Daily Development Process

1. **Start Development**:
   ```bash
   # Pull latest changes
   git pull origin main
   
   # Install any new dependencies
   npm install
   
   # Run contract tests to ensure baseline
   npm run test:contracts
   ```

2. **Making API Changes**:
   ```bash
   # Run specific contract tests while developing
   npm test -- --testPathPattern="hotels-api" --watch
   
   # Generate contracts after changes
   npm run contracts:generate
   ```

3. **Before Committing**:
   ```bash
   # Run all contract tests
   npm run test:contracts
   
   # Check generated contracts
   git status pacts/
   
   # Commit both code and contract changes
   git add .
   git commit -m "feat: update booking API contract"
   ```

### IDE Setup

#### VS Code Extensions

Install these recommended extensions:

- **Jest** - For running tests in IDE
- **REST Client** - For testing API endpoints
- **JSON Schema** - For contract validation

#### VS Code Settings

Add to your `.vscode/settings.json`:

```json
{
  "jest.jestCommandLine": "npm run test:contracts",
  "jest.autoRun": {
    "watch": false,
    "onStartup": ["all-tests"]
  },
  "files.associations": {
    "*.contract.test.js": "javascript"
  }
}
```

### Running Tests

#### Basic Commands

```bash
# Run all contract tests
npm run test:contracts

# Run specific service tests
npm test -- --testPathPattern="hotels-api"

# Run specific test file
npm test -- tests/contracts/hotels-api/bookings.contract.test.js

# Run with watch mode
npm run test:contracts:watch
```

#### Advanced Options

```bash
# Run with verbose output
npm test -- --testPathPattern=contracts --verbose

# Run with coverage
npm test -- --testPathPattern=contracts --coverage

# Run specific test by name
npm test -- --testPathPattern=contracts --testNamePattern="should create booking"

# Debug mode
npm test -- --testPathPattern=contracts --runInBand --detectOpenHandles
```

### Debugging Contract Tests

#### Enable Debug Logging

```bash
# Set environment variable
export PACT_LOG_LEVEL=DEBUG

# Or run with inline variable
PACT_LOG_LEVEL=DEBUG npm run test:contracts
```

#### Check Generated Contracts

```bash
# View generated contract files
cat pacts/qantas-hotels-frontend-hotels-api.json | jq '.'

# Check contract interactions
jq '.interactions[] | {description, request: .request.path}' pacts/*.json
```

#### Common Debug Commands

```bash
# Check Pact mock server logs
tail -f logs/pact.log

# Validate contract JSON
jq empty pacts/*.json && echo "Valid JSON" || echo "Invalid JSON"

# Check test output with detailed errors
npm test -- --testPathPattern=contracts --verbose --no-cache
```

## Troubleshooting Setup Issues

### Issue: Contract Tests Not Found

**Symptoms**: Jest reports "No tests found"

**Solution**:
```bash
# Check test file patterns
find tests/ -name "*.contract.test.js"

# Verify Jest config
cat jest/jest.config.contracts.ts

# Run with explicit pattern
npm test -- "tests/contracts/**/*.test.js"
```

### Issue: Pact Dependencies Missing

**Symptoms**: Module not found errors for @pact-foundation/pact

**Solution**:
```bash
# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install

# Verify Pact installation
npm list @pact-foundation/pact --depth=0
```

### Issue: Port Conflicts

**Symptoms**: "Port already in use" errors

**Solution**:
```bash
# Check what's using the port
lsof -i :1234

# Kill processes using the port
kill -9 $(lsof -t -i:1234)

# Use dynamic port allocation in tests
```

### Issue: Permission Errors

**Symptoms**: Cannot write to pacts/ or logs/ directories

**Solution**:
```bash
# Create directories with proper permissions
mkdir -p pacts logs
chmod 755 pacts logs

# Check current permissions
ls -la pacts/ logs/
```

### Issue: Contract Generation Fails

**Symptoms**: No .json files in pacts/ directory after tests

**Solution**:
```bash
# Check test execution
npm test -- --testPathPattern=contracts --verbose

# Verify provider.verify() is called
grep -r "provider.verify" tests/contracts/

# Check for test failures
npm test -- --testPathPattern=contracts --bail
```

## Next Steps

After completing setup:

1. **Read the main guide**: [CONTRACT_TESTING_GUIDE.md](./CONTRACT_TESTING_GUIDE.md)
2. **Write your first test**: Follow the examples in the guide
3. **Integrate with CI/CD**: Set up automated contract verification
4. **Share contracts**: Configure contract sharing with provider teams

## Getting Help

- **Documentation**: See [CONTRACT_TESTING_GUIDE.md](./CONTRACT_TESTING_GUIDE.md)
- **Team Support**: Contact platform team for setup issues
- **Community**: Check [Pact.js documentation](https://docs.pact.io/implementation_guides/javascript)

---

*Setup complete! You're ready to start writing contract tests.*