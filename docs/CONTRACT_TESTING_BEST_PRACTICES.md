# Contract Testing Best Practices

## Core Principles

### 1. Consumer-Driven Approach

Contract tests should be written from the consumer's perspective, focusing on what the front-end application actually needs from the API.

```javascript
// ✅ Good: Test what the consumer actually uses
{
  body: {
    booking: {
      id: Pact.Matchers.string(),
      status: Pact.Matchers.term({
        matcher: 'confirmed|pending|cancelled',
        generate: 'confirmed'
      }),
      totalAmount: {
        currency: 'AUD',
        amount: Pact.Matchers.decimal()
      }
    }
  }
}

// ❌ Bad: Testing fields the consumer doesn't use
{
  body: {
    booking: {
      id: '12345',
      status: 'confirmed',
      internalId: '67890',           // Consumer doesn't use this
      auditTrail: [...],             // Consumer doesn't use this
      systemMetadata: {...}          // Consumer doesn't use this
    }
  }
}
```

### 2. Minimal Contracts

Only include the fields and interactions that your consumer actually uses. This reduces brittleness and maintenance overhead.

```javascript
// ✅ Good: Minimal, focused contract
const propertyContract = {
  id: Pact.Matchers.string(),
  name: Pact.Matchers.string(),
  location: {
    city: Pact.Matchers.string(),
    country: Pact.Matchers.string()
  },
  pricing: {
    currency: 'AUD',
    baseRate: Pact.Matchers.decimal()
  }
};

// ❌ Bad: Over-specified contract
const propertyContract = {
  id: Pact.Matchers.string(),
  name: Pact.Matchers.string(),
  description: Pact.Matchers.string(),
  location: {
    city: Pact.Matchers.string(),
    state: Pact.Matchers.string(),
    country: Pact.Matchers.string(),
    postcode: Pact.Matchers.string(),
    coordinates: {
      latitude: Pact.Matchers.decimal(),
      longitude: Pact.Matchers.decimal()
    },
    timezone: Pact.Matchers.string()
  },
  // ... many more fields the consumer doesn't use
};
```

### 3. Realistic Test Data

Use realistic test data that represents actual production scenarios, but keep it consistent and predictable.

```javascript
// ✅ Good: Realistic, consistent test data
const mockBookingData = {
  propertyId: 'prop-sydney-harbour-hotel-001',
  checkIn: '2024-06-15',
  checkOut: '2024-06-18',
  guests: {
    adults: 2,
    children: 1,
    infants: 0
  },
  roomType: 'deluxe-harbour-view',
  totalAmount: {
    currency: 'AUD',
    amount: 450.00
  }
};

// ❌ Bad: Generic, unrealistic test data
const mockBookingData = {
  propertyId: '1',
  checkIn: '2023-01-01',
  checkOut: '2023-01-02',
  guests: {
    adults: 1
  },
  totalAmount: {
    currency: 'USD',
    amount: 100
  }
};
```

## Test Organization

### 1. Directory Structure

Organize tests by API service and functionality:

```
tests/contracts/
├── hotels-api/
│   ├── bookings.contract.test.js
│   ├── properties.contract.test.js
│   ├── search.contract.test.js
│   └── quotes.contract.test.js
├── auth-api/
│   ├── authentication.contract.test.js
│   └── member-services.contract.test.js
├── sanity-cms/
│   ├── content.contract.test.js
│   └── campaigns.contract.test.js
└── payment-services/
    ├── adyen-payments.contract.test.js
    └── points-payments.contract.test.js
```

### 2. Test Naming Conventions

Use descriptive, consistent naming for tests and interactions:

```javascript
// ✅ Good: Descriptive test names
describe('Hotels API - Booking Management', () => {
  describe('POST /bookings', () => {
    it('should create a confirmed booking for authenticated user', () => {
      // Test implementation
    });

    it('should return validation error for missing check-in date', () => {
      // Test implementation
    });

    it('should return unauthorized error for invalid token', () => {
      // Test implementation
    });
  });
});

// ❌ Bad: Generic test names
describe('API Tests', () => {
  it('should work', () => {
    // Test implementation
  });

  it('should fail', () => {
    // Test implementation
  });
});
```

### 3. Interaction Descriptions

Write clear, specific interaction descriptions:

```javascript
// ✅ Good: Clear interaction descriptions
provider.addInteraction({
  state: 'property is available for booking',
  uponReceiving: 'a booking request for Sydney Harbour Hotel with 2 adults',
  withRequest: {
    method: 'POST',
    path: '/bookings',
    // ...
  },
  willRespondWith: {
    status: 201,
    // ...
  }
});

// ❌ Bad: Vague interaction descriptions
provider.addInteraction({
  state: 'data exists',
  uponReceiving: 'a request',
  // ...
});
```

## Data Management

### 1. Mock Data Generators

Create reusable mock data generators for consistency:

```javascript
// tests/helpers/mock-data-generators.js
class MockDataGenerator {
  static property(overrides = {}) {
    return {
      id: `prop-${Date.now()}`,
      name: 'Sydney Harbour Hotel',
      location: {
        city: 'Sydney',
        country: 'Australia',
        coordinates: {
          latitude: -33.8688,
          longitude: 151.2093
        }
      },
      rating: {
        type: 'star',
        value: 4
      },
      pricing: {
        currency: 'AUD',
        baseRate: 250.00
      },
      ...overrides
    };
  }

  static booking(overrides = {}) {
    return {
      id: `booking-${Date.now()}`,
      status: 'confirmed',
      propertyId: 'prop-sydney-harbour-hotel-001',
      checkIn: '2024-06-15',
      checkOut: '2024-06-18',
      guests: {
        adults: 2,
        children: 0,
        infants: 0
      },
      totalAmount: {
        currency: 'AUD',
        amount: 750.00
      },
      ...overrides
    };
  }

  static authToken(overrides = {}) {
    return {
      accessToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
      tokenType: 'Bearer',
      expiresIn: 3600,
      ...overrides
    };
  }
}

module.exports = MockDataGenerator;
```

### 2. Provider States

Use provider states to set up test scenarios:

```javascript
// ✅ Good: Specific provider states
provider.addInteraction({
  state: 'property prop-123 is available from 2024-06-15 to 2024-06-18',
  uponReceiving: 'availability check for property prop-123',
  // ...
});

provider.addInteraction({
  state: 'user user-456 is authenticated with QFF membership',
  uponReceiving: 'member details request for user-456',
  // ...
});

// ❌ Bad: Generic provider states
provider.addInteraction({
  state: 'data exists',
  uponReceiving: 'a request',
  // ...
});
```

### 3. Schema Validation

Use Pact matchers for flexible schema validation:

```javascript
// ✅ Good: Flexible schema validation
body: {
  properties: Pact.Matchers.eachLike({
    id: Pact.Matchers.string(),
    name: Pact.Matchers.string(),
    rating: Pact.Matchers.integer(),
    pricing: {
      currency: Pact.Matchers.term({
        matcher: 'AUD|USD|EUR',
        generate: 'AUD'
      }),
      baseRate: Pact.Matchers.decimal()
    },
    availability: Pact.Matchers.boolean()
  }, { min: 1 })
}

// ❌ Bad: Rigid exact matching
body: {
  properties: [
    {
      id: 'prop-123',
      name: 'Exact Hotel Name',
      rating: 4,
      pricing: {
        currency: 'AUD',
        baseRate: 250.00
      },
      availability: true
    }
  ]
}
```

## Error Handling

### 1. Comprehensive Error Scenarios

Test both success and failure scenarios:

```javascript
describe('Error Handling', () => {
  it('should handle invalid authentication token', async () => {
    provider.addInteraction({
      state: 'user token is invalid',
      uponReceiving: 'request with invalid authentication token',
      withRequest: {
        method: 'GET',
        path: '/bookings',
        headers: {
          'Authorization': 'Bearer invalid-token'
        }
      },
      willRespondWith: {
        status: 401,
        headers: {
          'Content-Type': 'application/json'
        },
        body: {
          error: {
            code: 'INVALID_TOKEN',
            message: 'Authentication token is invalid or expired',
            timestamp: Pact.Matchers.iso8601DateTime()
          }
        }
      }
    });

    await expect(apiClient.getBookings('invalid-token'))
      .rejects.toThrow('Authentication token is invalid');
  });

  it('should handle validation errors for booking creation', async () => {
    provider.addInteraction({
      state: 'booking validation will fail',
      uponReceiving: 'booking request with missing required fields',
      withRequest: {
        method: 'POST',
        path: '/bookings',
        body: {
          propertyId: 'prop-123'
          // Missing checkIn, checkOut, guests
        }
      },
      willRespondWith: {
        status: 400,
        body: {
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Required fields are missing',
            details: {
              missingFields: ['checkIn', 'checkOut', 'guests']
            }
          }
        }
      }
    });

    await expect(apiClient.createBooking({ propertyId: 'prop-123' }))
      .rejects.toThrow('Required fields are missing');
  });
});
```

### 2. Consistent Error Response Format

Ensure error responses follow a consistent format:

```javascript
const errorResponseSchema = {
  error: {
    code: Pact.Matchers.string(),
    message: Pact.Matchers.string(),
    timestamp: Pact.Matchers.iso8601DateTime(),
    requestId: Pact.Matchers.uuid(),
    details: Pact.Matchers.like({})
  }
};

// Use in all error scenarios
willRespondWith: {
  status: 400,
  body: errorResponseSchema
}
```

## Performance Optimization

### 1. Parallel Test Execution

Configure tests to run in parallel:

```javascript
// jest.config.contracts.js
module.exports = {
  maxWorkers: 4,
  testTimeout: 30000,
  setupFilesAfterEnv: ['<rootDir>/tests/helpers/contract-test-setup.js']
};
```

### 2. Resource Management

Properly manage Pact provider instances:

```javascript
describe('Hotels API Contract Tests', () => {
  let provider;

  beforeAll(async () => {
    provider = new Pact({
      consumer: 'qantas-hotels-frontend',
      provider: 'hotels-api',
      port: 0, // Use dynamic port allocation
      logLevel: 'ERROR' // Reduce log noise in CI
    });
    await provider.setup();
  });

  afterEach(async () => {
    await provider.verify();
  });

  afterAll(async () => {
    await provider.finalize();
  });

  // Tests here...
});
```

### 3. Selective Test Execution

Use test patterns for faster development cycles:

```bash
# Run only booking-related tests
npm test -- --testPathPattern="bookings"

# Run tests for specific API
npm test -- --testPathPattern="hotels-api"

# Run single test file
npm test -- tests/contracts/hotels-api/properties.contract.test.js
```

## Maintenance and Evolution

### 1. Contract Versioning

Version your contracts to manage breaking changes:

```javascript
const provider = new Pact({
  consumer: 'qantas-hotels-frontend',
  provider: 'hotels-api',
  pactfileWriteMode: 'update', // Merge interactions
  spec: 2 // Pact specification version
});
```

### 2. Regular Contract Review

Establish a process for reviewing and updating contracts:

1. **Weekly Review**: Check for unused interactions
2. **Monthly Cleanup**: Remove obsolete contracts
3. **Quarterly Assessment**: Evaluate contract coverage
4. **Release Planning**: Update contracts for new features

### 3. Contract Documentation

Document contract changes and decisions:

```javascript
// Document complex interactions
provider.addInteraction({
  state: 'property has exclusive offers',
  uponReceiving: 'request for property with exclusive QFF member offers',
  // This interaction tests the QFF member exclusive pricing
  // which includes both base rate and member discount
  withRequest: {
    method: 'GET',
    path: '/properties/prop-123/offers',
    headers: {
      'Authorization': 'Bearer qff-member-token',
      'X-Member-Tier': 'Gold'
    }
  },
  willRespondWith: {
    status: 200,
    body: {
      offers: Pact.Matchers.eachLike({
        id: Pact.Matchers.string(),
        type: 'member-exclusive',
        discount: {
          percentage: Pact.Matchers.integer(),
          amount: Pact.Matchers.decimal()
        }
      })
    }
  }
});
```

## Common Anti-Patterns to Avoid

### 1. Over-Specification

```javascript
// ❌ Bad: Testing implementation details
body: {
  timestamp: '2024-01-15T10:30:00.123Z', // Exact timestamp
  processingTime: 245,                    // Internal metric
  serverVersion: '1.2.3'                  // Implementation detail
}

// ✅ Good: Testing contract requirements
body: {
  timestamp: Pact.Matchers.iso8601DateTime(),
  // Don't test internal metrics or implementation details
}
```

### 2. Brittle Exact Matching

```javascript
// ❌ Bad: Brittle exact matching
body: {
  properties: [
    { id: 'prop-1', name: 'Hotel One' },
    { id: 'prop-2', name: 'Hotel Two' }
  ]
}

// ✅ Good: Flexible matching
body: {
  properties: Pact.Matchers.eachLike({
    id: Pact.Matchers.string(),
    name: Pact.Matchers.string()
  }, { min: 1 })
}
```

### 3. Testing UI Logic in Contracts

```javascript
// ❌ Bad: Testing UI logic
it('should display booking confirmation message', async () => {
  // Contract tests should not test UI rendering
  const response = await apiClient.createBooking(bookingData);
  expect(screen.getByText('Booking confirmed')).toBeInTheDocument();
});

// ✅ Good: Testing API contract only
it('should return confirmed booking with ID', async () => {
  const response = await apiClient.createBooking(bookingData);
  expect(response.booking.status).toBe('confirmed');
  expect(response.booking.id).toBeDefined();
});
```

### 4. Shared State Between Tests

```javascript
// ❌ Bad: Shared state between tests
let bookingId;

it('should create booking', async () => {
  const response = await apiClient.createBooking(bookingData);
  bookingId = response.booking.id; // Shared state
});

it('should get booking details', async () => {
  const response = await apiClient.getBooking(bookingId); // Depends on previous test
});

// ✅ Good: Independent tests
it('should create booking', async () => {
  const response = await apiClient.createBooking(bookingData);
  expect(response.booking.id).toBeDefined();
});

it('should get booking details', async () => {
  // Set up provider state for this specific test
  provider.addInteraction({
    state: 'booking booking-123 exists',
    // ...
  });
  const response = await apiClient.getBooking('booking-123');
});
```

## Summary Checklist

Before writing contract tests, ensure you:

- [ ] Understand what the consumer actually needs from the API
- [ ] Have realistic test data that represents production scenarios
- [ ] Use appropriate Pact matchers for flexible validation
- [ ] Test both success and error scenarios
- [ ] Follow consistent naming conventions
- [ ] Organize tests logically by service and functionality
- [ ] Use provider states to set up test scenarios
- [ ] Avoid testing implementation details or UI logic
- [ ] Manage resources properly (setup/teardown)
- [ ] Document complex interactions and decisions

Following these best practices will help you create maintainable, reliable contract tests that provide fast feedback and high confidence in your API integrations.

---

*These best practices are based on the Pact.js community guidelines and our team's experience with contract testing.*