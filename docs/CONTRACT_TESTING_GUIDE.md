# Contract Testing Guide

## Table of Contents

1. [Overview](#overview)
2. [Setup and Installation](#setup-and-installation)
3. [Contract Testing Methodology](#contract-testing-methodology)
4. [Writing Contract Tests](#writing-contract-tests)
5. [Best Practices](#best-practices)
6. [Troubleshooting](#troubleshooting)
7. [Performance Benefits](#performance-benefits)

## Overview

Contract testing is a methodology for ensuring that applications can communicate with each other by verifying that the messages they send or receive conform to a shared understanding documented in a contract. This guide covers the implementation of contract testing for the Qantas Hotels application using Pact.js.

### Why Contract Testing?

- **Faster Feedback**: Contract tests run in seconds vs minutes for E2E tests
- **Isolated Testing**: Test API contracts without UI dependencies
- **Better Error Isolation**: Quickly identify if issues are API-related or UI-related
- **Improved Reliability**: Reduced test flakiness compared to E2E tests
- **Consumer-Driven**: Front-end defines the contract expectations

### Contract Testing vs E2E Testing

| Aspect | Contract Tests | E2E Tests |
|--------|---------------|-----------|
| Execution Time | < 5 minutes | 15-30 minutes |
| Scope | API contracts only | Full user journey |
| Dependencies | Mock services | Real services |
| Flakiness | Low | High |
| Debugging | Easy to isolate | Complex |
| Maintenance | Low | High |

## Setup and Installation

### Prerequisites

- Node.js 16+ 
- npm or yarn
- Jest testing framework (already configured)

### Installation Steps

1. **Install Pact.js dependencies** (already completed):
   ```bash
   npm install --save-dev @pact-foundation/pact
   ```

2. **Verify installation**:
   ```bash
   npm test -- --testPathPattern=contracts
   ```

3. **Directory structure**:
   ```
   tests/
   ├── contracts/
   │   ├── hotels-api/
   │   ├── auth-api/
   │   ├── sanity-cms/
   │   └── payment-services/
   ├── helpers/
   └── config/
   ```

### Local Development Setup

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd qantas-hotels
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Run contract tests**:
   ```bash
   npm run test:contracts
   ```

4. **Generate contracts**:
   ```bash
   npm run contracts:generate
   ```

## Contract Testing Methodology

### Consumer-Driven Contract Testing

Our implementation follows the consumer-driven contract testing approach:

1. **Consumer (Front-end)** defines expected API interactions
2. **Contract** is generated from consumer tests
3. **Provider (Backend)** verifies it can fulfill the contract
4. **Continuous Verification** ensures ongoing compatibility

### Contract Lifecycle

```mermaid
graph LR
    A[Write Consumer Test] --> B[Generate Contract]
    B --> C[Share Contract]
    C --> D[Provider Verification]
    D --> E[Deploy if Compatible]
    E --> F[Monitor Compatibility]
    F --> A
```

### Test Categories

#### 1. Happy Path Tests
- Successful API interactions
- Valid request/response cycles
- Proper authentication flows

#### 2. Error Scenario Tests
- Invalid request handling
- Authentication failures
- Resource not found scenarios
- Server error responses

#### 3. Edge Case Tests
- Boundary value testing
- Empty response handling
- Large payload validation

## Writing Contract Tests

### Basic Contract Test Structure

```javascript
const { Pact } = require('@pact-foundation/pact');
const { ContractTestHelper } = require('../helpers/contract-test-helpers');

describe('Hotels API Contract Tests', () => {
  const provider = new Pact({
    consumer: 'qantas-hotels-frontend',
    provider: 'hotels-api',
    port: 1234,
    log: path.resolve(process.cwd(), 'logs', 'pact.log'),
    dir: path.resolve(process.cwd(), 'pacts'),
    logLevel: 'INFO'
  });

  beforeAll(() => provider.setup());
  afterAll(() => provider.finalize());
  afterEach(() => provider.verify());

  describe('GET /properties', () => {
    beforeEach(() => {
      return provider.addInteraction({
        state: 'properties exist',
        uponReceiving: 'a request for properties',
        withRequest: {
          method: 'GET',
          path: '/properties',
          headers: {
            'Accept': 'application/json',
            'Authorization': 'Bearer token123'
          }
        },
        willRespondWith: {
          status: 200,
          headers: {
            'Content-Type': 'application/json'
          },
          body: {
            properties: [
              {
                id: '12345',
                name: 'Test Hotel',
                location: {
                  city: 'Sydney',
                  country: 'Australia'
                }
              }
            ]
          }
        }
      });
    });

    it('should return properties list', async () => {
      const response = await apiClient.getProperties();
      expect(response.properties).toBeDefined();
      expect(response.properties[0].id).toBe('12345');
    });
  });
});
```

### Contract Test Patterns

#### 1. Authentication Pattern
```javascript
const authHeaders = {
  'Authorization': 'Bearer token123',
  'X-User-Id': 'user123'
};

// Use in all authenticated requests
withRequest: {
  method: 'GET',
  path: '/bookings',
  headers: authHeaders
}
```

#### 2. Error Response Pattern
```javascript
willRespondWith: {
  status: 400,
  headers: {
    'Content-Type': 'application/json'
  },
  body: {
    error: {
      code: 'INVALID_REQUEST',
      message: 'Missing required field: checkIn'
    }
  }
}
```

#### 3. Pagination Pattern
```javascript
body: {
  data: [...],
  pagination: {
    page: 1,
    totalPages: 5,
    totalResults: 50
  }
}
```

### Creating New Contract Tests

1. **Identify the API endpoint** to test
2. **Determine test scenarios** (happy path, errors, edge cases)
3. **Create test file** in appropriate directory
4. **Define provider state** if needed
5. **Write interaction expectations**
6. **Implement test assertions**
7. **Run and verify** the test

### Example: Adding a New Booking Contract Test

```javascript
// tests/contracts/hotels-api/bookings.contract.test.js
describe('POST /bookings', () => {
  beforeEach(() => {
    return provider.addInteraction({
      state: 'property is available',
      uponReceiving: 'a booking request',
      withRequest: {
        method: 'POST',
        path: '/bookings',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer token123'
        },
        body: {
          propertyId: '12345',
          checkIn: '2024-06-01',
          checkOut: '2024-06-03',
          guests: {
            adults: 2,
            children: 0
          }
        }
      },
      willRespondWith: {
        status: 201,
        headers: {
          'Content-Type': 'application/json'
        },
        body: {
          booking: {
            id: '67890',
            status: 'confirmed',
            propertyId: '12345'
          }
        }
      }
    });
  });

  it('should create a booking successfully', async () => {
    const bookingData = {
      propertyId: '12345',
      checkIn: '2024-06-01',
      checkOut: '2024-06-03',
      guests: { adults: 2, children: 0 }
    };

    const response = await apiClient.createBooking(bookingData);
    expect(response.booking.id).toBe('67890');
    expect(response.booking.status).toBe('confirmed');
  });
});
```

## Best Practices

### 1. Contract Design Principles

- **Minimal Contracts**: Only include fields your consumer actually uses
- **Realistic Data**: Use realistic test data that matches production patterns
- **Provider States**: Use provider states to set up test scenarios
- **Versioning**: Version your contracts to manage breaking changes

### 2. Test Organization

- **Group by Service**: Organize tests by API service (hotels-api, auth-api, etc.)
- **Descriptive Names**: Use clear, descriptive test and interaction names
- **Shared Helpers**: Create reusable helpers for common patterns
- **Mock Data**: Use consistent mock data generators

### 3. Data Management

```javascript
// Good: Use realistic but consistent test data
const mockProperty = {
  id: 'prop-12345',
  name: 'Sydney Harbour Hotel',
  location: {
    city: 'Sydney',
    country: 'Australia',
    coordinates: {
      latitude: -33.8688,
      longitude: 151.2093
    }
  }
};

// Avoid: Generic or unrealistic data
const mockProperty = {
  id: '1',
  name: 'Hotel',
  location: 'Place'
};
```

### 4. Error Handling

```javascript
// Test both success and error scenarios
describe('Authentication errors', () => {
  it('should handle invalid token', async () => {
    provider.addInteraction({
      uponReceiving: 'request with invalid token',
      withRequest: {
        headers: { 'Authorization': 'Bearer invalid-token' }
      },
      willRespondWith: {
        status: 401,
        body: {
          error: {
            code: 'INVALID_TOKEN',
            message: 'Token is invalid or expired'
          }
        }
      }
    });
  });
});
```

### 5. Performance Optimization

- **Parallel Execution**: Run contract tests in parallel
- **Selective Testing**: Use test patterns to run specific contract suites
- **Resource Management**: Clean up resources after tests
- **Caching**: Cache contract generation when possible

## Troubleshooting

### Common Issues and Solutions

#### 1. Contract Generation Fails

**Problem**: Contract files are not generated after test execution

**Solutions**:
- Verify Pact configuration is correct
- Check file permissions in the `pacts` directory
- Ensure `provider.verify()` is called in `afterEach`
- Check for syntax errors in interaction definitions

```javascript
// Ensure proper cleanup
afterEach(() => provider.verify());
afterAll(() => provider.finalize());
```

#### 2. Mock Server Not Starting

**Problem**: Pact mock server fails to start

**Solutions**:
- Check if port is already in use
- Verify Pact installation
- Check system resources and permissions
- Use different port numbers for parallel tests

```javascript
// Use dynamic port allocation
const provider = new Pact({
  port: 0, // Let Pact choose available port
  // ... other config
});
```

#### 3. Request/Response Mismatch

**Problem**: Actual API calls don't match contract expectations

**Solutions**:
- Enable detailed logging to see actual vs expected
- Check request headers, especially authentication
- Verify request body structure and data types
- Use Pact matchers for flexible matching

```javascript
// Enable detailed logging
const provider = new Pact({
  logLevel: 'DEBUG',
  log: path.resolve(process.cwd(), 'logs', 'pact-debug.log')
});

// Use matchers for flexible validation
body: {
  id: Pact.Matchers.uuid(),
  timestamp: Pact.Matchers.iso8601DateTime(),
  amount: Pact.Matchers.decimal()
}
```

#### 4. Authentication Issues

**Problem**: Authentication-related contract tests fail

**Solutions**:
- Verify token format and structure
- Check authentication headers are included
- Use provider states to set up authentication context
- Mock authentication responses appropriately

```javascript
// Set up authentication provider state
beforeEach(() => {
  return provider.addInteraction({
    state: 'user is authenticated',
    // ... rest of interaction
  });
});
```

#### 5. Schema Validation Errors

**Problem**: Response schema doesn't match expectations

**Solutions**:
- Use JSON schema validation tools
- Check for optional vs required fields
- Verify data types match exactly
- Use Pact matchers for type validation

```javascript
// Use type matchers instead of exact values
body: {
  properties: Pact.Matchers.eachLike({
    id: Pact.Matchers.string(),
    name: Pact.Matchers.string(),
    rating: Pact.Matchers.integer()
  })
}
```

### Debugging Tips

1. **Enable Verbose Logging**:
   ```javascript
   const provider = new Pact({
     logLevel: 'DEBUG'
   });
   ```

2. **Check Generated Contracts**:
   - Review generated `.json` files in `pacts/` directory
   - Verify interactions are captured correctly

3. **Use Test Isolation**:
   ```bash
   # Run specific contract test
   npm test -- --testPathPattern="hotels-api/bookings"
   ```

4. **Validate API Responses**:
   ```javascript
   // Add response validation
   const response = await apiClient.getProperties();
   console.log('Actual response:', JSON.stringify(response, null, 2));
   ```

### Getting Help

- **Documentation**: Refer to [Pact.js documentation](https://docs.pact.io/implementation_guides/javascript)
- **Team Support**: Contact the platform team for contract testing questions
- **Debugging**: Use the troubleshooting checklist above
- **Community**: Check Pact community forums for common issues

## Performance Benefits

### Execution Time Comparison

| Test Type | Execution Time | Reliability | Maintenance |
|-----------|---------------|-------------|-------------|
| Contract Tests | 2-5 minutes | 99%+ | Low |
| E2E Tests | 15-30 minutes | 85-90% | High |

### Key Performance Metrics

- **50-80% faster** execution compared to equivalent E2E tests
- **99%+ reliability** with minimal flakiness
- **Reduced CI/CD pipeline time** by 10-15 minutes per build
- **Faster feedback loops** for developers

### ROI Benefits

1. **Developer Productivity**: Faster feedback enables quicker iterations
2. **Reduced Debugging Time**: Easier to isolate API vs UI issues
3. **Lower Maintenance Cost**: More stable tests require less maintenance
4. **Improved Confidence**: Better test coverage of API contracts
5. **Faster Deployments**: Quicker validation enables faster releases

---

*This guide is maintained by the Qantas Hotels development team. For updates or questions, please contact the platform team.*