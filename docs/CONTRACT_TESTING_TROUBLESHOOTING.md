# Contract Testing Troubleshooting Guide

## Quick Diagnosis

### Is Your Issue Here?

| Symptom | Quick Fix | Section |
|---------|-----------|---------|
| Tests not found | Check file patterns | [Test Discovery](#test-discovery-issues) |
| Port already in use | Use dynamic ports | [Port Conflicts](#port-conflicts) |
| Contract not generated | Check provider.verify() | [Contract Generation](#contract-generation-issues) |
| Authentication fails | Verify token format | [Authentication Issues](#authentication-issues) |
| Schema mismatch | Use Pact matchers | [Schema Validation](#schema-validation-errors) |
| Tests are slow | Enable parallel execution | [Performance Issues](#performance-issues) |

## Test Discovery Issues

### Problem: "No tests found"

**Symptoms:**
```bash
$ npm run test:contracts
No tests found, exiting with code 1
```

**Diagnosis:**
```bash
# Check if test files exist
find tests/ -name "*.contract.test.js"

# Verify Jest config
cat jest/jest.config.contracts.ts

# Check test patterns
npm test -- --listTests --testPathPattern=contracts
```

**Solutions:**

1. **Fix file naming**:
   ```bash
   # Ensure files follow naming convention
   mv tests/contracts/hotels.test.js tests/contracts/hotels.contract.test.js
   ```

2. **Update Jest configuration**:
   ```javascript
   // jest/jest.config.contracts.ts
   module.exports = {
     testMatch: [
       '**/tests/contracts/**/*.contract.test.js',
       '**/tests/contracts/**/*.test.js'
     ]
   };
   ```

3. **Run with explicit pattern**:
   ```bash
   npm test -- "tests/contracts/**/*.test.js"
   ```

### Problem: Tests found but not executing

**Symptoms:**
- Tests are listed but don't run
- Hanging test execution

**Solutions:**

1. **Check for syntax errors**:
   ```bash
   # Validate JavaScript syntax
   node -c tests/contracts/hotels-api/bookings.contract.test.js
   ```

2. **Enable verbose output**:
   ```bash
   npm test -- --testPathPattern=contracts --verbose
   ```

3. **Run single test file**:
   ```bash
   npm test -- tests/contracts/hotels-api/bookings.contract.test.js
   ```

## Port Conflicts

### Problem: "Port already in use"

**Symptoms:**
```
Error: listen EADDRINUSE: address already in use :::1234
```

**Diagnosis:**
```bash
# Check what's using the port
lsof -i :1234
netstat -tulpn | grep 1234
```

**Solutions:**

1. **Use dynamic port allocation**:
   ```javascript
   const provider = new Pact({
     port: 0, // Let Pact choose available port
     // ... other config
   });
   ```

2. **Kill processes using the port**:
   ```bash
   # Kill specific process
   kill -9 $(lsof -t -i:1234)
   
   # Or kill all node processes (be careful!)
   pkill -f node
   ```

3. **Use different ports for parallel tests**:
   ```javascript
   // Use process ID to generate unique ports
   const provider = new Pact({
     port: 1234 + process.pid % 1000,
     // ... other config
   });
   ```

### Problem: Multiple Pact instances conflict

**Symptoms:**
- Random port conflicts in parallel tests
- Intermittent test failures

**Solutions:**

1. **Proper test isolation**:
   ```javascript
   describe('Hotels API Tests', () => {
     let provider;
   
     beforeAll(async () => {
       provider = new Pact({
         port: 0, // Dynamic port
         consumer: `qantas-hotels-${Date.now()}`, // Unique consumer name
         provider: 'hotels-api'
       });
       await provider.setup();
     });
   
     afterAll(async () => {
       await provider.finalize();
     });
   });
   ```

2. **Sequential test execution**:
   ```bash
   # Run tests sequentially if parallel execution causes issues
   npm test -- --testPathPattern=contracts --runInBand
   ```

## Contract Generation Issues

### Problem: No contract files generated

**Symptoms:**
- Tests pass but no `.json` files in `pacts/` directory
- Empty pacts directory after test execution

**Diagnosis:**
```bash
# Check if pacts directory exists and is writable
ls -la pacts/
mkdir -p pacts && chmod 755 pacts

# Check test execution logs
npm test -- --testPathPattern=contracts --verbose
```

**Solutions:**

1. **Ensure provider.verify() is called**:
   ```javascript
   describe('API Contract Tests', () => {
     afterEach(async () => {
       await provider.verify(); // This generates the contract
     });
   
     afterAll(async () => {
       await provider.finalize(); // This writes the contract file
     });
   });
   ```

2. **Check Pact configuration**:
   ```javascript
   const provider = new Pact({
     consumer: 'qantas-hotels-frontend',
     provider: 'hotels-api',
     port: 1234,
     dir: path.resolve(process.cwd(), 'pacts'), // Ensure this path exists
     pactfileWriteMode: 'overwrite' // or 'update'
   });
   ```

3. **Verify test actually runs interactions**:
   ```javascript
   it('should generate contract', async () => {
     // Make sure you actually call the API client
     const response = await apiClient.getProperties();
     expect(response).toBeDefined();
   });
   ```

### Problem: Contract files are empty or malformed

**Symptoms:**
- Contract files exist but are empty `{}`
- Invalid JSON in contract files

**Solutions:**

1. **Check for test failures**:
   ```bash
   # Run tests with bail to stop on first failure
   npm test -- --testPathPattern=contracts --bail
   ```

2. **Validate contract JSON**:
   ```bash
   # Check if generated contracts are valid JSON
   jq empty pacts/*.json && echo "Valid JSON" || echo "Invalid JSON"
   ```

3. **Enable debug logging**:
   ```javascript
   const provider = new Pact({
     logLevel: 'DEBUG',
     log: path.resolve(process.cwd(), 'logs', 'pact-debug.log')
   });
   ```

## Authentication Issues

### Problem: Authentication token validation fails

**Symptoms:**
```
Expected status code 200 but got 401
Authentication failed in contract test
```

**Diagnosis:**
```bash
# Check token format in test
grep -r "Authorization" tests/contracts/

# Verify token structure
echo "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." | base64 -d
```

**Solutions:**

1. **Use realistic token format**:
   ```javascript
   // ✅ Good: Realistic JWT token structure
   const authHeaders = {
     'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c',
     'X-User-Id': 'user-12345'
   };
   
   // ❌ Bad: Generic token
   const authHeaders = {
     'Authorization': 'Bearer token123'
   };
   ```

2. **Mock authentication provider state**:
   ```javascript
   provider.addInteraction({
     state: 'user is authenticated with valid token',
     uponReceiving: 'authenticated request',
     withRequest: {
       headers: {
         'Authorization': Pact.Matchers.term({
           matcher: 'Bearer [A-Za-z0-9\\-_]+\\.[A-Za-z0-9\\-_]+\\.[A-Za-z0-9\\-_]+',
           generate: 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
         })
       }
     }
   });
   ```

3. **Test authentication separately**:
   ```javascript
   describe('Authentication', () => {
     it('should validate authentication token', async () => {
       provider.addInteraction({
         state: 'token validation service is available',
         uponReceiving: 'token validation request',
         withRequest: {
           method: 'POST',
           path: '/auth/validate',
           body: {
             token: Pact.Matchers.string()
           }
         },
         willRespondWith: {
           status: 200,
           body: {
             valid: true,
             userId: Pact.Matchers.string()
           }
         }
       });
   
       const result = await authClient.validateToken('test-token');
       expect(result.valid).toBe(true);
     });
   });
   ```

### Problem: QFF member authentication fails

**Symptoms:**
- Member-specific endpoints return 403
- QFF tier validation fails

**Solutions:**

1. **Include member-specific headers**:
   ```javascript
   const qffMemberHeaders = {
     'Authorization': 'Bearer qff-member-token',
     'X-Member-Id': 'QFF123456789',
     'X-Member-Tier': 'Gold',
     'X-Points-Balance': '50000'
   };
   ```

2. **Set up member provider states**:
   ```javascript
   provider.addInteraction({
     state: 'user QFF123456789 is Gold tier member with 50000 points',
     uponReceiving: 'member exclusive offers request',
     // ... rest of interaction
   });
   ```

## Schema Validation Errors

### Problem: Response schema doesn't match expectations

**Symptoms:**
```
Expected property 'id' to be string but got number
Schema validation failed for response body
```

**Diagnosis:**
```bash
# Check actual API response structure
curl -H "Authorization: Bearer token" https://api.example.com/properties | jq '.'

# Compare with contract expectations
cat pacts/*.json | jq '.interactions[].response.body'
```

**Solutions:**

1. **Use flexible Pact matchers**:
   ```javascript
   // ✅ Good: Flexible type matching
   body: {
     properties: Pact.Matchers.eachLike({
       id: Pact.Matchers.string(),
       name: Pact.Matchers.string(),
       rating: Pact.Matchers.integer(),
       price: Pact.Matchers.decimal()
     })
   }
   
   // ❌ Bad: Exact value matching
   body: {
     properties: [
       {
         id: 'prop-123',
         name: 'Exact Hotel Name',
         rating: 4,
         price: 250.00
       }
     ]
   }
   ```

2. **Handle optional fields**:
   ```javascript
   body: {
     property: {
       id: Pact.Matchers.string(),
       name: Pact.Matchers.string(),
       description: Pact.Matchers.string(), // Always present
       amenities: Pact.Matchers.eachLike(   // Optional array
         Pact.Matchers.string(),
         { min: 0 }
       )
     }
   }
   ```

3. **Use term matchers for enums**:
   ```javascript
   body: {
     booking: {
       status: Pact.Matchers.term({
         matcher: 'confirmed|pending|cancelled',
         generate: 'confirmed'
       }),
       paymentMethod: Pact.Matchers.term({
         matcher: 'credit_card|points|bank_transfer',
         generate: 'credit_card'
       })
     }
   }
   ```

### Problem: Date/time format validation fails

**Symptoms:**
```
Expected ISO 8601 date format but got '2024-01-15 10:30:00'
Date validation failed in contract test
```

**Solutions:**

1. **Use ISO 8601 matchers**:
   ```javascript
   body: {
     booking: {
       checkIn: Pact.Matchers.iso8601Date(),      // 2024-01-15
       checkOut: Pact.Matchers.iso8601Date(),     // 2024-01-18
       createdAt: Pact.Matchers.iso8601DateTime(), // 2024-01-15T10:30:00Z
       updatedAt: Pact.Matchers.iso8601DateTime()
     }
   }
   ```

2. **Custom date format matchers**:
   ```javascript
   body: {
     booking: {
       checkIn: Pact.Matchers.term({
         matcher: '\\d{4}-\\d{2}-\\d{2}',
         generate: '2024-06-15'
       })
     }
   }
   ```

## Performance Issues

### Problem: Contract tests are slow

**Symptoms:**
- Tests take longer than 5 minutes
- Timeout errors in CI/CD

**Diagnosis:**
```bash
# Run tests with timing information
npm test -- --testPathPattern=contracts --verbose --detectOpenHandles

# Check for hanging processes
ps aux | grep pact
```

**Solutions:**

1. **Enable parallel execution**:
   ```javascript
   // jest.config.contracts.js
   module.exports = {
     maxWorkers: 4,
     testTimeout: 30000
   };
   ```

2. **Optimize provider setup**:
   ```javascript
   describe('API Tests', () => {
     let provider;
   
     beforeAll(async () => {
       provider = new Pact({
         logLevel: 'ERROR', // Reduce logging overhead
         port: 0,           // Dynamic port allocation
         timeout: 10000     // Shorter timeout
       });
       await provider.setup();
     });
   
     // Use beforeEach only when necessary
     beforeEach(() => {
       // Only add interactions that change between tests
     });
   });
   ```

3. **Reduce test scope**:
   ```bash
   # Run specific test suites
   npm test -- --testPathPattern="hotels-api/bookings"
   
   # Skip slow tests in development
   npm test -- --testPathPattern=contracts --testNamePattern="^(?!.*slow)"
   ```

### Problem: Memory leaks in contract tests

**Symptoms:**
- Increasing memory usage during test execution
- Out of memory errors in CI/CD

**Solutions:**

1. **Proper cleanup**:
   ```javascript
   describe('API Tests', () => {
     let provider;
   
     afterEach(async () => {
       await provider.verify();
       // Clear any cached data
       jest.clearAllMocks();
     });
   
     afterAll(async () => {
       await provider.finalize();
       provider = null;
     });
   });
   ```

2. **Monitor memory usage**:
   ```bash
   # Run tests with memory monitoring
   node --max-old-space-size=4096 node_modules/.bin/jest --testPathPattern=contracts
   ```

## CI/CD Integration Issues

### Problem: Contract tests fail in CI but pass locally

**Symptoms:**
- Tests pass on developer machines
- Fail consistently in CI/CD pipeline

**Diagnosis:**
```bash
# Check CI environment differences
echo $NODE_VERSION
echo $CI
env | grep -i pact

# Compare package versions
npm list @pact-foundation/pact
```

**Solutions:**

1. **Environment consistency**:
   ```yaml
   # .buildkite/pipeline.yml
   steps:
     - label: "Contract Tests"
       command: |
         export NODE_ENV=test
         export PACT_LOG_LEVEL=ERROR
         npm ci
         npm run test:contracts
       timeout_in_minutes: 10
   ```

2. **Handle CI-specific issues**:
   ```javascript
   // tests/config/pact-config.js
   const isCI = process.env.CI === 'true';
   
   const pactConfig = {
     logLevel: isCI ? 'ERROR' : 'INFO',
     timeout: isCI ? 60000 : 30000,
     port: isCI ? 0 : 1234 // Always use dynamic ports in CI
   };
   ```

3. **Retry flaky tests**:
   ```javascript
   // jest.config.contracts.js
   module.exports = {
     testRetries: process.env.CI ? 2 : 0
   };
   ```

### Problem: Contract publishing fails

**Symptoms:**
```
Failed to publish contracts to broker
Network timeout when publishing contracts
```

**Solutions:**

1. **Check broker configuration**:
   ```bash
   # Test broker connectivity
   curl -v $PACT_BROKER_URL/health
   
   # Verify credentials
   echo $PACT_BROKER_USERNAME
   echo $PACT_BROKER_PASSWORD
   ```

2. **Publish with retry logic**:
   ```bash
   # scripts/publish-contracts.sh
   #!/bin/bash
   for i in {1..3}; do
     if pact-broker publish pacts --consumer-app-version $BUILD_NUMBER; then
       break
     fi
     echo "Publish attempt $i failed, retrying..."
     sleep 5
   done
   ```

## Debugging Techniques

### Enable Detailed Logging

```javascript
const provider = new Pact({
  logLevel: 'DEBUG',
  log: path.resolve(process.cwd(), 'logs', 'pact-debug.log')
});
```

### Inspect Generated Contracts

```bash
# Pretty print contract JSON
cat pacts/*.json | jq '.'

# Check specific interactions
jq '.interactions[] | {description, request: .request.path, status: .response.status}' pacts/*.json

# Validate contract structure
jq '.consumer.name, .provider.name, (.interactions | length)' pacts/*.json
```

### Test Individual Components

```javascript
// Test API client separately
describe('API Client Unit Tests', () => {
  it('should format request correctly', () => {
    const client = new HotelsApiClient();
    const request = client.buildBookingRequest({
      propertyId: 'prop-123',
      checkIn: '2024-06-15'
    });
    
    expect(request.method).toBe('POST');
    expect(request.path).toBe('/bookings');
    expect(request.body.propertyId).toBe('prop-123');
  });
});
```

### Monitor Test Execution

```bash
# Run with detailed output
npm test -- --testPathPattern=contracts --verbose --detectOpenHandles

# Check for hanging processes
ps aux | grep -E "(pact|node)" | grep -v grep

# Monitor resource usage
top -p $(pgrep -f "jest.*contracts")
```

## Getting Help

### Before Asking for Help

1. **Check this troubleshooting guide**
2. **Search existing issues** in the team's knowledge base
3. **Reproduce the issue** with minimal test case
4. **Gather diagnostic information**:
   ```bash
   # System information
   node --version
   npm --version
   npm list @pact-foundation/pact
   
   # Test execution logs
   npm test -- --testPathPattern=contracts --verbose > test-output.log 2>&1
   
   # Generated contracts
   ls -la pacts/
   cat pacts/*.json | jq '.'
   ```

### Where to Get Help

1. **Team Documentation**: Check internal wiki and documentation
2. **Pact Community**: [Pact.js Documentation](https://docs.pact.io/implementation_guides/javascript)
3. **Platform Team**: Contact for infrastructure-related issues
4. **Code Review**: Ask team members to review contract test implementation

### Creating Good Bug Reports

Include this information when reporting issues:

```markdown
## Issue Description
Brief description of the problem

## Environment
- Node.js version: 
- npm version: 
- @pact-foundation/pact version: 
- Operating System: 
- CI/CD environment: 

## Steps to Reproduce
1. Step one
2. Step two
3. Step three

## Expected Behavior
What should happen

## Actual Behavior
What actually happens

## Logs and Output
```
[Include relevant logs here]
```

## Additional Context
Any other relevant information
```

---

*This troubleshooting guide is maintained by the development team. If you find a solution to an issue not covered here, please contribute by updating this document.*