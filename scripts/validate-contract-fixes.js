#!/usr/bin/env node
/* eslint-disable no-console, no-unused-vars */

/**
 * Validation script to check if the Pact contract test fixes are working correctly
 */

const fs = require('fs');
const { spawn } = require('child_process');

/**
 * Check if required files exist
 */
function checkRequiredFiles() {
  console.log('🔍 Checking required files...');

  const requiredFiles = [
    'tests/contracts/helpers/ContractTestBase.js',
    'tests/contracts/helpers/testRunner.js',
    'tests/contracts/setup/contractTestSetup.js',
    'jest/jest.config.contracts.ts',
  ];

  const missingFiles = requiredFiles.filter((file) => !fs.existsSync(file));

  if (missingFiles.length > 0) {
    console.log('❌ Missing required files:');
    missingFiles.forEach((file) => console.log(`  - ${file}`));
    return false;
  }

  console.log('✅ All required files present');
  return true;
}

/**
 * Check if the ContractTestBase has the new port management
 */
function checkPortManagement() {
  console.log('🔍 Checking port management implementation...');

  const contractTestBase = fs.readFileSync('tests/contracts/helpers/ContractTestBase.js', 'utf8');

  const hasPortManager = contractTestBase.includes('class PortManager');
  const hasDynamicPort = contractTestBase.includes('getAvailablePort()');
  const hasPortRelease = contractTestBase.includes('releasePort(');

  if (!hasPortManager || !hasDynamicPort || !hasPortRelease) {
    console.log('❌ Port management not properly implemented');
    return false;
  }

  console.log('✅ Port management implementation found');
  return true;
}

/**
 * Check if Jest configuration has concurrency limits
 */
function checkJestConfig() {
  console.log('🔍 Checking Jest configuration...');

  const jestConfig = fs.readFileSync('jest/jest.config.contracts.ts', 'utf8');

  const hasMaxWorkers = jestConfig.includes('maxWorkers');
  const hasMaxConcurrency = jestConfig.includes('maxConcurrency');
  const hasTimeout = jestConfig.includes('testTimeout: 60000');

  if (!hasMaxWorkers || !hasMaxConcurrency || !hasTimeout) {
    console.log('❌ Jest configuration missing concurrency controls');
    return false;
  }

  console.log('✅ Jest configuration properly updated');
  return true;
}

/**
 * Check if the migrated test file uses the new pattern
 */
function checkMigratedTest() {
  console.log('🔍 Checking migrated test file...');

  const testFile = 'tests/contracts/hotels-api/bookings.contract.test.js';

  if (!fs.existsSync(testFile)) {
    console.log('⚠️  Test file not found, skipping check');
    return true;
  }

  const testContent = fs.readFileSync(testFile, 'utf8');

  const hasTestRunner = testContent.includes('createContractTestSuite');
  const hasMockServerGetter = testContent.includes('createMockServerUrlGetter');
  const hasAddInteractionSafely = testContent.includes('addInteractionSafely');
  const noHardcodedPort = !testContent.includes('port: 1234');

  if (!hasTestRunner || !hasMockServerGetter || !hasAddInteractionSafely || !noHardcodedPort) {
    console.log('❌ Test file not properly migrated');
    return false;
  }

  console.log('✅ Test file properly migrated');
  return true;
}

/**
 * Run a quick test to ensure the system works
 */
function runQuickTest() {
  return new Promise((resolve) => {
    console.log('🧪 Running quick validation test...');

    const jest = spawn('node', ['scripts/run-contract-tests.js', '--testNamePattern=should create a booking successfully', '--bail'], {
      stdio: 'pipe',
      env: {
        ...process.env,
        NODE_ENV: 'test',
      },
    });

    let output = '';
    jest.stdout.on('data', (data) => {
      output += data.toString();
    });

    jest.stderr.on('data', (data) => {
      output += data.toString();
    });

    jest.on('close', (code) => {
      if (code === 0) {
        console.log('✅ Quick test passed');
        resolve(true);
      } else {
        console.log('❌ Quick test failed');
        console.log('Output:', output);
        resolve(false);
      }
    });

    jest.on('error', (error) => {
      console.log('❌ Failed to run quick test:', error.message);
      resolve(false);
    });

    // Timeout after 30 seconds
    setTimeout(() => {
      jest.kill('SIGTERM');
      console.log('⏰ Quick test timed out');
      resolve(false);
    }, 30000);
  });
}

/**
 * Main validation function
 */
async function main() {
  console.log('🚀 Validating Pact contract test fixes...\n');

  const checks = [
    { name: 'Required Files', fn: checkRequiredFiles },
    { name: 'Port Management', fn: checkPortManagement },
    { name: 'Jest Configuration', fn: checkJestConfig },
    { name: 'Migrated Test', fn: checkMigratedTest },
  ];

  let allPassed = true;

  for (const check of checks) {
    const passed = check.fn();
    if (!passed) {
      allPassed = false;
    }
    console.log('');
  }

  if (allPassed) {
    console.log('🎉 All validation checks passed!');
    console.log('\n📋 Summary of fixes:');
    console.log('✅ Dynamic port allocation to prevent conflicts');
    console.log('✅ Proper resource cleanup and management');
    console.log('✅ Concurrency controls in Jest configuration');
    console.log('✅ Enhanced error handling and process cleanup');
    console.log('✅ Test isolation and state management');

    console.log('\n🚀 You can now run contract tests safely:');
    console.log('  npm run test:contracts');
    console.log('  npm run test:contracts:watch');
    console.log('  npm run test:contracts:debug');
  } else {
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { checkRequiredFiles, checkPortManagement, checkJestConfig, checkMigratedTest };
