#!/usr/bin/env node
/* eslint-disable no-console, no-template-curly-in-string */

/**
 * <PERSON><PERSON><PERSON> to migrate existing Pact contract tests to use the new concurrency-safe pattern
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

const CONTRACTS_DIR = 'tests/contracts';

/**
 * Migration patterns to apply to contract test files
 */
const MIGRATION_PATTERNS = [
  // Replace hardcoded Pact setup with new test runner
  {
    pattern: /const { Pact, Matchers } = require\('@pact-foundation\/pact'\);/g,
    replacement: `const { Matchers } = require('@pact-foundation/pact');
const { createContractTestSuite, createMockServerUrlGetter, addInteractionSafely } = require('../helpers/testRunner');`,
  },

  // Replace describe block with createContractTestSuite
  {
    pattern: /describe\('([^']+)', \(\) => \{[\s\S]*?let provider;[\s\S]*?const mockServerBaseUrl = '[^']+';/g,
    replacement: (match, testName) => {
      const providerName = extractProviderName(match);
      return `createContractTestSuite('${testName}', '${providerName}', (contractTest) => {
  const getMockServerUrl = createMockServerUrlGetter(contractTest);`;
    },
  },

  // Replace beforeAll setup
  {
    pattern: /beforeAll\(\(\) => \{[\s\S]*?return provider\.setup\(\);\s*\}\);/g,
    replacement: '',
  },

  // Replace afterAll teardown
  {
    pattern: /afterAll\(\(\) => \{[\s\S]*?return provider\.finalize\(\);\s*\}\);/g,
    replacement: '',
  },

  // Replace afterEach verify
  {
    pattern: /afterEach\(\(\) => \{[\s\S]*?return provider\.verify\(\);\s*\}\);/g,
    replacement: '',
  },

  // Replace provider.addInteraction with addInteractionSafely
  {
    pattern: /return provider\.addInteraction\(/g,
    replacement: 'await addInteractionSafely(contractTest, ',
  },

  // Replace beforeEach with async
  {
    pattern: /beforeEach\(\(\) => \{/g,
    replacement: 'beforeEach(async () => {',
  },

  // Replace mockServerBaseUrl with getMockServerUrl()
  {
    pattern: /\$\{mockServerBaseUrl\}/g,
    replacement: '${getMockServerUrl()}',
  },
];

/**
 * Extract provider name from the old Pact setup
 */
function extractProviderName(setupCode) {
  const match = setupCode.match(/provider:\s*['"]([^'"]+)['"]/);
  return match ? match[1] : 'unknown-provider';
}

/**
 * Apply migration patterns to a file
 */
function migrateFile(filePath) {
  console.log(`Migrating ${filePath}...`);

  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  MIGRATION_PATTERNS.forEach(({ pattern, replacement }) => {
    const originalContent = content;

    if (typeof replacement === 'function') {
      content = content.replace(pattern, replacement);
    } else {
      content = content.replace(pattern, replacement);
    }

    if (content !== originalContent) {
      modified = true;
    }
  });

  if (modified) {
    // Create backup
    const backupPath = `${filePath}.backup`;
    fs.writeFileSync(backupPath, fs.readFileSync(filePath));

    // Write migrated content
    fs.writeFileSync(filePath, content);
    console.log(`✅ Migrated ${filePath} (backup created at ${backupPath})`);
  } else {
    console.log(`⏭️  No changes needed for ${filePath}`);
  }
}

/**
 * Find all contract test files
 */
function findContractTestFiles() {
  const pattern = path.join(CONTRACTS_DIR, '**/*.contract.test.js');
  return glob.sync(pattern);
}

/**
 * Main migration function
 */
function main() {
  console.log('🚀 Starting Pact contract test migration...\n');

  const testFiles = findContractTestFiles();

  if (testFiles.length === 0) {
    console.log('No contract test files found.');
    return;
  }

  console.log(`Found ${testFiles.length} contract test files:\n`);
  testFiles.forEach((file) => console.log(`  - ${file}`));
  console.log('');

  testFiles.forEach(migrateFile);

  console.log('\n✨ Migration completed!');
  console.log('\n📝 Next steps:');
  console.log('1. Review the migrated files for any manual adjustments needed');
  console.log('2. Run the contract tests to ensure they work correctly');
  console.log("3. Remove backup files once you're satisfied with the migration");
}

if (require.main === module) {
  main();
}

module.exports = { migrateFile, findContractTestFiles };
