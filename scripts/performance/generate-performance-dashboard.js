#!/usr/bin/env node

/**
 * Performance Dashboard Generator
 *
 * This script generates a comprehensive performance dashboard that combines
 * benchmark results, reliability tracking, and ROI analysis.
 */

const fs = require('fs');
const path = require('path');

class PerformanceDashboard {
  constructor() {
    this.reportsDir = path.join(process.cwd(), 'reports');
    this.performanceDir = path.join(this.reportsDir, 'performance');
    this.reliabilityDir = path.join(this.reportsDir, 'reliability');
    this.dashboardDir = path.join(this.reportsDir, 'dashboard');
    this.ensureDirectories();
  }

  ensureDirectories() {
    [this.reportsDir, this.performanceDir, this.reliabilityDir, this.dashboardDir].forEach((dir) => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
  }

  async generateDashboard() {
    console.log('📊 Generating Performance Dashboard');
    console.log('===================================');

    try {
      // Load data from various sources
      const performanceData = this.loadPerformanceData();
      const reliabilityData = this.loadReliabilityData();
      const historicalData = this.loadHistoricalData();

      // Generate dashboard data
      const dashboardData = {
        timestamp: new Date().toISOString(),
        performance: performanceData,
        reliability: reliabilityData,
        historical: historicalData,
        summary: this.generateSummary(performanceData, reliabilityData, historicalData),
        roi: this.calculateROI(performanceData, reliabilityData),
      };

      // Generate HTML dashboard
      const htmlContent = this.generateDashboardHTML(dashboardData);
      const dashboardPath = path.join(this.dashboardDir, 'performance-dashboard.html');
      fs.writeFileSync(dashboardPath, htmlContent);

      // Generate JSON data for API consumption
      const jsonPath = path.join(this.dashboardDir, 'dashboard-data.json');
      fs.writeFileSync(jsonPath, JSON.stringify(dashboardData, null, 2));

      // Generate CSV export for analysis
      this.generateCSVExport(dashboardData);

      console.log(`✅ Dashboard generated: ${dashboardPath}`);
      console.log(`📊 Data export: ${jsonPath}`);

      return dashboardData;
    } catch (error) {
      console.error('❌ Dashboard generation failed:', error.message);
      throw error;
    }
  }

  loadPerformanceData() {
    const latestPath = path.join(this.performanceDir, 'latest.json');

    if (fs.existsSync(latestPath)) {
      try {
        return JSON.parse(fs.readFileSync(latestPath, 'utf8'));
      } catch (error) {
        console.warn('Failed to load performance data:', error.message);
      }
    }

    return null;
  }

  loadReliabilityData() {
    const reliabilityPath = path.join(this.reliabilityDir, 'reliability-data.json');

    if (fs.existsSync(reliabilityPath)) {
      try {
        return JSON.parse(fs.readFileSync(reliabilityPath, 'utf8'));
      } catch (error) {
        console.warn('Failed to load reliability data:', error.message);
      }
    }

    return null;
  }

  loadHistoricalData() {
    // Load historical performance data from multiple benchmark runs
    const historicalData = {
      benchmarks: [],
      trends: {},
    };

    try {
      const files = fs
        .readdirSync(this.performanceDir)
        .filter((file) => file.startsWith('performance-benchmark-') && file.endsWith('.json'))
        .sort()
        .slice(-30); // Last 30 benchmark runs

      files.forEach((file) => {
        try {
          const data = JSON.parse(fs.readFileSync(path.join(this.performanceDir, file), 'utf8'));
          historicalData.benchmarks.push({
            timestamp: data.timestamp,
            contractDuration: data.contractTests?.avgDuration || 0,
            contractReliability: data.contractTests?.reliability || 0,
            e2eDuration: data.e2eTests?.avgDuration || 0,
            e2eReliability: data.e2eTests?.reliability || 0,
            speedImprovement: data.comparison?.speedImprovement?.percentage || 0,
          });
        } catch (error) {
          console.warn(`Failed to load historical data from ${file}:`, error.message);
        }
      });

      // Calculate trends
      if (historicalData.benchmarks.length > 1) {
        historicalData.trends = this.calculateTrends(historicalData.benchmarks);
      }
    } catch (error) {
      console.warn('Failed to load historical data:', error.message);
    }

    return historicalData;
  }

  calculateTrends(benchmarks) {
    if (benchmarks.length < 2) return {};

    const recent = benchmarks.slice(-5); // Last 5 runs
    const older = benchmarks.slice(-10, -5); // Previous 5 runs

    const calculateAverage = (data, field) => {
      const values = data.map((d) => d[field]).filter((v) => v > 0);
      return values.length > 0 ? values.reduce((a, b) => a + b, 0) / values.length : 0;
    };

    const recentAvgDuration = calculateAverage(recent, 'contractDuration');
    const olderAvgDuration = calculateAverage(older, 'contractDuration');
    const recentAvgReliability = calculateAverage(recent, 'contractReliability');
    const olderAvgReliability = calculateAverage(older, 'contractReliability');

    return {
      durationTrend: olderAvgDuration > 0 ? ((recentAvgDuration - olderAvgDuration) / olderAvgDuration) * 100 : 0,
      reliabilityTrend: olderAvgReliability > 0 ? recentAvgReliability - olderAvgReliability : 0,
      improving: recentAvgDuration < olderAvgDuration && recentAvgReliability >= olderAvgReliability,
    };
  }

  generateSummary(performanceData, reliabilityData, historicalData) {
    const summary = {
      status: 'unknown',
      highlights: [],
      concerns: [],
      metrics: {},
    };

    // Performance metrics
    if (performanceData?.contractTests) {
      const contract = performanceData.contractTests;
      summary.metrics.contractDuration = contract.avgDuration;
      summary.metrics.contractReliability = contract.reliability;

      if (contract.reliability >= 95) {
        summary.highlights.push('Contract tests show excellent reliability (≥95%)');
      } else if (contract.reliability < 85) {
        summary.concerns.push('Contract test reliability is below 85%');
      }

      if (contract.avgDuration < 300000) {
        // 5 minutes
        summary.highlights.push('Contract tests complete in under 5 minutes');
      } else {
        summary.concerns.push('Contract tests are taking longer than 5 minutes');
      }
    }

    // Comparison with E2E tests
    if (performanceData?.comparison?.available) {
      const comparison = performanceData.comparison;
      summary.metrics.speedImprovement = comparison.speedImprovement.percentage;
      summary.metrics.timeSavings = comparison.timeSavings.weeklyHours;

      if (comparison.speedImprovement.percentage > 50) {
        summary.highlights.push(`Contract tests are ${comparison.speedImprovement.percentage.toFixed(1)}% faster than E2E tests`);
      }

      if (comparison.timeSavings.weeklyHours > 5) {
        summary.highlights.push(`Saving ${comparison.timeSavings.weeklyHours.toFixed(1)} hours per week`);
      }
    }

    // Reliability trends
    if (reliabilityData?.summary) {
      const reliability = reliabilityData.summary;
      if (reliability.trend > 0) {
        summary.highlights.push('Test reliability is improving over time');
      } else if (reliability.trend < -5) {
        summary.concerns.push('Test reliability is declining');
      }
    }

    // Historical trends
    if (historicalData?.trends) {
      const trends = historicalData.trends;
      if (trends.improving) {
        summary.highlights.push('Performance trends are improving');
      }

      if (trends.durationTrend > 20) {
        summary.concerns.push('Test execution time is increasing');
      }
    }

    // Overall status
    if (summary.concerns.length === 0) {
      summary.status = 'excellent';
    } else if (summary.concerns.length <= 2) {
      summary.status = 'good';
    } else {
      summary.status = 'needs-attention';
    }

    return summary;
  }

  calculateROI(performanceData, reliabilityData) {
    const roi = {
      timeSavings: {},
      costSavings: {},
      productivityGains: {},
      qualityImprovements: {},
    };

    if (performanceData?.comparison?.available) {
      const comparison = performanceData.comparison;

      // Time savings
      roi.timeSavings = {
        perRun: comparison.timeSavings.perRun / 1000 / 60, // minutes
        daily: comparison.timeSavings.dailyMinutes,
        weekly: comparison.timeSavings.weeklyHours,
        monthly: comparison.timeSavings.monthlyHours,
        yearly: comparison.timeSavings.monthlyHours * 12,
      };

      // Cost savings (assuming $100/hour developer cost)
      const hourlyRate = 100;
      roi.costSavings = {
        weekly: comparison.timeSavings.weeklyHours * hourlyRate,
        monthly: comparison.timeSavings.monthlyHours * hourlyRate,
        yearly: comparison.timeSavings.monthlyHours * 12 * hourlyRate,
      };

      // Productivity gains
      const fasterFeedback = comparison.speedImprovement.percentage;
      roi.productivityGains = {
        fasterFeedback: `${fasterFeedback.toFixed(1)}% faster feedback loops`,
        deploymentFrequency: fasterFeedback > 50 ? 'Increased deployment frequency' : 'Moderate improvement',
        developerSatisfaction: fasterFeedback > 70 ? 'High' : 'Moderate',
      };
    }

    // Quality improvements
    if (performanceData?.contractTests && performanceData?.e2eTests?.available) {
      const contractReliability = performanceData.contractTests.reliability;
      const e2eReliability = performanceData.e2eTests.reliability;

      roi.qualityImprovements = {
        testReliability: contractReliability - e2eReliability,
        reducedFlakiness: contractReliability > e2eReliability ? 'Significant reduction' : 'Minimal change',
        betterIsolation: 'Improved error isolation and debugging',
      };
    }

    return roi;
  }

  generateDashboardHTML(data) {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contract Testing Performance Dashboard</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f8f9fa; }
        .dashboard { max-width: 1400px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; background: white; padding: 30px; border-radius: 12px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .header h1 { color: #2c3e50; margin-bottom: 10px; }
        .header .timestamp { color: #7f8c8d; }
        .status-badge { display: inline-block; padding: 8px 16px; border-radius: 20px; font-weight: bold; text-transform: uppercase; font-size: 0.8em; }
        .status-excellent { background: #d4edda; color: #155724; }
        .status-good { background: #fff3cd; color: #856404; }
        .status-needs-attention { background: #f8d7da; color: #721c24; }
        
        .grid { display: grid; gap: 20px; margin-bottom: 30px; }
        .grid-2 { grid-template-columns: 1fr 1fr; }
        .grid-3 { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }
        .grid-4 { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }
        
        .card { background: white; border-radius: 12px; padding: 24px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
        .card h2 { color: #2c3e50; margin-bottom: 16px; font-size: 1.3em; }
        .card h3 { color: #34495e; margin-bottom: 12px; font-size: 1.1em; }
        
        .metric { text-align: center; padding: 20px; }
        .metric-value { font-size: 2.5em; font-weight: bold; margin-bottom: 8px; }
        .metric-label { color: #7f8c8d; font-size: 0.9em; }
        .metric-excellent { color: #27ae60; }
        .metric-good { color: #f39c12; }
        .metric-poor { color: #e74c3c; }
        
        .highlight { background: #d4edda; color: #155724; padding: 12px; border-radius: 8px; margin: 8px 0; }
        .concern { background: #f8d7da; color: #721c24; padding: 12px; border-radius: 8px; margin: 8px 0; }
        
        .comparison-table { width: 100%; border-collapse: collapse; }
        .comparison-table th, .comparison-table td { padding: 12px; text-align: left; border-bottom: 1px solid #dee2e6; }
        .comparison-table th { background: #f8f9fa; font-weight: 600; }
        .improvement { color: #27ae60; font-weight: bold; }
        .degradation { color: #e74c3c; font-weight: bold; }
        
        .roi-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; }
        .roi-item { text-align: center; padding: 16px; background: #f8f9fa; border-radius: 8px; }
        .roi-value { font-size: 1.8em; font-weight: bold; color: #27ae60; }
        .roi-label { color: #7f8c8d; font-size: 0.9em; margin-top: 4px; }
        
        .chart-placeholder { height: 200px; background: #f8f9fa; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #7f8c8d; }
        
        @media (max-width: 768px) {
            .grid-2, .grid-3, .grid-4 { grid-template-columns: 1fr; }
            .dashboard { padding: 10px; }
        }
    </style>
</head>
<body>
    <div class="dashboard">
        ${this.generateHeaderSection(data)}
        ${this.generateOverviewSection(data)}
        ${this.generatePerformanceSection(data)}
        ${this.generateReliabilitySection(data)}
        ${this.generateROISection(data)}
        ${this.generateHistoricalSection(data)}
    </div>
</body>
</html>`;
  }

  generateHeaderSection(data) {
    const status = data.summary?.status || 'unknown';
    const statusClass = `status-${status}`;

    return `
        <div class="header">
            <h1>Contract Testing Performance Dashboard</h1>
            <p class="timestamp">Last updated: ${new Date(data.timestamp).toLocaleString()}</p>
            <div style="margin-top: 16px;">
                <span class="status-badge ${statusClass}">Status: ${status.replace('-', ' ')}</span>
            </div>
        </div>`;
  }

  generateOverviewSection(data) {
    const summary = data.summary || {};
    const metrics = summary.metrics || {};

    return `
        <div class="grid grid-4">
            <div class="card metric">
                <div class="metric-value ${this.getMetricClass(metrics.contractReliability, 95, 85)}">
                    ${metrics.contractReliability ? metrics.contractReliability.toFixed(1) + '%' : 'N/A'}
                </div>
                <div class="metric-label">Contract Test Reliability</div>
            </div>
            <div class="card metric">
                <div class="metric-value ${this.getMetricClass(metrics.contractDuration, 300000, 600000, true)}">
                    ${metrics.contractDuration ? (metrics.contractDuration / 1000).toFixed(1) + 's' : 'N/A'}
                </div>
                <div class="metric-label">Average Duration</div>
            </div>
            <div class="card metric">
                <div class="metric-value ${this.getMetricClass(metrics.speedImprovement, 50, 25)}">
                    ${metrics.speedImprovement ? metrics.speedImprovement.toFixed(1) + '%' : 'N/A'}
                </div>
                <div class="metric-label">Speed Improvement</div>
            </div>
            <div class="card metric">
                <div class="metric-value ${this.getMetricClass(metrics.timeSavings, 5, 2)}">
                    ${metrics.timeSavings ? metrics.timeSavings.toFixed(1) + 'h' : 'N/A'}
                </div>
                <div class="metric-label">Weekly Time Savings</div>
            </div>
        </div>

        <div class="grid grid-2">
            <div class="card">
                <h2>✅ Highlights</h2>
                ${
                  summary.highlights?.length > 0
                    ? summary.highlights.map((h) => `<div class="highlight">${h}</div>`).join('')
                    : '<p>No highlights available</p>'
                }
            </div>
            <div class="card">
                <h2>⚠️ Areas for Improvement</h2>
                ${
                  summary.concerns?.length > 0
                    ? summary.concerns.map((c) => `<div class="concern">${c}</div>`).join('')
                    : '<div class="highlight">All metrics look good!</div>'
                }
            </div>
        </div>`;
  }

  generatePerformanceSection(data) {
    const performance = data.performance;

    if (!performance) {
      return `
        <div class="card">
            <h2>Performance Comparison</h2>
            <p>No performance data available. Run the benchmark script to generate performance data.</p>
        </div>`;
    }

    const contract = performance.contractTests;
    const e2e = performance.e2eTests;
    const comparison = performance.comparison;

    return `
        <div class="card">
            <h2>Performance Comparison</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>Metric</th>
                        <th>Contract Tests</th>
                        ${e2e?.available ? '<th>E2E Tests</th>' : ''}
                        <th>Improvement</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Average Duration</td>
                        <td>${(contract.avgDuration / 1000).toFixed(1)}s</td>
                        ${e2e?.available ? `<td>${(e2e.avgDuration / 1000).toFixed(1)}s</td>` : ''}
                        <td class="${comparison?.available ? 'improvement' : ''}">
                            ${comparison?.available ? comparison.speedImprovement.percentage.toFixed(1) + '% faster' : 'N/A'}
                        </td>
                    </tr>
                    <tr>
                        <td>Reliability</td>
                        <td>${contract.reliability.toFixed(1)}%</td>
                        ${e2e?.available ? `<td>${e2e.reliability.toFixed(1)}%</td>` : ''}
                        <td class="${comparison?.available && comparison.reliabilityImprovement.percentage > 0 ? 'improvement' : ''}">
                            ${comparison?.available ? comparison.reliabilityImprovement.percentage.toFixed(1) + '% more reliable' : 'N/A'}
                        </td>
                    </tr>
                    <tr>
                        <td>Tests Run</td>
                        <td>${Math.round(contract.avgTestsRun)}</td>
                        ${e2e?.available ? `<td>${Math.round(e2e.avgTestsRun)}</td>` : ''}
                        <td>-</td>
                    </tr>
                </tbody>
            </table>
        </div>`;
  }

  generateReliabilitySection(data) {
    const reliability = data.reliability;

    if (!reliability) {
      return `
        <div class="card">
            <h2>Reliability Analysis</h2>
            <p>No reliability data available. Run the reliability tracking script to generate data.</p>
        </div>`;
    }

    const summary = reliability.summary || {};
    const recentRun = reliability.runs?.[reliability.runs.length - 1];

    return `
        <div class="grid grid-2">
            <div class="card">
                <h2>Reliability Metrics</h2>
                <div class="grid grid-2">
                    <div class="metric">
                        <div class="metric-value ${this.getMetricClass(summary.avgReliability, 95, 85)}">
                            ${summary.avgReliability ? summary.avgReliability.toFixed(1) + '%' : 'N/A'}
                        </div>
                        <div class="metric-label">Average Reliability</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value ${summary.trend > 0 ? 'metric-excellent' : summary.trend < -5 ? 'metric-poor' : 'metric-good'}">
                            ${summary.trend ? (summary.trend > 0 ? '+' : '') + summary.trend.toFixed(1) + '%' : 'N/A'}
                        </div>
                        <div class="metric-label">Reliability Trend</div>
                    </div>
                </div>
            </div>
            <div class="card">
                <h2>Flaky Tests</h2>
                ${
                  summary.recentFlakyTests?.length > 0
                    ? `
                    <p>Recent flaky tests detected:</p>
                    <ul>
                        ${summary.recentFlakyTests
                          .slice(0, 5)
                          .map((test) => `<li>${test.name} (${test.failureRate?.toFixed(1) || 'N/A'}% failure rate)</li>`)
                          .join('')}
                    </ul>
                `
                    : '<p class="highlight">No flaky tests detected! 🎉</p>'
                }
            </div>
        </div>`;
  }

  generateROISection(data) {
    const roi = data.roi || {};

    return `
        <div class="card">
            <h2>Return on Investment (ROI)</h2>
            <div class="grid grid-3">
                <div>
                    <h3>Time Savings</h3>
                    <div class="roi-grid">
                        <div class="roi-item">
                            <div class="roi-value">${roi.timeSavings?.weekly?.toFixed(1) || 'N/A'}h</div>
                            <div class="roi-label">Weekly</div>
                        </div>
                        <div class="roi-item">
                            <div class="roi-value">${roi.timeSavings?.monthly?.toFixed(1) || 'N/A'}h</div>
                            <div class="roi-label">Monthly</div>
                        </div>
                        <div class="roi-item">
                            <div class="roi-value">${roi.timeSavings?.yearly?.toFixed(0) || 'N/A'}h</div>
                            <div class="roi-label">Yearly</div>
                        </div>
                    </div>
                </div>
                <div>
                    <h3>Cost Savings</h3>
                    <div class="roi-grid">
                        <div class="roi-item">
                            <div class="roi-value">$${roi.costSavings?.weekly?.toFixed(0) || 'N/A'}</div>
                            <div class="roi-label">Weekly</div>
                        </div>
                        <div class="roi-item">
                            <div class="roi-value">$${roi.costSavings?.monthly?.toFixed(0) || 'N/A'}</div>
                            <div class="roi-label">Monthly</div>
                        </div>
                        <div class="roi-item">
                            <div class="roi-value">$${roi.costSavings?.yearly?.toFixed(0) || 'N/A'}</div>
                            <div class="roi-label">Yearly</div>
                        </div>
                    </div>
                </div>
                <div>
                    <h3>Quality Improvements</h3>
                    <ul>
                        <li>${roi.qualityImprovements?.reducedFlakiness || 'Improved test stability'}</li>
                        <li>${roi.qualityImprovements?.betterIsolation || 'Better error isolation'}</li>
                        <li>${roi.productivityGains?.fasterFeedback || 'Faster feedback loops'}</li>
                    </ul>
                </div>
            </div>
        </div>`;
  }

  generateHistoricalSection(data) {
    const historical = data.historical || {};
    const trends = historical.trends || {};

    return `
        <div class="card">
            <h2>Historical Trends</h2>
            <div class="grid grid-2">
                <div>
                    <h3>Performance Trends</h3>
                    ${
                      trends.durationTrend !== undefined
                        ? `
                        <p>Duration trend: <span class="${trends.durationTrend < 0 ? 'improvement' : 'degradation'}">
                            ${trends.durationTrend > 0 ? '+' : ''}${trends.durationTrend.toFixed(1)}%
                        </span></p>
                    `
                        : '<p>No trend data available</p>'
                    }
                    ${
                      trends.reliabilityTrend !== undefined
                        ? `
                        <p>Reliability trend: <span class="${trends.reliabilityTrend > 0 ? 'improvement' : 'degradation'}">
                            ${trends.reliabilityTrend > 0 ? '+' : ''}${trends.reliabilityTrend.toFixed(1)}%
                        </span></p>
                    `
                        : ''
                    }
                </div>
                <div>
                    <h3>Data Points</h3>
                    <p>Benchmark runs: ${historical.benchmarks?.length || 0}</p>
                    <p>Overall trend: <span class="${trends.improving ? 'improvement' : 'degradation'}">
                        ${trends.improving ? 'Improving' : 'Needs attention'}
                    </span></p>
                </div>
            </div>
            <div class="chart-placeholder">
                Historical performance chart would be displayed here
                <br><small>Consider integrating with Chart.js or similar library</small>
            </div>
        </div>`;
  }

  getMetricClass(value, excellentThreshold, goodThreshold, inverse = false) {
    if (!value) return 'metric-good';

    if (inverse) {
      // For metrics where lower is better (like duration)
      if (value <= excellentThreshold) return 'metric-excellent';
      if (value <= goodThreshold) return 'metric-good';
      return 'metric-poor';
    } else {
      // For metrics where higher is better (like reliability)
      if (value >= excellentThreshold) return 'metric-excellent';
      if (value >= goodThreshold) return 'metric-good';
      return 'metric-poor';
    }
  }

  generateCSVExport(data) {
    const csvData = [];

    // Header
    csvData.push([
      'Timestamp',
      'Contract Duration (ms)',
      'Contract Reliability (%)',
      'E2E Duration (ms)',
      'E2E Reliability (%)',
      'Speed Improvement (%)',
      'Weekly Time Savings (h)',
      'Status',
    ]);

    // Current data
    const performance = data.performance;
    const summary = data.summary;

    if (performance) {
      csvData.push([
        data.timestamp,
        performance.contractTests?.avgDuration || '',
        performance.contractTests?.reliability || '',
        performance.e2eTests?.avgDuration || '',
        performance.e2eTests?.reliability || '',
        performance.comparison?.speedImprovement?.percentage || '',
        performance.comparison?.timeSavings?.weeklyHours || '',
        summary?.status || '',
      ]);
    }

    // Historical data
    if (data.historical?.benchmarks) {
      data.historical.benchmarks.forEach((benchmark) => {
        csvData.push([
          benchmark.timestamp,
          benchmark.contractDuration,
          benchmark.contractReliability,
          benchmark.e2eDuration,
          benchmark.e2eReliability,
          benchmark.speedImprovement,
          '', // Weekly savings not available in historical data
          '', // Status not available in historical data
        ]);
      });
    }

    // Convert to CSV string
    const csvContent = csvData.map((row) => row.map((field) => `"${field}"`).join(',')).join('\n');

    // Save CSV file
    const csvPath = path.join(this.dashboardDir, 'performance-data.csv');
    fs.writeFileSync(csvPath, csvContent);
    console.log(`📊 CSV export generated: ${csvPath}`);
  }
}

// Run dashboard generation if called directly
if (require.main === module) {
  const dashboard = new PerformanceDashboard();
  dashboard.generateDashboard().catch((error) => {
    console.error('Dashboard generation failed:', error);
    process.exit(1);
  });
}

module.exports = PerformanceDashboard;
