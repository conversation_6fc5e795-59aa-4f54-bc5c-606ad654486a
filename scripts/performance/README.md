# Contract Testing Performance Analysis Tools

This directory contains tools for analyzing and reporting on the performance benefits of contract testing compared to E2E testing.

## Tools Overview

### 1. Benchmark Contract Tests (`benchmark-contract-tests.js`)

Runs performance benchmarks comparing contract tests with E2E tests.

**Usage:**
```bash
# Run benchmark with default settings
npm run performance:benchmark

# Run directly with Node.js
node scripts/performance/benchmark-contract-tests.js
```

**Features:**
- Runs contract tests multiple times to get reliable metrics
- Attempts to run E2E tests for comparison (if available)
- Measures execution time, reliability, and resource usage
- Generates detailed HTML and JSON reports
- Calculates performance improvements and time savings

**Output:**
- `reports/performance/performance-benchmark-{timestamp}.json`
- `reports/performance/performance-benchmark-{timestamp}.html`
- `reports/performance/latest.json` (latest results)
- `reports/performance/latest.html` (latest report)

### 2. Track Test Reliability (`track-test-reliability.js`)

Monitors test reliability over time and identifies flaky tests.

**Usage:**
```bash
# Run reliability tracking with default 10 iterations
npm run performance:reliability

# Run with custom number of iterations
node scripts/performance/track-test-reliability.js 20
```

**Features:**
- Runs contract tests multiple times to track reliability
- Identifies flaky tests and failure patterns
- Calculates consistency scores and trends
- Tracks reliability improvements over time
- Generates detailed reliability reports

**Output:**
- `reports/reliability/reliability-data.json` (historical data)
- `reports/reliability/reliability-report-{timestamp}.html`
- `reports/reliability/latest-reliability.html`

### 3. Generate Performance Dashboard (`generate-performance-dashboard.js`)

Creates a comprehensive performance dashboard combining all metrics.

**Usage:**
```bash
# Generate dashboard from existing data
npm run performance:dashboard

# Run directly
node scripts/performance/generate-performance-dashboard.js
```

**Features:**
- Combines benchmark and reliability data
- Calculates ROI and cost savings
- Shows historical trends and improvements
- Generates interactive HTML dashboard
- Exports data in multiple formats (JSON, CSV)

**Output:**
- `reports/dashboard/performance-dashboard.html`
- `reports/dashboard/dashboard-data.json`
- `reports/dashboard/performance-data.csv`

## Quick Start

### Run Complete Performance Analysis

```bash
# Run all performance analysis tools
npm run performance:full-analysis
```

This will:
1. Run performance benchmarks
2. Track test reliability
3. Generate comprehensive dashboard
4. Create all reports and exports

### View Results

After running the analysis, open the generated reports:

```bash
# Open the main dashboard
open reports/dashboard/performance-dashboard.html

# Open latest benchmark report
open reports/performance/latest.html

# Open latest reliability report
open reports/reliability/latest-reliability.html
```

## Report Structure

### Performance Reports Directory

```
reports/
├── performance/           # Benchmark results
│   ├── latest.json       # Latest benchmark data
│   ├── latest.html       # Latest benchmark report
│   └── performance-benchmark-*.json/html
├── reliability/          # Reliability tracking
│   ├── reliability-data.json  # Historical reliability data
│   ├── latest-reliability.html
│   └── reliability-report-*.html
└── dashboard/           # Combined dashboard
    ├── performance-dashboard.html  # Main dashboard
    ├── dashboard-data.json        # Dashboard data
    └── performance-data.csv       # CSV export
```

## Key Metrics Tracked

### Performance Metrics
- **Execution Time**: Average, min, max test execution times
- **Speed Improvement**: Percentage improvement over E2E tests
- **Resource Usage**: Memory and CPU utilization
- **Consistency**: Execution time variance and predictability

### Reliability Metrics
- **Test Reliability**: Percentage of successful test runs
- **Flaky Tests**: Tests that intermittently fail
- **Failure Patterns**: Common failure reasons and categories
- **Consistency Score**: Measure of test execution consistency

### Business Metrics
- **Time Savings**: Daily, weekly, monthly time savings
- **Cost Savings**: Dollar value of time savings
- **ROI**: Return on investment calculations
- **Productivity Impact**: Developer productivity improvements

## Configuration

### Environment Variables

```bash
# Pact logging level for performance tests
export PACT_LOG_LEVEL=ERROR

# Number of benchmark iterations
export BENCHMARK_ITERATIONS=5

# Number of reliability tracking iterations
export RELIABILITY_ITERATIONS=10
```

### Customization

You can customize the analysis by modifying the configuration in each script:

```javascript
// benchmark-contract-tests.js
const iterations = 5;  // Number of benchmark runs
const timeout = 300000; // 5 minutes timeout

// track-test-reliability.js
const defaultIterations = 10; // Default reliability tracking runs

// generate-performance-dashboard.js
const maxHistoricalRuns = 30; // Number of historical runs to include
```

## CI/CD Integration

### Automated Performance Tracking

Add to your CI/CD pipeline:

```yaml
# .buildkite/pipeline.yml
- label: "Performance Analysis"
  command: |
    npm run performance:benchmark
    npm run performance:dashboard
  artifact_paths:
    - "reports/**/*"
```

### Performance Monitoring

Set up regular performance monitoring:

```bash
# Weekly performance check
0 9 * * 1 cd /path/to/project && npm run performance:full-analysis
```

## Troubleshooting

### Common Issues

1. **No E2E tests found**
   - The benchmark tool will skip E2E comparison
   - Contract test analysis will still work

2. **Port conflicts**
   - Tools use dynamic port allocation
   - If issues persist, run tests sequentially

3. **Memory issues**
   - Reduce number of iterations
   - Run with `--max-old-space-size=4096`

4. **Permission errors**
   - Ensure write permissions to `reports/` directory
   - Create directories manually if needed

### Debug Mode

Run tools with debug output:

```bash
# Enable debug logging
PACT_LOG_LEVEL=DEBUG npm run performance:benchmark

# Run with verbose output
node scripts/performance/benchmark-contract-tests.js --verbose
```

## Contributing

When adding new performance metrics or tools:

1. Follow the existing code structure
2. Add appropriate error handling
3. Generate both JSON and HTML outputs
4. Update this README with new features
5. Add tests for new functionality

## Support

For questions or issues with the performance analysis tools:

1. Check this README and troubleshooting section
2. Review the generated reports for error details
3. Contact the platform team for infrastructure issues
4. Create an issue in the project repository

---

*These tools help demonstrate the value and ROI of contract testing implementation.*