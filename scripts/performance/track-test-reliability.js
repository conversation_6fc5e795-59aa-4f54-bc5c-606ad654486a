#!/usr/bin/env node

/**
 * Test Reliability Tracking Tool
 *
 * This script tracks test reliability over time, identifying flaky tests
 * and monitoring improvements in test stability.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class ReliabilityTracker {
  constructor() {
    this.dataDir = path.join(process.cwd(), 'reports', 'reliability');
    this.dataFile = path.join(this.dataDir, 'reliability-data.json');
    this.ensureDataDirectory();
    this.loadHistoricalData();
  }

  ensureDataDirectory() {
    if (!fs.existsSync(this.dataDir)) {
      fs.mkdirSync(this.dataDir, { recursive: true });
    }
  }

  loadHistoricalData() {
    if (fs.existsSync(this.dataFile)) {
      try {
        this.historicalData = JSON.parse(fs.readFileSync(this.dataFile, 'utf8'));
      } catch (error) {
        console.warn('Failed to load historical data, starting fresh');
        this.historicalData = { runs: [], summary: {} };
      }
    } else {
      this.historicalData = { runs: [], summary: {} };
    }
  }

  async trackReliability(iterations = 10) {
    console.log('📊 Starting Test Reliability Tracking');
    console.log(`Running ${iterations} iterations to track reliability...`);
    console.log('=====================================');

    const runData = {
      timestamp: new Date().toISOString(),
      iterations,
      results: [],
      summary: {},
    };

    // Run contract tests multiple times
    for (let i = 1; i <= iterations; i++) {
      console.log(`\n🔄 Iteration ${i}/${iterations}`);

      const result = await this.runSingleIteration(i);
      runData.results.push(result);

      // Display immediate feedback
      if (result.success) {
        console.log(`   ✅ Success - ${result.duration}ms, ${result.testsRun} tests`);
      } else {
        console.log(`   ❌ Failed - ${result.duration}ms, Error: ${result.error}`);
      }

      // Brief pause between iterations
      if (i < iterations) {
        await this.sleep(1000);
      }
    }

    // Calculate reliability metrics
    runData.summary = this.calculateReliabilityMetrics(runData.results);

    // Store the run data
    this.historicalData.runs.push(runData);

    // Update overall summary
    this.updateOverallSummary();

    // Save data
    this.saveData();

    // Generate reports
    await this.generateReliabilityReport(runData);

    // Display summary
    this.displayReliabilitySummary(runData);

    return runData;
  }

  async runSingleIteration(iteration) {
    const startTime = Date.now();

    try {
      const output = execSync('npm run test:contracts', {
        encoding: 'utf8',
        timeout: 300000, // 5 minutes
        stdio: 'pipe',
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Parse test results
      const testResults = this.parseTestOutput(output);

      return {
        iteration,
        timestamp: new Date().toISOString(),
        success: true,
        duration,
        testsRun: testResults.total,
        testsPassed: testResults.passed,
        testsFailed: testResults.failed,
        testDetails: testResults.details,
        output: this.sanitizeOutput(output),
      };
    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;

      return {
        iteration,
        timestamp: new Date().toISOString(),
        success: false,
        duration,
        error: error.message,
        testsRun: 0,
        testsPassed: 0,
        testsFailed: 0,
        output: this.sanitizeOutput(error.stdout || error.message),
      };
    }
  }

  parseTestOutput(output) {
    const results = {
      total: 0,
      passed: 0,
      failed: 0,
      details: [],
    };

    // Parse Jest output for individual test results
    const lines = output.split('\n');
    let currentSuite = '';

    for (const line of lines) {
      // Detect test suite
      const suiteMatch = line.match(/^\s*(.+\.contract\.test\.js)$/);
      if (suiteMatch) {
        currentSuite = suiteMatch[1];
        continue;
      }

      // Detect individual test results
      const testMatch = line.match(/^\s*(✓|×)\s+(.+?)\s+\((\d+)ms\)/);
      if (testMatch) {
        const [, status, testName, duration] = testMatch;
        const success = status === '✓';

        results.details.push({
          suite: currentSuite,
          name: testName.trim(),
          success,
          duration: parseInt(duration),
        });

        if (success) {
          results.passed++;
        } else {
          results.failed++;
        }
        results.total++;
      }
    }

    // Fallback parsing if detailed parsing fails
    if (results.total === 0) {
      const summaryMatch = output.match(/Tests:\s+(\d+)\s+failed,\s+(\d+)\s+passed,\s+(\d+)\s+total/);
      if (summaryMatch) {
        results.failed = parseInt(summaryMatch[1]);
        results.passed = parseInt(summaryMatch[2]);
        results.total = parseInt(summaryMatch[3]);
      }
    }

    return results;
  }

  sanitizeOutput(output) {
    // Remove ANSI color codes and limit length
    return output.replace(/\x1b\[[0-9;]*m/g, '').substring(0, 5000);
  }

  calculateReliabilityMetrics(results) {
    const successfulRuns = results.filter((r) => r.success);
    const failedRuns = results.filter((r) => !r.success);

    const reliability = (successfulRuns.length / results.length) * 100;

    // Calculate duration statistics
    const durations = successfulRuns.map((r) => r.duration);
    const avgDuration = durations.length > 0 ? durations.reduce((a, b) => a + b, 0) / durations.length : 0;
    const minDuration = durations.length > 0 ? Math.min(...durations) : 0;
    const maxDuration = durations.length > 0 ? Math.max(...durations) : 0;

    // Calculate test count statistics
    const testCounts = successfulRuns.map((r) => r.testsRun);
    const avgTestsRun = testCounts.length > 0 ? testCounts.reduce((a, b) => a + b, 0) / testCounts.length : 0;

    // Identify flaky tests
    const flakyTests = this.identifyFlakyTests(results);

    // Calculate failure patterns
    const failureReasons = this.analyzeFailureReasons(failedRuns);

    return {
      reliability,
      totalRuns: results.length,
      successfulRuns: successfulRuns.length,
      failedRuns: failedRuns.length,
      avgDuration,
      minDuration,
      maxDuration,
      avgTestsRun,
      flakyTests,
      failureReasons,
      consistencyScore: this.calculateConsistencyScore(results),
    };
  }

  identifyFlakyTests(results) {
    const testResults = {};

    // Collect results for each test
    results.forEach((run) => {
      if (run.testDetails) {
        run.testDetails.forEach((test) => {
          const testKey = `${test.suite}::${test.name}`;
          if (!testResults[testKey]) {
            testResults[testKey] = { runs: 0, failures: 0, suite: test.suite, name: test.name };
          }
          testResults[testKey].runs++;
          if (!test.success) {
            testResults[testKey].failures++;
          }
        });
      }
    });

    // Identify flaky tests (tests that sometimes pass, sometimes fail)
    const flakyTests = [];
    Object.entries(testResults).forEach(([testKey, data]) => {
      const failureRate = data.failures / data.runs;
      if (failureRate > 0 && failureRate < 1) {
        flakyTests.push({
          suite: data.suite,
          name: data.name,
          runs: data.runs,
          failures: data.failures,
          failureRate: failureRate * 100,
        });
      }
    });

    // Sort by failure rate (most flaky first)
    return flakyTests.sort((a, b) => b.failureRate - a.failureRate);
  }

  analyzeFailureReasons(failedRuns) {
    const reasons = {};

    failedRuns.forEach((run) => {
      const error = run.error || 'Unknown error';

      // Categorize common error types
      let category = 'Other';
      if (error.includes('timeout') || error.includes('TIMEOUT')) {
        category = 'Timeout';
      } else if (error.includes('ECONNREFUSED') || error.includes('connection')) {
        category = 'Connection Error';
      } else if (error.includes('Port') || error.includes('EADDRINUSE')) {
        category = 'Port Conflict';
      } else if (error.includes('Authentication') || error.includes('401')) {
        category = 'Authentication Error';
      } else if (error.includes('Schema') || error.includes('validation')) {
        category = 'Schema Validation';
      }

      if (!reasons[category]) {
        reasons[category] = { count: 0, examples: [] };
      }
      reasons[category].count++;

      if (reasons[category].examples.length < 3) {
        reasons[category].examples.push(error.substring(0, 200));
      }
    });

    return reasons;
  }

  calculateConsistencyScore(results) {
    if (results.length < 2) return 100;

    const successfulRuns = results.filter((r) => r.success);
    if (successfulRuns.length === 0) return 0;

    // Calculate coefficient of variation for duration
    const durations = successfulRuns.map((r) => r.duration);
    const mean = durations.reduce((a, b) => a + b, 0) / durations.length;
    const variance = durations.reduce((sum, duration) => sum + Math.pow(duration - mean, 2), 0) / durations.length;
    const stdDev = Math.sqrt(variance);
    const coefficientOfVariation = stdDev / mean;

    // Convert to consistency score (lower variation = higher consistency)
    const consistencyScore = Math.max(0, 100 - coefficientOfVariation * 100);

    return consistencyScore;
  }

  updateOverallSummary() {
    const allRuns = this.historicalData.runs;
    if (allRuns.length === 0) return;

    const recentRuns = allRuns.slice(-10); // Last 10 runs
    const reliabilities = recentRuns.map((run) => run.summary.reliability);
    const avgReliability = reliabilities.reduce((a, b) => a + b, 0) / reliabilities.length;

    // Calculate trend
    const oldReliability =
      allRuns.length > 5 ? allRuns.slice(-10, -5).reduce((sum, run) => sum + run.summary.reliability, 0) / 5 : avgReliability;
    const trend = avgReliability - oldReliability;

    this.historicalData.summary = {
      totalRuns: allRuns.length,
      avgReliability,
      trend,
      lastUpdated: new Date().toISOString(),
      recentFlakyTests: this.aggregateFlakyTests(recentRuns),
    };
  }

  aggregateFlakyTests(runs) {
    const allFlakyTests = {};

    runs.forEach((run) => {
      if (run.summary.flakyTests) {
        run.summary.flakyTests.forEach((test) => {
          const key = `${test.suite}::${test.name}`;
          if (!allFlakyTests[key]) {
            allFlakyTests[key] = { ...test, occurrences: 0 };
          }
          allFlakyTests[key].occurrences++;
        });
      }
    });

    return Object.values(allFlakyTests)
      .sort((a, b) => b.occurrences - a.occurrences)
      .slice(0, 10); // Top 10 flaky tests
  }

  saveData() {
    fs.writeFileSync(this.dataFile, JSON.stringify(this.historicalData, null, 2));
  }

  async generateReliabilityReport(runData) {
    const htmlContent = this.generateReliabilityHTML(runData);
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `reliability-report-${timestamp}.html`;
    const filepath = path.join(this.dataDir, filename);

    fs.writeFileSync(filepath, htmlContent);
    console.log(`\n📊 Reliability report generated: ${filepath}`);

    // Also save as latest
    const latestPath = path.join(this.dataDir, 'latest-reliability.html');
    fs.writeFileSync(latestPath, htmlContent);
  }

  generateReliabilityHTML(runData) {
    const summary = runData.summary;

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Reliability Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .metric-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0; }
        .metric-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff; }
        .metric-value { font-size: 2em; font-weight: bold; color: #007bff; }
        .metric-label { color: #666; margin-top: 5px; }
        .reliability-excellent { border-left-color: #28a745; }
        .reliability-excellent .metric-value { color: #28a745; }
        .reliability-good { border-left-color: #ffc107; }
        .reliability-good .metric-value { color: #ffc107; }
        .reliability-poor { border-left-color: #dc3545; }
        .reliability-poor .metric-value { color: #dc3545; }
        .flaky-tests { margin: 20px 0; }
        .flaky-test { background: #fff3cd; padding: 10px; margin: 5px 0; border-radius: 4px; border-left: 4px solid #ffc107; }
        .failure-reasons { margin: 20px 0; }
        .failure-reason { background: #f8d7da; padding: 10px; margin: 5px 0; border-radius: 4px; border-left: 4px solid #dc3545; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; }
        .status-success { color: #28a745; }
        .status-failure { color: #dc3545; }
        .timestamp { color: #666; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Test Reliability Report</h1>
            <p class="timestamp">Generated: ${new Date(runData.timestamp).toLocaleString()}</p>
            <p>Based on ${runData.iterations} test iterations</p>
        </div>

        <div class="metric-grid">
            <div class="metric-card ${this.getReliabilityClass(summary.reliability)}">
                <div class="metric-value">${summary.reliability.toFixed(1)}%</div>
                <div class="metric-label">Test Reliability</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${summary.successfulRuns}/${summary.totalRuns}</div>
                <div class="metric-label">Successful Runs</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${(summary.avgDuration / 1000).toFixed(1)}s</div>
                <div class="metric-label">Average Duration</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${summary.consistencyScore.toFixed(1)}%</div>
                <div class="metric-label">Consistency Score</div>
            </div>
        </div>

        ${this.generateFlakyTestsSection(summary.flakyTests)}
        ${this.generateFailureReasonsSection(summary.failureReasons)}
        ${this.generateRunDetailsTable(runData.results)}
        ${this.generateRecommendationsSection(summary)}
    </div>
</body>
</html>`;
  }

  getReliabilityClass(reliability) {
    if (reliability >= 95) return 'reliability-excellent';
    if (reliability >= 85) return 'reliability-good';
    return 'reliability-poor';
  }

  generateFlakyTestsSection(flakyTests) {
    if (!flakyTests || flakyTests.length === 0) {
      return `
        <div class="flaky-tests">
            <h2>Flaky Tests</h2>
            <p>✅ No flaky tests detected!</p>
        </div>`;
    }

    return `
        <div class="flaky-tests">
            <h2>Flaky Tests (${flakyTests.length})</h2>
            ${flakyTests
              .map(
                (test) => `
                <div class="flaky-test">
                    <strong>${test.name}</strong> in ${test.suite}<br>
                    <small>Failed ${test.failures}/${test.runs} times (${test.failureRate.toFixed(1)}% failure rate)</small>
                </div>
            `,
              )
              .join('')}
        </div>`;
  }

  generateFailureReasonsSection(failureReasons) {
    if (!failureReasons || Object.keys(failureReasons).length === 0) {
      return `
        <div class="failure-reasons">
            <h2>Failure Analysis</h2>
            <p>✅ No failures to analyze!</p>
        </div>`;
    }

    return `
        <div class="failure-reasons">
            <h2>Failure Analysis</h2>
            ${Object.entries(failureReasons)
              .map(
                ([category, data]) => `
                <div class="failure-reason">
                    <strong>${category}</strong> (${data.count} occurrences)<br>
                    <small>Example: ${data.examples[0] || 'No example available'}</small>
                </div>
            `,
              )
              .join('')}
        </div>`;
  }

  generateRunDetailsTable(results) {
    return `
        <div>
            <h2>Run Details</h2>
            <table>
                <thead>
                    <tr>
                        <th>Iteration</th>
                        <th>Status</th>
                        <th>Duration</th>
                        <th>Tests Run</th>
                        <th>Tests Passed</th>
                        <th>Tests Failed</th>
                    </tr>
                </thead>
                <tbody>
                    ${results
                      .map(
                        (result) => `
                        <tr>
                            <td>${result.iteration}</td>
                            <td class="${result.success ? 'status-success' : 'status-failure'}">
                                ${result.success ? '✅ Success' : '❌ Failed'}
                            </td>
                            <td>${(result.duration / 1000).toFixed(1)}s</td>
                            <td>${result.testsRun}</td>
                            <td>${result.testsPassed}</td>
                            <td>${result.testsFailed}</td>
                        </tr>
                    `,
                      )
                      .join('')}
                </tbody>
            </table>
        </div>`;
  }

  generateRecommendationsSection(summary) {
    const recommendations = [];

    if (summary.reliability < 95) {
      recommendations.push('⚠️ Test reliability is below 95%. Investigate and fix flaky tests.');
    }

    if (summary.flakyTests && summary.flakyTests.length > 0) {
      recommendations.push(`⚠️ ${summary.flakyTests.length} flaky tests detected. Focus on stabilizing these tests first.`);
    }

    if (summary.consistencyScore < 80) {
      recommendations.push('⚠️ Test execution time is inconsistent. Check for resource contention or environmental issues.');
    }

    if (summary.avgDuration > 300000) {
      recommendations.push('⚠️ Tests are taking longer than 5 minutes. Consider optimization or parallel execution.');
    }

    if (recommendations.length === 0) {
      recommendations.push('✅ Test reliability looks good! Continue monitoring for any degradation.');
    }

    return `
        <div>
            <h2>Recommendations</h2>
            <ul>
                ${recommendations.map((rec) => `<li>${rec}</li>`).join('')}
            </ul>
        </div>`;
  }

  displayReliabilitySummary(runData) {
    const summary = runData.summary;

    console.log('\n🎯 Reliability Tracking Complete!');
    console.log('==================================');
    console.log(`📊 Reliability: ${summary.reliability.toFixed(1)}%`);
    console.log(`✅ Successful Runs: ${summary.successfulRuns}/${summary.totalRuns}`);
    console.log(`⏱️  Average Duration: ${(summary.avgDuration / 1000).toFixed(1)}s`);
    console.log(`📈 Consistency Score: ${summary.consistencyScore.toFixed(1)}%`);

    if (summary.flakyTests && summary.flakyTests.length > 0) {
      console.log(`\n⚠️  Flaky Tests Detected: ${summary.flakyTests.length}`);
      summary.flakyTests.slice(0, 3).forEach((test) => {
        console.log(`   - ${test.name} (${test.failureRate.toFixed(1)}% failure rate)`);
      });
    }

    if (Object.keys(summary.failureReasons).length > 0) {
      console.log(`\n❌ Failure Categories:`);
      Object.entries(summary.failureReasons).forEach(([category, data]) => {
        console.log(`   - ${category}: ${data.count} occurrences`);
      });
    }

    console.log(`\n📄 Report saved to: ${this.dataDir}`);
  }

  sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}

// Run reliability tracking if called directly
if (require.main === module) {
  const iterations = parseInt(process.argv[2]) || 10;
  const tracker = new ReliabilityTracker();
  tracker.trackReliability(iterations).catch((error) => {
    console.error('Reliability tracking failed:', error);
    process.exit(1);
  });
}

module.exports = ReliabilityTracker;
