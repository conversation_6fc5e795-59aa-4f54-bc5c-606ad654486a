#!/usr/bin/env node

/**
 * Contract Test Performance Benchmarking Tool
 *
 * This script runs contract tests and E2E tests to compare performance metrics
 * including execution time, reliability, and resource usage.
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class PerformanceBenchmark {
  constructor() {
    this.results = {
      timestamp: new Date().toISOString(),
      contractTests: {},
      e2eTests: {},
      comparison: {},
    };
    this.reportDir = path.join(process.cwd(), 'reports', 'performance');
    this.ensureReportDirectory();
  }

  ensureReportDirectory() {
    if (!fs.existsSync(this.reportDir)) {
      fs.mkdirSync(this.reportDir, { recursive: true });
    }
  }

  async runBenchmark() {
    console.log('🚀 Starting Contract Test Performance Benchmark');
    console.log('================================================');

    try {
      // Run contract tests benchmark
      console.log('\n📊 Running Contract Tests Benchmark...');
      await this.benchmarkContractTests();

      // Run E2E tests benchmark (if available)
      console.log('\n📊 Running E2E Tests Benchmark...');
      await this.benchmarkE2ETests();

      // Generate comparison report
      console.log('\n📈 Generating Performance Comparison...');
      this.generateComparison();

      // Save results
      await this.saveResults();

      // Display summary
      this.displaySummary();
    } catch (error) {
      console.error('❌ Benchmark failed:', error.message);
      process.exit(1);
    }
  }

  async benchmarkContractTests() {
    const iterations = 5;
    const results = [];

    for (let i = 1; i <= iterations; i++) {
      console.log(`  Run ${i}/${iterations}...`);

      const startTime = Date.now();
      const startMemory = process.memoryUsage();

      try {
        // Run contract tests
        const output = execSync('npm run test:contracts', {
          encoding: 'utf8',
          timeout: 300000, // 5 minutes timeout
          stdio: 'pipe',
        });

        const endTime = Date.now();
        const endMemory = process.memoryUsage();
        const duration = endTime - startTime;

        // Parse test results
        const testResults = this.parseJestOutput(output);

        results.push({
          iteration: i,
          duration,
          success: true,
          testsRun: testResults.numTotalTests,
          testsPassed: testResults.numPassedTests,
          testsFailed: testResults.numFailedTests,
          memoryUsage: {
            heapUsed: endMemory.heapUsed - startMemory.heapUsed,
            heapTotal: endMemory.heapTotal - startMemory.heapTotal,
            external: endMemory.external - startMemory.external,
          },
        });

        console.log(`    ✅ Completed in ${duration}ms`);
      } catch (error) {
        const endTime = Date.now();
        const duration = endTime - startTime;

        results.push({
          iteration: i,
          duration,
          success: false,
          error: error.message,
          testsRun: 0,
          testsPassed: 0,
          testsFailed: 0,
        });

        console.log(`    ❌ Failed in ${duration}ms`);
      }

      // Wait between iterations to avoid resource conflicts
      if (i < iterations) {
        await this.sleep(2000);
      }
    }

    this.results.contractTests = this.calculateStatistics(results);
  }

  async benchmarkE2ETests() {
    // Check if E2E tests are available
    const e2eTestCommand = this.detectE2ETestCommand();

    if (!e2eTestCommand) {
      console.log('  ⚠️  E2E tests not found or not configured');
      this.results.e2eTests = {
        available: false,
        reason: 'E2E tests not found or not configured',
      };
      return;
    }

    const iterations = 3; // Fewer iterations for slower E2E tests
    const results = [];

    for (let i = 1; i <= iterations; i++) {
      console.log(`  Run ${i}/${iterations}...`);

      const startTime = Date.now();
      const startMemory = process.memoryUsage();

      try {
        // Run E2E tests (subset that covers similar functionality)
        const output = execSync(e2eTestCommand, {
          encoding: 'utf8',
          timeout: 1800000, // 30 minutes timeout
          stdio: 'pipe',
        });

        const endTime = Date.now();
        const endMemory = process.memoryUsage();
        const duration = endTime - startTime;

        // Parse test results based on test framework
        const testResults = this.parseE2EOutput(output, e2eTestCommand);

        results.push({
          iteration: i,
          duration,
          success: true,
          testsRun: testResults.numTotalTests,
          testsPassed: testResults.numPassedTests,
          testsFailed: testResults.numFailedTests,
          memoryUsage: {
            heapUsed: endMemory.heapUsed - startMemory.heapUsed,
            heapTotal: endMemory.heapTotal - startMemory.heapTotal,
            external: endMemory.external - startMemory.external,
          },
        });

        console.log(`    ✅ Completed in ${(duration / 1000).toFixed(1)}s`);
      } catch (error) {
        const endTime = Date.now();
        const duration = endTime - startTime;

        results.push({
          iteration: i,
          duration,
          success: false,
          error: error.message,
          testsRun: 0,
          testsPassed: 0,
          testsFailed: 0,
        });

        console.log(`    ❌ Failed in ${(duration / 1000).toFixed(1)}s`);
      }

      // Longer wait between E2E test iterations
      if (i < iterations) {
        await this.sleep(5000);
      }
    }

    this.results.e2eTests = this.calculateStatistics(results);
  }

  detectE2ETestCommand() {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const scripts = packageJson.scripts || {};

    // Look for common E2E test script names
    const e2eScriptNames = ['test:e2e', 'e2e', 'cypress:run', 'test:integration', 'test:functional'];

    for (const scriptName of e2eScriptNames) {
      if (scripts[scriptName]) {
        return `npm run ${scriptName}`;
      }
    }

    // Check for Cypress
    if (fs.existsSync('cypress.config.ts') || fs.existsSync('cypress.config.js')) {
      return 'npx cypress run --headless';
    }

    return null;
  }

  parseJestOutput(output) {
    const results = {
      numTotalTests: 0,
      numPassedTests: 0,
      numFailedTests: 0,
    };

    // Parse Jest output for test counts
    const testSummaryMatch = output.match(/Tests:\s+(\d+)\s+failed,\s+(\d+)\s+passed,\s+(\d+)\s+total/);
    if (testSummaryMatch) {
      results.numFailedTests = parseInt(testSummaryMatch[1]);
      results.numPassedTests = parseInt(testSummaryMatch[2]);
      results.numTotalTests = parseInt(testSummaryMatch[3]);
    } else {
      // Alternative parsing for different Jest output formats
      const passedMatch = output.match(/(\d+)\s+passed/);
      const failedMatch = output.match(/(\d+)\s+failed/);

      if (passedMatch) results.numPassedTests = parseInt(passedMatch[1]);
      if (failedMatch) results.numFailedTests = parseInt(failedMatch[1]);
      results.numTotalTests = results.numPassedTests + results.numFailedTests;
    }

    return results;
  }

  parseE2EOutput(output, command) {
    const results = {
      numTotalTests: 0,
      numPassedTests: 0,
      numFailedTests: 0,
    };

    if (command.includes('cypress')) {
      // Parse Cypress output
      const specMatch = output.match(/(\d+)\s+passing/);
      const failMatch = output.match(/(\d+)\s+failing/);

      if (specMatch) results.numPassedTests = parseInt(specMatch[1]);
      if (failMatch) results.numFailedTests = parseInt(failMatch[1]);
      results.numTotalTests = results.numPassedTests + results.numFailedTests;
    } else {
      // Generic parsing for other test frameworks
      results.numTotalTests = 1; // Assume at least one test ran
      results.numPassedTests = 1;
      results.numFailedTests = 0;
    }

    return results;
  }

  calculateStatistics(results) {
    const successfulRuns = results.filter((r) => r.success);
    const durations = successfulRuns.map((r) => r.duration);

    if (durations.length === 0) {
      return {
        available: true,
        reliability: 0,
        avgDuration: 0,
        minDuration: 0,
        maxDuration: 0,
        totalRuns: results.length,
        successfulRuns: 0,
        failedRuns: results.length,
      };
    }

    const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;
    const minDuration = Math.min(...durations);
    const maxDuration = Math.max(...durations);
    const reliability = (successfulRuns.length / results.length) * 100;

    // Calculate average test counts
    const avgTestsRun = successfulRuns.reduce((sum, r) => sum + r.testsRun, 0) / successfulRuns.length;
    const avgTestsPassed = successfulRuns.reduce((sum, r) => sum + r.testsPassed, 0) / successfulRuns.length;

    // Calculate memory usage statistics
    const memoryStats = this.calculateMemoryStatistics(successfulRuns);

    return {
      available: true,
      reliability,
      avgDuration,
      minDuration,
      maxDuration,
      totalRuns: results.length,
      successfulRuns: successfulRuns.length,
      failedRuns: results.length - successfulRuns.length,
      avgTestsRun,
      avgTestsPassed,
      memoryUsage: memoryStats,
      rawResults: results,
    };
  }

  calculateMemoryStatistics(successfulRuns) {
    if (successfulRuns.length === 0 || !successfulRuns[0].memoryUsage) {
      return null;
    }

    const heapUsed = successfulRuns.map((r) => r.memoryUsage.heapUsed);
    const heapTotal = successfulRuns.map((r) => r.memoryUsage.heapTotal);

    return {
      avgHeapUsed: heapUsed.reduce((a, b) => a + b, 0) / heapUsed.length,
      maxHeapUsed: Math.max(...heapUsed),
      avgHeapTotal: heapTotal.reduce((a, b) => a + b, 0) / heapTotal.length,
      maxHeapTotal: Math.max(...heapTotal),
    };
  }

  generateComparison() {
    const contract = this.results.contractTests;
    const e2e = this.results.e2eTests;

    if (!contract.available) {
      this.results.comparison = {
        available: false,
        reason: 'Contract tests not available',
      };
      return;
    }

    if (!e2e.available) {
      this.results.comparison = {
        available: false,
        reason: 'E2E tests not available for comparison',
      };
      return;
    }

    // Calculate performance improvements
    const speedImprovement = ((e2e.avgDuration - contract.avgDuration) / e2e.avgDuration) * 100;
    const reliabilityImprovement = contract.reliability - e2e.reliability;

    // Calculate time savings
    const timeSavingsPerRun = e2e.avgDuration - contract.avgDuration;
    const dailyRuns = 10; // Estimate
    const dailyTimeSavings = (timeSavingsPerRun * dailyRuns) / 1000 / 60; // minutes

    this.results.comparison = {
      available: true,
      speedImprovement: {
        percentage: speedImprovement,
        absoluteMs: timeSavingsPerRun,
        absoluteMinutes: timeSavingsPerRun / 1000 / 60,
      },
      reliabilityImprovement: {
        percentage: reliabilityImprovement,
        contractReliability: contract.reliability,
        e2eReliability: e2e.reliability,
      },
      timeSavings: {
        perRun: timeSavingsPerRun,
        dailyMinutes: dailyTimeSavings,
        weeklyHours: (dailyTimeSavings * 5) / 60,
        monthlyHours: (dailyTimeSavings * 22) / 60,
      },
      resourceUsage: this.compareResourceUsage(contract, e2e),
    };
  }

  compareResourceUsage(contract, e2e) {
    if (!contract.memoryUsage || !e2e.memoryUsage) {
      return null;
    }

    const heapReduction = ((e2e.memoryUsage.avgHeapUsed - contract.memoryUsage.avgHeapUsed) / e2e.memoryUsage.avgHeapUsed) * 100;

    return {
      memoryReduction: {
        percentage: heapReduction,
        contractAvgMB: contract.memoryUsage.avgHeapUsed / 1024 / 1024,
        e2eAvgMB: e2e.memoryUsage.avgHeapUsed / 1024 / 1024,
      },
    };
  }

  async saveResults() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `performance-benchmark-${timestamp}.json`;
    const filepath = path.join(this.reportDir, filename);

    fs.writeFileSync(filepath, JSON.stringify(this.results, null, 2));
    console.log(`\n📄 Results saved to: ${filepath}`);

    // Also save as latest.json for easy access
    const latestPath = path.join(this.reportDir, 'latest.json');
    fs.writeFileSync(latestPath, JSON.stringify(this.results, null, 2));

    // Generate HTML report
    await this.generateHTMLReport();
  }

  async generateHTMLReport() {
    const htmlContent = this.generateHTMLContent();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `performance-report-${timestamp}.html`;
    const filepath = path.join(this.reportDir, filename);

    fs.writeFileSync(filepath, htmlContent);
    console.log(`📊 HTML report generated: ${filepath}`);

    // Also save as latest.html
    const latestPath = path.join(this.reportDir, 'latest.html');
    fs.writeFileSync(latestPath, htmlContent);
  }

  generateHTMLContent() {
    const contract = this.results.contractTests;
    const e2e = this.results.e2eTests;
    const comparison = this.results.comparison;

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contract Test Performance Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .metric-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .metric-card { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff; }
        .metric-value { font-size: 2em; font-weight: bold; color: #007bff; }
        .metric-label { color: #666; margin-top: 5px; }
        .comparison-section { margin: 30px 0; }
        .improvement { color: #28a745; font-weight: bold; }
        .degradation { color: #dc3545; font-weight: bold; }
        .chart-container { margin: 20px 0; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; }
        .status-success { color: #28a745; }
        .status-failure { color: #dc3545; }
        .timestamp { color: #666; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Contract Test Performance Report</h1>
            <p class="timestamp">Generated: ${new Date(this.results.timestamp).toLocaleString()}</p>
        </div>

        ${this.generateSummarySection()}
        ${this.generateComparisonSection()}
        ${this.generateDetailedMetrics()}
        ${this.generateRecommendations()}
    </div>
</body>
</html>`;
  }

  generateSummarySection() {
    const contract = this.results.contractTests;
    const e2e = this.results.e2eTests;

    return `
        <div class="metric-grid">
            <div class="metric-card">
                <div class="metric-value">${(contract.avgDuration / 1000).toFixed(1)}s</div>
                <div class="metric-label">Contract Tests Avg Duration</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${contract.reliability.toFixed(1)}%</div>
                <div class="metric-label">Contract Tests Reliability</div>
            </div>
            ${
              e2e.available
                ? `
            <div class="metric-card">
                <div class="metric-value">${(e2e.avgDuration / 1000).toFixed(1)}s</div>
                <div class="metric-label">E2E Tests Avg Duration</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${e2e.reliability.toFixed(1)}%</div>
                <div class="metric-label">E2E Tests Reliability</div>
            </div>
            `
                : ''
            }
        </div>`;
  }

  generateComparisonSection() {
    const comparison = this.results.comparison;

    if (!comparison.available) {
      return `
        <div class="comparison-section">
            <h2>Performance Comparison</h2>
            <p>⚠️ ${comparison.reason}</p>
        </div>`;
    }

    const speedClass = comparison.speedImprovement.percentage > 0 ? 'improvement' : 'degradation';
    const reliabilityClass = comparison.reliabilityImprovement.percentage > 0 ? 'improvement' : 'degradation';

    return `
        <div class="comparison-section">
            <h2>Performance Comparison</h2>
            <div class="metric-grid">
                <div class="metric-card">
                    <div class="metric-value ${speedClass}">${comparison.speedImprovement.percentage.toFixed(1)}%</div>
                    <div class="metric-label">Speed Improvement</div>
                    <small>${comparison.speedImprovement.absoluteMinutes.toFixed(1)} minutes faster per run</small>
                </div>
                <div class="metric-card">
                    <div class="metric-value ${reliabilityClass}">${comparison.reliabilityImprovement.percentage.toFixed(1)}%</div>
                    <div class="metric-label">Reliability Improvement</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value improvement">${comparison.timeSavings.weeklyHours.toFixed(1)}h</div>
                    <div class="metric-label">Weekly Time Savings</div>
                    <small>${comparison.timeSavings.dailyMinutes.toFixed(1)} minutes saved daily</small>
                </div>
                <div class="metric-card">
                    <div class="metric-value improvement">${comparison.timeSavings.monthlyHours.toFixed(1)}h</div>
                    <div class="metric-label">Monthly Time Savings</div>
                </div>
            </div>
        </div>`;
  }

  generateDetailedMetrics() {
    const contract = this.results.contractTests;
    const e2e = this.results.e2eTests;

    return `
        <div class="comparison-section">
            <h2>Detailed Metrics</h2>
            <table>
                <thead>
                    <tr>
                        <th>Metric</th>
                        <th>Contract Tests</th>
                        ${e2e.available ? '<th>E2E Tests</th>' : ''}
                        <th>Notes</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Average Duration</td>
                        <td>${(contract.avgDuration / 1000).toFixed(1)}s</td>
                        ${e2e.available ? `<td>${(e2e.avgDuration / 1000).toFixed(1)}s</td>` : ''}
                        <td>Time to complete test suite</td>
                    </tr>
                    <tr>
                        <td>Reliability</td>
                        <td class="status-success">${contract.reliability.toFixed(1)}%</td>
                        ${e2e.available ? `<td class="${e2e.reliability > 95 ? 'status-success' : 'status-failure'}">${e2e.reliability.toFixed(1)}%</td>` : ''}
                        <td>Percentage of successful test runs</td>
                    </tr>
                    <tr>
                        <td>Tests Run</td>
                        <td>${Math.round(contract.avgTestsRun)}</td>
                        ${e2e.available ? `<td>${Math.round(e2e.avgTestsRun)}</td>` : ''}
                        <td>Average number of tests executed</td>
                    </tr>
                    <tr>
                        <td>Min Duration</td>
                        <td>${(contract.minDuration / 1000).toFixed(1)}s</td>
                        ${e2e.available ? `<td>${(e2e.minDuration / 1000).toFixed(1)}s</td>` : ''}
                        <td>Fastest test run</td>
                    </tr>
                    <tr>
                        <td>Max Duration</td>
                        <td>${(contract.maxDuration / 1000).toFixed(1)}s</td>
                        ${e2e.available ? `<td>${(e2e.maxDuration / 1000).toFixed(1)}s</td>` : ''}
                        <td>Slowest test run</td>
                    </tr>
                </tbody>
            </table>
        </div>`;
  }

  generateRecommendations() {
    const contract = this.results.contractTests;
    const comparison = this.results.comparison;
    const recommendations = [];

    if (contract.reliability < 95) {
      recommendations.push('⚠️ Contract test reliability is below 95%. Investigate flaky tests and improve stability.');
    }

    if (contract.avgDuration > 300000) {
      // 5 minutes
      recommendations.push(
        '⚠️ Contract tests are taking longer than 5 minutes. Consider optimizing test execution or running tests in parallel.',
      );
    }

    if (comparison.available && comparison.speedImprovement.percentage > 50) {
      recommendations.push('✅ Excellent performance improvement! Contract tests are significantly faster than E2E tests.');
    }

    if (comparison.available && comparison.reliabilityImprovement.percentage > 5) {
      recommendations.push(
        '✅ Contract tests show improved reliability over E2E tests. Consider migrating more test coverage to contract tests.',
      );
    }

    if (recommendations.length === 0) {
      recommendations.push('✅ Contract tests are performing well. Continue monitoring performance metrics.');
    }

    return `
        <div class="comparison-section">
            <h2>Recommendations</h2>
            <ul>
                ${recommendations.map((rec) => `<li>${rec}</li>`).join('')}
            </ul>
        </div>`;
  }

  displaySummary() {
    console.log('\n🎉 Performance Benchmark Complete!');
    console.log('=====================================');

    const contract = this.results.contractTests;
    console.log(`\n📊 Contract Tests:`);
    console.log(`   Average Duration: ${(contract.avgDuration / 1000).toFixed(1)}s`);
    console.log(`   Reliability: ${contract.reliability.toFixed(1)}%`);
    console.log(`   Successful Runs: ${contract.successfulRuns}/${contract.totalRuns}`);

    const e2e = this.results.e2eTests;
    if (e2e.available) {
      console.log(`\n📊 E2E Tests:`);
      console.log(`   Average Duration: ${(e2e.avgDuration / 1000).toFixed(1)}s`);
      console.log(`   Reliability: ${e2e.reliability.toFixed(1)}%`);
      console.log(`   Successful Runs: ${e2e.successfulRuns}/${e2e.totalRuns}`);
    }

    const comparison = this.results.comparison;
    if (comparison.available) {
      console.log(`\n🚀 Performance Improvement:`);
      console.log(`   Speed: ${comparison.speedImprovement.percentage.toFixed(1)}% faster`);
      console.log(`   Reliability: ${comparison.reliabilityImprovement.percentage.toFixed(1)}% more reliable`);
      console.log(`   Time Savings: ${comparison.timeSavings.weeklyHours.toFixed(1)} hours/week`);
    }

    console.log(`\n📄 Reports generated in: ${this.reportDir}`);
  }

  sleep(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}

// Run benchmark if called directly
if (require.main === module) {
  const benchmark = new PerformanceBenchmark();
  benchmark.runBenchmark().catch((error) => {
    console.error('Benchmark failed:', error);
    process.exit(1);
  });
}

module.exports = PerformanceBenchmark;
