#!/usr/bin/env node

/**
 * Contract Change Notification Script
 *
 * This script sends notifications to provider teams when contracts change,
 * helping coordinate verification efforts between consumer and provider teams.
 */

const fs = require('fs');
const path = require('path');

const VERIFICATION_REPORT_PATH = path.join(__dirname, '../../tests/contracts/logs/verification-report.json');
const BUILD_NUMBER = process.env.BUILDKITE_BUILD_NUMBER || 'local';
const BRANCH = process.env.BUILDKITE_BRANCH || 'unknown';
const COMMIT = process.env.BUILDKITE_COMMIT || 'unknown';
const BUILD_URL = process.env.BUILDKITE_BUILD_URL || 'local';

console.log('📢 Processing contract change notifications...');

// Read verification report
if (!fs.existsSync(VERIFICATION_REPORT_PATH)) {
  console.log('⚠️  No verification report found - skipping notifications');
  process.exit(0);
}

let verificationReport;
try {
  verificationReport = JSON.parse(fs.readFileSync(VERIFICATION_REPORT_PATH, 'utf8'));
} catch (error) {
  console.log('❌ Error reading verification report:', error.message);
  process.exit(1);
}

const { changes } = verificationReport;

if (!changes || changes.length === 0) {
  console.log('✅ No contract changes detected - no notifications needed');
  process.exit(0);
}

console.log(`📋 Processing ${changes.length} contract changes...`);

// Provider team mapping (in a real implementation, this would come from a config file)
const providerTeamMapping = {
  'hotels-api': {
    team: 'backend-hotels',
    slack: '#team-hotels-api',
    email: '<EMAIL>',
    webhook: process.env.HOTELS_API_WEBHOOK_URL,
  },
  'auth-api': {
    team: 'backend-auth',
    slack: '#team-auth-services',
    email: '<EMAIL>',
    webhook: process.env.AUTH_API_WEBHOOK_URL,
  },
  'sanity-cms': {
    team: 'content-team',
    slack: '#team-content',
    email: '<EMAIL>',
    webhook: process.env.SANITY_WEBHOOK_URL,
  },
  'payment-services': {
    team: 'payments-team',
    slack: '#team-payments',
    email: '<EMAIL>',
    webhook: process.env.PAYMENTS_WEBHOOK_URL,
  },
  'geolocation-services': {
    team: 'platform-team',
    slack: '#team-platform',
    email: '<EMAIL>',
    webhook: process.env.GEOLOCATION_WEBHOOK_URL,
  },
};

// Process each contract change
const notifications = [];

changes.forEach((change) => {
  const providerInfo = providerTeamMapping[change.provider];

  if (!providerInfo) {
    console.log(`⚠️  No team mapping found for provider: ${change.provider}`);
    return;
  }

  const notification = {
    provider: change.provider,
    team: providerInfo.team,
    changeType: change.type,
    channels: {
      slack: providerInfo.slack,
      email: providerInfo.email,
      webhook: providerInfo.webhook,
    },
    message: generateNotificationMessage(change, providerInfo),
    priority: change.type === 'breaking' ? 'high' : 'normal',
  };

  notifications.push(notification);

  console.log(`📤 Notification prepared for ${change.provider} (${change.type})`);
});

// Send notifications (in a real implementation, this would integrate with actual notification services)
notifications.forEach((notification) => {
  console.log(`\n📢 Sending notification to ${notification.team}:`);
  console.log(`   Provider: ${notification.provider}`);
  console.log(`   Change Type: ${notification.changeType}`);
  console.log(`   Priority: ${notification.priority}`);
  console.log(`   Slack: ${notification.channels.slack}`);

  // Simulate sending notification
  if (process.env.BUILDKITE) {
    console.log(`   🚀 Would send to webhook: ${notification.channels.webhook || 'not configured'}`);
  }

  // In a real implementation, you would:
  // 1. Send Slack message using Slack API
  // 2. Send email notification
  // 3. Trigger webhook to provider's CI/CD system
  // 4. Create GitHub/GitLab issue or PR comment
});

// Generate summary report
const summary = {
  timestamp: new Date().toISOString(),
  buildNumber: BUILD_NUMBER,
  branch: BRANCH,
  commit: COMMIT,
  buildUrl: BUILD_URL,
  totalNotifications: notifications.length,
  highPriorityNotifications: notifications.filter((n) => n.priority === 'high').length,
  notifications: notifications.map((n) => ({
    provider: n.provider,
    team: n.team,
    changeType: n.changeType,
    priority: n.priority,
  })),
};

// Save notification summary
const summaryPath = path.join(__dirname, '../../tests/contracts/logs/notification-summary.json');
fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));

console.log('\n📊 Notification Summary:');
console.log(`   Total notifications: ${summary.totalNotifications}`);
console.log(`   High priority: ${summary.highPriorityNotifications}`);
console.log(`   Normal priority: ${summary.totalNotifications - summary.highPriorityNotifications}`);

// Output for build annotation
if (process.env.BUILDKITE) {
  const annotation = generateBuildAnnotation(summary);
  console.log('\n--- Notification Summary for Build Annotation ---');
  console.log(annotation);
}

console.log('🎉 Contract change notifications completed!');

function generateNotificationMessage(change, providerInfo) {
  const changeTypeEmoji = {
    new: '🆕',
    modified: '🔄',
    breaking: '⚠️',
  };

  const emoji = changeTypeEmoji[change.type] || '📋';
  const urgency = change.type === 'breaking' ? '**URGENT** - ' : '';

  return {
    title: `${emoji} Contract Change Notification - ${change.provider}`,
    body: `${urgency}The Qantas Hotels UI team has ${change.type === 'new' ? 'created a new' : 'updated the'} contract for ${change.provider}.

**Details:**
- Change Type: ${change.type}
- Provider: ${change.provider}
- Build: ${BUILD_NUMBER}
- Branch: ${BRANCH}
- Commit: ${COMMIT.substring(0, 8)}

**Action Required:**
${
  change.type === 'breaking'
    ? '⚠️ This change may be breaking. Please verify your service can fulfill the updated contract before the next deployment.'
    : 'Please verify your service can fulfill the updated contract at your earliest convenience.'
}

**Contract Location:**
- Build artifacts: ${BUILD_URL}
- Contract file: qantas-hotels-ui-${change.provider}.json

**Next Steps:**
1. Download the updated contract from the build artifacts
2. Run provider verification tests against your service
3. Report any issues to the Hotels UI team
4. Confirm compatibility before deployment

Need help? Contact the Hotels UI team in #team-bellhops or reply to this notification.`,

    slack: {
      text: `${emoji} Contract change detected for ${change.provider}`,
      attachments: [
        {
          color: change.type === 'breaking' ? 'danger' : 'warning',
          fields: [
            { title: 'Provider', value: change.provider, short: true },
            { title: 'Change Type', value: change.type, short: true },
            { title: 'Build', value: BUILD_NUMBER, short: true },
            { title: 'Branch', value: BRANCH, short: true },
          ],
          actions: [
            {
              type: 'button',
              text: 'View Build',
              url: BUILD_URL,
            },
          ],
        },
      ],
    },
  };
}

function generateBuildAnnotation(summary) {
  const highPriorityIcon = summary.highPriorityNotifications > 0 ? '⚠️' : '✅';

  return `## 📢 Contract Change Notifications

**Build:** ${summary.buildNumber}  
**Branch:** ${summary.branch}  
**Notifications Sent:** ${summary.totalNotifications}

### Notification Summary ${highPriorityIcon}
- **High Priority:** ${summary.highPriorityNotifications} (breaking changes)
- **Normal Priority:** ${summary.totalNotifications - summary.highPriorityNotifications}

### Provider Teams Notified
${summary.notifications
  .map((n) => {
    const icon = n.priority === 'high' ? '⚠️' : '📋';
    return `${icon} **${n.team}** - ${n.provider} (${n.changeType})`;
  })
  .join('\n')}

${summary.highPriorityNotifications > 0 ? '\n⚠️ **Breaking changes detected** - Provider verification required before deployment' : ''}
`;
}
