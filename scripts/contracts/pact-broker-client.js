#!/usr/bin/env node

/**
 * Pact Broker Client
 *
 * This script provides functionality to interact with a Pact Broker
 * for publishing contracts and triggering provider verification.
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');

const PACTS_DIR = path.join(__dirname, '../../tests/contracts/pacts');
const PACT_BROKER_URL = process.env.PACT_BROKER_URL || 'http://localhost:9292';
const PACT_BROKER_TOKEN = process.env.PACT_BROKER_TOKEN;
const BUILD_NUMBER = process.env.BUILDKITE_BUILD_NUMBER || 'local';
const BRANCH = process.env.BUILDKITE_BRANCH || 'unknown';
const COMMIT = process.env.BUILDKITE_COMMIT || 'unknown';

class PactBrokerClient {
  constructor(brokerUrl, token) {
    this.brokerUrl = brokerUrl;
    this.token = token;
    this.headers = {
      'Content-Type': 'application/json',
      Accept: 'application/hal+json',
    };

    if (token) {
      this.headers['Authorization'] = `Bearer ${token}`;
    }
  }

  async publishContract(contractPath, consumer, provider, version) {
    console.log(`📤 Publishing contract: ${consumer} -> ${provider} (v${version})`);

    const contractContent = fs.readFileSync(contractPath, 'utf8');
    const contract = JSON.parse(contractContent);

    // Add version and build metadata
    contract.metadata = {
      ...contract.metadata,
      pactSpecification: { version: '2.0.0' },
      buildInfo: {
        buildNumber: BUILD_NUMBER,
        branch: BRANCH,
        commit: COMMIT,
        timestamp: new Date().toISOString(),
      },
    };

    const url = `${this.brokerUrl}/pacts/provider/${provider}/consumer/${consumer}/version/${version}`;

    try {
      const response = await this.makeRequest('PUT', url, contract);

      if (response.statusCode >= 200 && response.statusCode < 300) {
        console.log(`✅ Contract published successfully`);
        return { success: true, response: response.data };
      } else {
        console.log(`❌ Failed to publish contract: ${response.statusCode} ${response.data}`);
        return { success: false, error: response.data };
      }
    } catch (error) {
      console.log(`❌ Error publishing contract: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  async triggerProviderVerification(provider, consumerVersionSelectors = null) {
    console.log(`🚀 Triggering provider verification for: ${provider}`);

    const selectors = consumerVersionSelectors || [{ latest: true }, { branch: BRANCH }, { deployedOrReleased: true }];

    const payload = {
      providerVersionBranch: BRANCH,
      consumerVersionSelectors: selectors,
      includePendingStatus: true,
      includeWipPactsSince: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
    };

    const url = `${this.brokerUrl}/pacts/provider/${provider}/for-verification`;

    try {
      const response = await this.makeRequest('POST', url, payload);

      if (response.statusCode >= 200 && response.statusCode < 300) {
        console.log(`✅ Provider verification triggered successfully`);
        return { success: true, response: response.data };
      } else {
        console.log(`❌ Failed to trigger verification: ${response.statusCode} ${response.data}`);
        return { success: false, error: response.data };
      }
    } catch (error) {
      console.log(`❌ Error triggering verification: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  async getProviderVerificationResults(provider, consumerVersion) {
    console.log(`📊 Getting verification results for: ${provider} <- ${consumerVersion}`);

    const url = `${this.brokerUrl}/verification-results/provider/${provider}/consumer/qantas-hotels-ui/version/${consumerVersion}`;

    try {
      const response = await this.makeRequest('GET', url);

      if (response.statusCode >= 200 && response.statusCode < 300) {
        return { success: true, results: response.data };
      } else {
        console.log(`❌ Failed to get verification results: ${response.statusCode}`);
        return { success: false, error: response.data };
      }
    } catch (error) {
      console.log(`❌ Error getting verification results: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  async createWebhook(provider, events = ['contract_content_changed']) {
    console.log(`🔗 Creating webhook for provider: ${provider}`);

    const webhook = {
      description: `Trigger ${provider} verification on contract changes`,
      provider: { name: provider },
      consumer: { name: 'qantas-hotels-ui' },
      events: events,
      request: {
        method: 'POST',
        url: `${process.env.PROVIDER_WEBHOOK_BASE_URL || 'https://api.example.com'}/webhooks/${provider}/contract-changed`,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${process.env.PROVIDER_WEBHOOK_TOKEN || 'token'}`,
        },
        body: {
          consumer: '${pactbroker.consumerName}',
          provider: '${pactbroker.providerName}',
          pactUrl: '${pactbroker.pactUrl}',
          verificationResultUrl: '${pactbroker.verificationResultUrl}',
          buildNumber: BUILD_NUMBER,
          branch: BRANCH,
        },
      },
    };

    const url = `${this.brokerUrl}/webhooks`;

    try {
      const response = await this.makeRequest('POST', url, webhook);

      if (response.statusCode >= 200 && response.statusCode < 300) {
        console.log(`✅ Webhook created successfully`);
        return { success: true, webhook: response.data };
      } else {
        console.log(`❌ Failed to create webhook: ${response.statusCode} ${response.data}`);
        return { success: false, error: response.data };
      }
    } catch (error) {
      console.log(`❌ Error creating webhook: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  async makeRequest(method, url, data = null) {
    return new Promise((resolve, reject) => {
      const urlObj = new URL(url);
      const isHttps = urlObj.protocol === 'https:';
      const client = isHttps ? https : http;

      const options = {
        hostname: urlObj.hostname,
        port: urlObj.port || (isHttps ? 443 : 80),
        path: urlObj.pathname + urlObj.search,
        method: method,
        headers: this.headers,
      };

      const req = client.request(options, (res) => {
        let responseData = '';

        res.on('data', (chunk) => {
          responseData += chunk;
        });

        res.on('end', () => {
          try {
            const parsedData = responseData ? JSON.parse(responseData) : null;
            resolve({
              statusCode: res.statusCode,
              headers: res.headers,
              data: parsedData,
            });
          } catch (error) {
            resolve({
              statusCode: res.statusCode,
              headers: res.headers,
              data: responseData,
            });
          }
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      if (data) {
        req.write(JSON.stringify(data));
      }

      req.end();
    });
  }
}

// Main execution
async function main() {
  const command = process.argv[2];

  if (!command) {
    console.log('Usage: node pact-broker-client.js <command> [options]');
    console.log('Commands:');
    console.log('  publish - Publish all contracts to Pact Broker');
    console.log('  verify <provider> - Trigger provider verification');
    console.log('  results <provider> - Get verification results');
    console.log('  webhook <provider> - Create webhook for provider');
    process.exit(1);
  }

  const client = new PactBrokerClient(PACT_BROKER_URL, PACT_BROKER_TOKEN);

  switch (command) {
    case 'publish':
      await publishAllContracts(client);
      break;

    case 'verify':
      const provider = process.argv[3];
      if (!provider) {
        console.log('Error: Provider name required');
        process.exit(1);
      }
      await client.triggerProviderVerification(provider);
      break;

    case 'results':
      const resultsProvider = process.argv[3];
      if (!resultsProvider) {
        console.log('Error: Provider name required');
        process.exit(1);
      }
      const results = await client.getProviderVerificationResults(resultsProvider, BUILD_NUMBER);
      console.log('Verification results:', JSON.stringify(results, null, 2));
      break;

    case 'webhook':
      const webhookProvider = process.argv[3];
      if (!webhookProvider) {
        console.log('Error: Provider name required');
        process.exit(1);
      }
      await client.createWebhook(webhookProvider);
      break;

    default:
      console.log(`Unknown command: ${command}`);
      process.exit(1);
  }
}

async function publishAllContracts(client) {
  console.log('📋 Publishing all contracts to Pact Broker...');

  if (!fs.existsSync(PACTS_DIR)) {
    console.log('❌ Pacts directory not found');
    process.exit(1);
  }

  const pactFiles = fs
    .readdirSync(PACTS_DIR)
    .filter((file) => file.endsWith('.json'))
    .filter((file) => !file.includes('setup-verification'));

  if (pactFiles.length === 0) {
    console.log('⚠️  No contract files found to publish');
    return;
  }

  const results = [];

  for (const file of pactFiles) {
    const contractPath = path.join(PACTS_DIR, file);
    const provider = file.replace('qantas-hotels-ui-', '').replace('.json', '');

    const result = await client.publishContract(contractPath, 'qantas-hotels-ui', provider, BUILD_NUMBER);

    results.push({
      provider,
      file,
      success: result.success,
      error: result.error,
    });
  }

  // Summary
  const successful = results.filter((r) => r.success).length;
  const failed = results.filter((r) => !r.success).length;

  console.log(`\n📊 Publishing Summary:`);
  console.log(`   Successful: ${successful}`);
  console.log(`   Failed: ${failed}`);

  if (failed > 0) {
    console.log('\n❌ Failed publications:');
    results
      .filter((r) => !r.success)
      .forEach((r) => {
        console.log(`   - ${r.provider}: ${r.error}`);
      });
    process.exit(1);
  }

  console.log('✅ All contracts published successfully!');
}

if (require.main === module) {
  main().catch((error) => {
    console.error('❌ Unexpected error:', error);
    process.exit(1);
  });
}

module.exports = { PactBrokerClient };
