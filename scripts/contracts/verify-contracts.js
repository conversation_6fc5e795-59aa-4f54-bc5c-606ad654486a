#!/usr/bin/env node

/**
 * Contract Verification Script
 *
 * This script checks for contract changes and triggers provider verification
 * when contracts are updated.
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

const PACTS_DIR = path.join(__dirname, '../../tests/contracts/pacts');
const PREVIOUS_CONTRACTS_DIR = process.env.PREVIOUS_CONTRACTS_DIR || path.join(__dirname, '../../.contract-cache');
const BUILD_NUMBER = process.env.BUILDKITE_BUILD_NUMBER || 'local';
const BRANCH = process.env.BUILDKITE_BRANCH || 'unknown';

console.log('🔍 Verifying contract changes...');
console.log(`Build: ${BUILD_NUMBER}, Branch: ${BRANCH}`);

// Ensure cache directory exists
if (!fs.existsSync(PREVIOUS_CONTRACTS_DIR)) {
  fs.mkdirSync(PREVIOUS_CONTRACTS_DIR, { recursive: true });
}

// Get current contract files
const currentContracts = fs
  .readdirSync(PACTS_DIR)
  .filter((file) => file.endsWith('.json'))
  .filter((file) => !file.includes('setup-verification'));

if (currentContracts.length === 0) {
  console.log('⚠️  No contract files found for verification');
  process.exit(0);
}

console.log(`📄 Checking ${currentContracts.length} contract files for changes...`);

const changes = [];
const contractHashes = {};

// Check each contract for changes
currentContracts.forEach((file) => {
  const currentPath = path.join(PACTS_DIR, file);
  const previousPath = path.join(PREVIOUS_CONTRACTS_DIR, file);

  const currentContent = fs.readFileSync(currentPath, 'utf8');
  const currentHash = crypto.createHash('sha256').update(currentContent).digest('hex');

  contractHashes[file] = currentHash;

  let hasChanged = true;
  let changeType = 'new';

  if (fs.existsSync(previousPath)) {
    const previousContent = fs.readFileSync(previousPath, 'utf8');
    const previousHash = crypto.createHash('sha256').update(previousContent).digest('hex');

    if (currentHash === previousHash) {
      hasChanged = false;
      console.log(`✅ ${file}: No changes`);
    } else {
      changeType = 'modified';
      console.log(`🔄 ${file}: Modified`);

      // Analyze the type of change
      try {
        const currentContract = JSON.parse(currentContent);
        const previousContract = JSON.parse(previousContent);

        const currentInteractions = currentContract.interactions || [];
        const previousInteractions = previousContract.interactions || [];

        if (currentInteractions.length !== previousInteractions.length) {
          console.log(`   📊 Interaction count changed: ${previousInteractions.length} → ${currentInteractions.length}`);
        }

        // Check for breaking changes (simplified analysis)
        const hasBreakingChanges = checkForBreakingChanges(previousContract, currentContract);
        if (hasBreakingChanges) {
          console.log(`   ⚠️  Potential breaking changes detected`);
          changeType = 'breaking';
        }
      } catch (error) {
        console.log(`   ❌ Error analyzing contract changes: ${error.message}`);
      }
    }
  } else {
    console.log(`🆕 ${file}: New contract`);
  }

  if (hasChanged) {
    changes.push({
      file,
      type: changeType,
      provider: file.replace('qantas-hotels-ui-', '').replace('.json', ''),
      hash: currentHash,
    });

    // Update cache
    fs.writeFileSync(previousPath, currentContent);
  }
});

// Generate verification report
const report = {
  timestamp: new Date().toISOString(),
  buildNumber: BUILD_NUMBER,
  branch: BRANCH,
  totalContracts: currentContracts.length,
  changedContracts: changes.length,
  changes: changes,
  contractHashes: contractHashes,
};

// Save verification report
const reportPath = path.join(__dirname, '../../tests/contracts/logs/verification-report.json');
fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

console.log(`\n📊 Verification Summary:`);
console.log(`   Total contracts: ${currentContracts.length}`);
console.log(`   Changed contracts: ${changes.length}`);

if (changes.length > 0) {
  console.log(`\n🔄 Contract Changes:`);
  changes.forEach((change) => {
    const icon = change.type === 'breaking' ? '⚠️' : change.type === 'new' ? '🆕' : '🔄';
    console.log(`   ${icon} ${change.provider} (${change.type})`);
  });

  // If running in CI, trigger provider verification
  if (process.env.BUILDKITE && BRANCH !== 'master') {
    console.log(`\n🚀 Triggering provider verification for changed contracts...`);

    changes.forEach((change) => {
      console.log(`   📤 Notifying ${change.provider} team of contract changes`);
      // In a real implementation, this would trigger provider verification builds
      // For now, we'll just log the action
    });
  }

  // Output summary for build annotation
  const summary = `## 🔍 Contract Verification Results

**Build:** ${BUILD_NUMBER}  
**Branch:** ${BRANCH}  
**Changed Contracts:** ${changes.length}/${currentContracts.length}

### Contract Changes
${changes
  .map((change) => {
    const icon = change.type === 'breaking' ? '⚠️' : change.type === 'new' ? '🆕' : '🔄';
    return `${icon} **${change.provider}** (${change.type})`;
  })
  .join('\n')}

${changes.some((c) => c.type === 'breaking') ? '\n⚠️ **Breaking changes detected** - Provider verification required before deployment' : ''}
`;

  console.log('\n--- Contract Verification Summary for Build Annotation ---');
  console.log(summary);

  // Exit with error code if breaking changes detected on master branch
  if (BRANCH === 'master' && changes.some((c) => c.type === 'breaking')) {
    console.log('\n❌ Breaking contract changes detected on master branch!');
    process.exit(1);
  }
} else {
  console.log(`\n✅ No contract changes detected`);
}

console.log('🎉 Contract verification completed!');

/**
 * Simple breaking change detection
 * This is a basic implementation - in production you'd want more sophisticated analysis
 */
function checkForBreakingChanges(previousContract, currentContract) {
  try {
    const prevInteractions = previousContract.interactions || [];
    const currInteractions = currentContract.interactions || [];

    // Check if any previous interactions are missing (potential breaking change)
    for (const prevInteraction of prevInteractions) {
      const matchingCurrent = currInteractions.find(
        (curr) =>
          curr.description === prevInteraction.description &&
          curr.request?.method === prevInteraction.request?.method &&
          curr.request?.path === prevInteraction.request?.path,
      );

      if (!matchingCurrent) {
        return true; // Interaction removed - potentially breaking
      }

      // Check if response status changed to error status
      if (prevInteraction.response?.status < 400 && matchingCurrent.response?.status >= 400) {
        return true; // Success response changed to error - breaking
      }
    }

    return false;
  } catch (error) {
    console.log(`Warning: Error in breaking change detection: ${error.message}`);
    return false;
  }
}
