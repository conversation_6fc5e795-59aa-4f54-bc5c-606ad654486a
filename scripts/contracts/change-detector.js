#!/usr/bin/env node

/**
 * Contract Change Detection System
 *
 * This script provides advanced contract change detection with semantic
 * analysis and automated provider verification triggering.
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { PactBrokerClient } = require('./pact-broker-client');

const PACTS_DIR = path.join(__dirname, '../../tests/contracts/pacts');
const CHANGE_CACHE_DIR = path.join(__dirname, '../../.contract-cache');
const BUILD_NUMBER = process.env.BUILDKITE_BUILD_NUMBER || 'local';
const BRANCH = process.env.BUILDKITE_BRANCH || 'unknown';
const COMMIT = process.env.BUILDKITE_COMMIT || 'unknown';

class ContractChangeDetector {
  constructor() {
    this.cacheDir = CHANGE_CACHE_DIR;
    this.pactBrokerClient = new PactBrokerClient(process.env.PACT_BROKER_URL || 'http://localhost:9292', process.env.PACT_BROKER_TOKEN);

    // Ensure cache directory exists
    if (!fs.existsSync(this.cacheDir)) {
      fs.mkdirSync(this.cacheDir, { recursive: true });
    }
  }

  async detectChanges() {
    console.log('🔍 Detecting contract changes with semantic analysis...');

    const currentContracts = this.loadCurrentContracts();
    const previousContracts = this.loadPreviousContracts();

    const changes = [];

    for (const [contractFile, currentContract] of Object.entries(currentContracts)) {
      const provider = this.extractProviderName(contractFile);
      const previousContract = previousContracts[contractFile];

      const changeAnalysis = this.analyzeContractChange(previousContract, currentContract, contractFile);

      if (changeAnalysis.hasChanged) {
        changes.push({
          file: contractFile,
          provider,
          ...changeAnalysis,
        });

        console.log(`🔄 ${provider}: ${changeAnalysis.changeType} (${changeAnalysis.severity})`);

        // Cache the current contract for future comparisons
        this.cacheContract(contractFile, currentContract);
      } else {
        console.log(`✅ ${provider}: No changes`);
      }
    }

    // Detect removed contracts
    for (const contractFile of Object.keys(previousContracts)) {
      if (!currentContracts[contractFile]) {
        const provider = this.extractProviderName(contractFile);
        changes.push({
          file: contractFile,
          provider,
          changeType: 'removed',
          severity: 'major',
          hasChanged: true,
          breakingChanges: ['Contract removed'],
          summary: 'Contract was removed',
        });

        console.log(`❌ ${provider}: Contract removed`);
      }
    }

    return changes;
  }

  loadCurrentContracts() {
    const contracts = {};

    if (!fs.existsSync(PACTS_DIR)) {
      return contracts;
    }

    const pactFiles = fs
      .readdirSync(PACTS_DIR)
      .filter((file) => file.endsWith('.json'))
      .filter((file) => !file.includes('setup-verification'));

    for (const file of pactFiles) {
      try {
        const contractPath = path.join(PACTS_DIR, file);
        const contractContent = fs.readFileSync(contractPath, 'utf8');
        contracts[file] = JSON.parse(contractContent);
      } catch (error) {
        console.log(`⚠️  Error loading contract ${file}: ${error.message}`);
      }
    }

    return contracts;
  }

  loadPreviousContracts() {
    const contracts = {};
    const cacheFiles = fs.existsSync(this.cacheDir) ? fs.readdirSync(this.cacheDir).filter((f) => f.endsWith('.json')) : [];

    for (const file of cacheFiles) {
      try {
        const cachePath = path.join(this.cacheDir, file);
        const contractContent = fs.readFileSync(cachePath, 'utf8');
        contracts[file] = JSON.parse(contractContent);
      } catch (error) {
        console.log(`⚠️  Error loading cached contract ${file}: ${error.message}`);
      }
    }

    return contracts;
  }

  analyzeContractChange(previousContract, currentContract, contractFile) {
    if (!previousContract) {
      return {
        hasChanged: true,
        changeType: 'new',
        severity: 'minor',
        breakingChanges: [],
        nonBreakingChanges: ['New contract created'],
        summary: 'New contract created',
      };
    }

    // Calculate content hashes for quick comparison
    const previousHash = this.calculateContractHash(previousContract);
    const currentHash = this.calculateContractHash(currentContract);

    if (previousHash === currentHash) {
      return {
        hasChanged: false,
        changeType: 'none',
        severity: 'none',
      };
    }

    // Perform detailed semantic analysis
    const analysis = this.performSemanticAnalysis(previousContract, currentContract);

    return {
      hasChanged: true,
      changeType: analysis.breakingChanges.length > 0 ? 'breaking' : 'non-breaking',
      severity: this.determineSeverity(analysis),
      breakingChanges: analysis.breakingChanges,
      nonBreakingChanges: analysis.nonBreakingChanges,
      summary: this.generateChangeSummary(analysis),
      details: analysis,
    };
  }

  performSemanticAnalysis(previousContract, currentContract) {
    const analysis = {
      breakingChanges: [],
      nonBreakingChanges: [],
      interactionChanges: [],
      metadataChanges: [],
    };

    const prevInteractions = previousContract.interactions || [];
    const currInteractions = currentContract.interactions || [];

    // Analyze interaction changes
    this.analyzeInteractionChanges(prevInteractions, currInteractions, analysis);

    // Analyze metadata changes
    this.analyzeMetadataChanges(previousContract.metadata, currentContract.metadata, analysis);

    return analysis;
  }

  analyzeInteractionChanges(prevInteractions, currInteractions, analysis) {
    // Create maps for easier comparison
    const prevMap = new Map();
    const currMap = new Map();

    prevInteractions.forEach((interaction) => {
      const key = this.getInteractionKey(interaction);
      prevMap.set(key, interaction);
    });

    currInteractions.forEach((interaction) => {
      const key = this.getInteractionKey(interaction);
      currMap.set(key, interaction);
    });

    // Check for removed interactions (breaking)
    for (const [key, interaction] of prevMap) {
      if (!currMap.has(key)) {
        analysis.breakingChanges.push(`Removed interaction: ${interaction.description}`);
        analysis.interactionChanges.push({
          type: 'removed',
          interaction: interaction.description,
          severity: 'breaking',
        });
      }
    }

    // Check for new interactions (non-breaking)
    for (const [key, interaction] of currMap) {
      if (!prevMap.has(key)) {
        analysis.nonBreakingChanges.push(`Added interaction: ${interaction.description}`);
        analysis.interactionChanges.push({
          type: 'added',
          interaction: interaction.description,
          severity: 'non-breaking',
        });
      }
    }

    // Check for modified interactions
    for (const [key, currInteraction] of currMap) {
      const prevInteraction = prevMap.get(key);
      if (prevInteraction) {
        const interactionChanges = this.analyzeInteractionModification(prevInteraction, currInteraction);
        analysis.breakingChanges.push(...interactionChanges.breaking);
        analysis.nonBreakingChanges.push(...interactionChanges.nonBreaking);

        if (interactionChanges.breaking.length > 0 || interactionChanges.nonBreaking.length > 0) {
          analysis.interactionChanges.push({
            type: 'modified',
            interaction: currInteraction.description,
            severity: interactionChanges.breaking.length > 0 ? 'breaking' : 'non-breaking',
            changes: [...interactionChanges.breaking, ...interactionChanges.nonBreaking],
          });
        }
      }
    }
  }

  analyzeInteractionModification(prevInteraction, currInteraction) {
    const changes = { breaking: [], nonBreaking: [] };

    // Check request changes
    if (prevInteraction.request && currInteraction.request) {
      // Method change is breaking
      if (prevInteraction.request.method !== currInteraction.request.method) {
        changes.breaking.push(`Method changed: ${prevInteraction.request.method} -> ${currInteraction.request.method}`);
      }

      // Path change is breaking
      if (prevInteraction.request.path !== currInteraction.request.path) {
        changes.breaking.push(`Path changed: ${prevInteraction.request.path} -> ${currInteraction.request.path}`);
      }

      // Header changes
      this.analyzeHeaderChanges(prevInteraction.request.headers, currInteraction.request.headers, 'request', changes);

      // Body changes
      this.analyzeBodyChanges(prevInteraction.request.body, currInteraction.request.body, 'request', changes);
    }

    // Check response changes
    if (prevInteraction.response && currInteraction.response) {
      // Status code change can be breaking
      if (prevInteraction.response.status !== currInteraction.response.status) {
        const isBreaking = this.isStatusChangeBreaking(prevInteraction.response.status, currInteraction.response.status);

        const changeDesc = `Status changed: ${prevInteraction.response.status} -> ${currInteraction.response.status}`;

        if (isBreaking) {
          changes.breaking.push(changeDesc);
        } else {
          changes.nonBreaking.push(changeDesc);
        }
      }

      // Header changes
      this.analyzeHeaderChanges(prevInteraction.response.headers, currInteraction.response.headers, 'response', changes);

      // Body changes
      this.analyzeBodyChanges(prevInteraction.response.body, currInteraction.response.body, 'response', changes);
    }

    return changes;
  }

  analyzeHeaderChanges(prevHeaders, currHeaders, context, changes) {
    const prev = prevHeaders || {};
    const curr = currHeaders || {};

    // Removed headers (potentially breaking for response, non-breaking for request)
    for (const header of Object.keys(prev)) {
      if (!curr[header]) {
        const changeDesc = `${context} header removed: ${header}`;
        if (context === 'response') {
          changes.breaking.push(changeDesc);
        } else {
          changes.nonBreaking.push(changeDesc);
        }
      }
    }

    // Added headers (non-breaking)
    for (const header of Object.keys(curr)) {
      if (!prev[header]) {
        changes.nonBreaking.push(`${context} header added: ${header}`);
      }
    }

    // Modified headers (potentially breaking)
    for (const header of Object.keys(curr)) {
      if (prev[header] && prev[header] !== curr[header]) {
        changes.breaking.push(`${context} header modified: ${header}`);
      }
    }
  }

  analyzeBodyChanges(prevBody, currBody, context, changes) {
    if (!prevBody && !currBody) return;

    if (!prevBody && currBody) {
      changes.nonBreaking.push(`${context} body added`);
      return;
    }

    if (prevBody && !currBody) {
      changes.breaking.push(`${context} body removed`);
      return;
    }

    // For detailed body analysis, we'd need schema comparison
    // For now, just check if they're different
    const prevStr = JSON.stringify(prevBody);
    const currStr = JSON.stringify(currBody);

    if (prevStr !== currStr) {
      // This is a simplified analysis - in practice, you'd want more sophisticated schema comparison
      changes.breaking.push(`${context} body structure changed`);
    }
  }

  isStatusChangeBreaking(prevStatus, currStatus) {
    // Success to error is breaking
    if (prevStatus < 400 && currStatus >= 400) return true;

    // Error to success might be non-breaking (improvement)
    if (prevStatus >= 400 && currStatus < 400) return false;

    // Different error codes might be breaking
    if (prevStatus >= 400 && currStatus >= 400 && prevStatus !== currStatus) return true;

    return false;
  }

  analyzeMetadataChanges(prevMetadata, currMetadata, analysis) {
    const prev = prevMetadata || {};
    const curr = currMetadata || {};

    // Check Pact specification version changes
    const prevSpec = prev.pactSpecification?.version;
    const currSpec = curr.pactSpecification?.version;

    if (prevSpec !== currSpec) {
      analysis.metadataChanges.push({
        type: 'pact-spec-version',
        from: prevSpec,
        to: currSpec,
      });

      if (this.isPactSpecVersionBreaking(prevSpec, currSpec)) {
        analysis.breakingChanges.push(`Pact specification version changed: ${prevSpec} -> ${currSpec}`);
      } else {
        analysis.nonBreakingChanges.push(`Pact specification version updated: ${prevSpec} -> ${currSpec}`);
      }
    }
  }

  isPactSpecVersionBreaking(prevVersion, currVersion) {
    // Major version changes are typically breaking
    if (!prevVersion || !currVersion) return false;

    const prevMajor = parseInt(prevVersion.split('.')[0]);
    const currMajor = parseInt(currVersion.split('.')[0]);

    return currMajor > prevMajor;
  }

  getInteractionKey(interaction) {
    const request = interaction.request || {};
    return `${request.method || 'GET'}:${request.path || ''}:${interaction.description || ''}`;
  }

  calculateContractHash(contract) {
    // Remove build-specific metadata before hashing
    const contractCopy = JSON.parse(JSON.stringify(contract));
    if (contractCopy.metadata?.buildInfo) {
      delete contractCopy.metadata.buildInfo;
    }

    const contractStr = JSON.stringify(contractCopy, Object.keys(contractCopy).sort());
    return crypto.createHash('sha256').update(contractStr).digest('hex');
  }

  determineSeverity(analysis) {
    if (analysis.breakingChanges.length > 0) {
      return 'major';
    } else if (analysis.nonBreakingChanges.length > 0) {
      return 'minor';
    } else {
      return 'patch';
    }
  }

  generateChangeSummary(analysis) {
    const parts = [];

    if (analysis.breakingChanges.length > 0) {
      parts.push(`${analysis.breakingChanges.length} breaking change(s)`);
    }

    if (analysis.nonBreakingChanges.length > 0) {
      parts.push(`${analysis.nonBreakingChanges.length} non-breaking change(s)`);
    }

    if (analysis.interactionChanges.length > 0) {
      const added = analysis.interactionChanges.filter((c) => c.type === 'added').length;
      const removed = analysis.interactionChanges.filter((c) => c.type === 'removed').length;
      const modified = analysis.interactionChanges.filter((c) => c.type === 'modified').length;

      const interactionParts = [];
      if (added > 0) interactionParts.push(`${added} added`);
      if (removed > 0) interactionParts.push(`${removed} removed`);
      if (modified > 0) interactionParts.push(`${modified} modified`);

      if (interactionParts.length > 0) {
        parts.push(`interactions: ${interactionParts.join(', ')}`);
      }
    }

    return parts.join('; ') || 'No significant changes';
  }

  cacheContract(contractFile, contract) {
    const cachePath = path.join(this.cacheDir, contractFile);
    fs.writeFileSync(cachePath, JSON.stringify(contract, null, 2));
  }

  extractProviderName(contractFile) {
    return contractFile.replace('qantas-hotels-ui-', '').replace('.json', '');
  }

  async triggerProviderVerifications(changes) {
    console.log('\n🚀 Triggering provider verifications for changed contracts...');

    const verificationResults = [];

    for (const change of changes) {
      if (change.changeType === 'removed') {
        console.log(`⏭️  Skipping verification for removed contract: ${change.provider}`);
        continue;
      }

      console.log(`📤 Triggering verification for ${change.provider} (${change.severity})...`);

      try {
        const result = await this.pactBrokerClient.triggerProviderVerification(change.provider);

        verificationResults.push({
          provider: change.provider,
          success: result.success,
          error: result.error,
          changeType: change.changeType,
          severity: change.severity,
        });

        if (result.success) {
          console.log(`✅ Verification triggered for ${change.provider}`);
        } else {
          console.log(`❌ Failed to trigger verification for ${change.provider}: ${result.error}`);
        }

        // Add delay between requests to avoid overwhelming the broker
        await new Promise((resolve) => setTimeout(resolve, 1000));
      } catch (error) {
        console.log(`❌ Error triggering verification for ${change.provider}: ${error.message}`);
        verificationResults.push({
          provider: change.provider,
          success: false,
          error: error.message,
          changeType: change.changeType,
          severity: change.severity,
        });
      }
    }

    return verificationResults;
  }
}

// Main execution
async function main() {
  const detector = new ContractChangeDetector();

  try {
    const changes = await detector.detectChanges();

    // Save change detection results
    const resultsPath = path.join(__dirname, '../../tests/contracts/logs/change-detection.json');
    const results = {
      timestamp: new Date().toISOString(),
      buildNumber: BUILD_NUMBER,
      branch: BRANCH,
      commit: COMMIT,
      totalChanges: changes.length,
      breakingChanges: changes.filter((c) => c.changeType === 'breaking').length,
      changes: changes,
    };

    fs.writeFileSync(resultsPath, JSON.stringify(results, null, 2));

    console.log(`\n📊 Change Detection Summary:`);
    console.log(`   Total changes: ${results.totalChanges}`);
    console.log(`   Breaking changes: ${results.breakingChanges}`);

    if (changes.length > 0) {
      // Trigger provider verifications
      const verificationResults = await detector.triggerProviderVerifications(changes);

      // Update results with verification status
      results.verificationResults = verificationResults;
      fs.writeFileSync(resultsPath, JSON.stringify(results, null, 2));

      const successfulVerifications = verificationResults.filter((r) => r.success).length;
      console.log(`   Verifications triggered: ${successfulVerifications}/${verificationResults.length}`);

      if (successfulVerifications < verificationResults.length) {
        console.log('⚠️  Some provider verifications failed to trigger');
        process.exit(1);
      }
    }

    console.log('✅ Contract change detection and verification triggering completed!');
  } catch (error) {
    console.error('❌ Error in change detection:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { ContractChangeDetector };
