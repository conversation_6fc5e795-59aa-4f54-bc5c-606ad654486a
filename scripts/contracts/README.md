# Contract Test Automation Scripts

This directory contains scripts for automating contract testing, publishing, verification, and deployment gates in the Qantas Hotels UI CI/CD pipeline.

## Overview

The contract automation system provides:

- **Automated contract testing** with performance monitoring
- **Contract change detection** with semantic analysis
- **Provider verification triggering** when contracts change
- **Contract publishing** to shared repositories or Pact Broker
- **Deployment gates** to prevent incompatible deployments
- **Compatibility checking** before deployment
- **Team notifications** when contracts change

## Scripts

### Core Scripts

#### `publish-contracts.js`
Publishes generated Pact contracts to a shared location for provider teams.

```bash
yarn contracts:publish
```

**Features:**
- Copies contracts to shared directory with build metadata
- Creates versioned contract files
- Generates contract registry for tracking
- Outputs build annotations for CI/CD

#### `verify-contracts.js`
Detects contract changes and validates them against previous versions.

```bash
yarn contracts:verify
```

**Features:**
- Compares contracts with cached versions
- Detects new, modified, and removed contracts
- Identifies potential breaking changes
- Caches contracts for future comparisons

#### `change-detector.js`
Advanced contract change detection with semantic analysis.

```bash
yarn contracts:detect
```

**Features:**
- Semantic analysis of contract changes
- Breaking vs non-breaking change classification
- Interaction-level change detection
- Automatic provider verification triggering

#### `pact-broker-client.js`
Client for interacting with Pact Broker for contract publishing and verification.

```bash
# Publish all contracts
yarn contracts:broker publish

# Trigger provider verification
yarn contracts:broker verify <provider-name>

# Get verification results
yarn contracts:broker results <provider-name>

# Create webhook
yarn contracts:broker webhook <provider-name>
```

#### `compatibility-checker.js`
Checks contract compatibility before deployment.

```bash
yarn contracts:compatibility
```

**Features:**
- Verifies provider compatibility with current contracts
- Checks verification results from Pact Broker
- Uses cached compatibility status when available
- Performs basic contract validation as fallback

#### `deployment-gate.js`
Acts as a deployment gate, blocking deployments with incompatible contracts.

```bash
yarn contracts:gate
```

**Features:**
- Blocks deployment if breaking changes aren't verified
- Allows deployment for verified changes
- Provides detailed blocking reasons
- Supports environment-specific rules

#### `monitor-performance.js`
Monitors contract test performance and generates reports.

```bash
yarn contracts:monitor
```

**Features:**
- Tracks test execution times
- Compares with E2E test performance
- Detects performance regressions
- Generates trend analysis

#### `notify-changes.js`
Sends notifications to provider teams when contracts change.

```bash
yarn contracts:notify
```

**Features:**
- Maps providers to team contacts
- Sends notifications via multiple channels
- Prioritizes breaking changes
- Tracks notification history

## Package.json Scripts

### Individual Scripts
- `contracts:publish` - Publish contracts to shared location
- `contracts:verify` - Verify contract changes
- `contracts:monitor` - Monitor performance
- `contracts:notify` - Send change notifications
- `contracts:gate` - Run deployment gate
- `contracts:broker` - Interact with Pact Broker
- `contracts:detect` - Advanced change detection
- `contracts:compatibility` - Check compatibility

### Composite Scripts
- `contracts:ci` - Full CI pipeline (test → detect → publish → monitor → notify)
- `contracts:deploy-check` - Pre-deployment checks (compatibility → gate)

## CI/CD Integration

### Buildkite Pipeline Integration

The contract automation is integrated into the Buildkite pipeline with these steps:

1. **Contract Tests** - Runs the full contract CI pipeline
2. **Contract Deployment Gate** - Checks compatibility before deployment

### Environment Variables

Configure these environment variables for full functionality:

#### Pact Broker Configuration
```bash
PACT_BROKER_URL=https://your-pact-broker.com
PACT_BROKER_TOKEN=your-broker-token
```

#### Provider Webhook Configuration
```bash
PROVIDER_WEBHOOK_BASE_URL=https://api.your-providers.com
PROVIDER_WEBHOOK_TOKEN=webhook-auth-token

# Provider-specific webhook URLs
HOTELS_API_WEBHOOK_URL=https://hotels-api.com/webhooks/contract-changed
AUTH_API_WEBHOOK_URL=https://auth-api.com/webhooks/contract-changed
SANITY_WEBHOOK_URL=https://sanity-cms.com/webhooks/contract-changed
PAYMENTS_WEBHOOK_URL=https://payments.com/webhooks/contract-changed
GEOLOCATION_WEBHOOK_URL=https://geolocation.com/webhooks/contract-changed
```

#### Shared Contracts Directory
```bash
SHARED_CONTRACTS_DIR=/path/to/shared/contracts
PREVIOUS_CONTRACTS_DIR=/path/to/contract/cache
```

## File Structure

```
scripts/contracts/
├── README.md                    # This file
├── publish-contracts.js         # Contract publishing
├── verify-contracts.js          # Contract verification
├── change-detector.js           # Advanced change detection
├── pact-broker-client.js        # Pact Broker integration
├── compatibility-checker.js     # Compatibility checking
├── deployment-gate.js           # Deployment gate
├── monitor-performance.js       # Performance monitoring
└── notify-changes.js           # Team notifications

tests/contracts/logs/            # Generated reports and logs
├── verification-report.json     # Contract change report
├── change-detection.json        # Semantic change analysis
├── compatibility-check.json     # Compatibility results
├── deployment-decision.json     # Deployment gate decision
├── performance-*.json          # Performance reports
└── notification-summary.json   # Notification tracking

.contract-cache/                 # Cached contract data
├── compatibility-status.json    # Provider compatibility cache
└── *.json                      # Cached contract files

shared-contracts/               # Published contracts
├── contract-registry.json      # Contract registry
└── *.json                     # Shared contract files
```

## Usage Examples

### Local Development

```bash
# Run contract tests with full automation
yarn contracts:ci

# Check if deployment would be allowed
yarn contracts:deploy-check

# Manually trigger provider verification
yarn contracts:broker verify hotels-api

# Check compatibility status
yarn contracts:compatibility
```

### CI/CD Pipeline

The scripts are automatically executed in the Buildkite pipeline:

1. **On every build**: Contract tests run with change detection and publishing
2. **Before deployment**: Compatibility check and deployment gate
3. **On contract changes**: Provider teams are automatically notified

### Provider Team Integration

Provider teams can integrate with the contract automation by:

1. **Setting up webhooks** to receive contract change notifications
2. **Running provider verification** when contracts change
3. **Updating compatibility status** after successful verification

## Troubleshooting

### Common Issues

#### No Contract Changes Detected
- Check that contract files are being generated in `tests/contracts/pacts/`
- Verify that the contract cache directory exists and is writable
- Ensure contract tests are running successfully

#### Provider Verification Fails
- Check Pact Broker connectivity and authentication
- Verify provider webhook URLs are correct
- Ensure provider services are accessible

#### Deployment Gate Blocks Deployment
- Check compatibility check results in the logs
- Verify that provider teams have verified breaking changes
- Update compatibility cache if verification is complete

#### Performance Monitoring Issues
- Ensure Jest is configured to output results JSON
- Check that performance log directory is writable
- Verify that test execution times are being captured

### Debug Mode

Run scripts with additional logging:

```bash
DEBUG=1 yarn contracts:ci
```

### Log Files

Check these log files for detailed information:

- `tests/contracts/logs/verification-report.json` - Contract changes
- `tests/contracts/logs/compatibility-check.json` - Compatibility status
- `tests/contracts/logs/deployment-decision.json` - Deployment decisions
- `tests/contracts/logs/performance-*.json` - Performance data

## Configuration

### Provider Team Mapping

Update the provider team mapping in `notify-changes.js`:

```javascript
const providerTeamMapping = {
  'hotels-api': {
    team: 'backend-hotels',
    slack: '#team-hotels-api',
    email: '<EMAIL>',
    webhook: process.env.HOTELS_API_WEBHOOK_URL
  },
  // Add more providers...
};
```

### Deployment Rules

Customize deployment rules in `deployment-gate.js`:

```javascript
// Skip gate for non-production environments
if (BRANCH !== 'master' && ENVIRONMENT !== 'production') {
  console.log('✅ Skipping deployment gate for non-production deployment');
  process.exit(0);
}
```

## Best Practices

1. **Run contract tests early** in the CI pipeline to get fast feedback
2. **Cache compatibility status** to avoid repeated checks
3. **Set up proper notifications** to ensure provider teams are informed
4. **Monitor performance trends** to catch regressions early
5. **Use semantic versioning** for contract changes
6. **Document breaking changes** clearly for provider teams

## Support

For issues with the contract automation system:

1. Check the troubleshooting section above
2. Review log files for detailed error information
3. Contact the Hotels UI team in #team-bellhops
4. Create an issue in the project repository