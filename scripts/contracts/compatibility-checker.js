#!/usr/bin/env node

/**
 * Contract Compatibility Checker
 *
 * This script checks contract compatibility before deployment by verifying
 * that all provider services can fulfill the current contracts.
 */

const fs = require('fs');
const path = require('path');
const { PactBrokerClient } = require('./pact-broker-client');

const BUILD_NUMBER = process.env.BUILDKITE_BUILD_NUMBER || 'local';
const BRANCH = process.env.BUILDKITE_BRANCH || 'unknown';
const ENVIRONMENT = process.env.ENVIRONMENT || 'unknown';
const COMPATIBILITY_CACHE_PATH = path.join(__dirname, '../../.contract-cache/compatibility-status.json');

class CompatibilityChecker {
  constructor() {
    this.pactBrokerClient = new PactBrokerClient(process.env.PACT_BROKER_URL || 'http://localhost:9292', process.env.PACT_BROKER_TOKEN);

    this.compatibilityCache = this.loadCompatibilityCache();
  }

  loadCompatibilityCache() {
    if (fs.existsSync(COMPATIBILITY_CACHE_PATH)) {
      try {
        return JSON.parse(fs.readFileSync(COMPATIBILITY_CACHE_PATH, 'utf8'));
      } catch (error) {
        console.log('⚠️  Could not load compatibility cache:', error.message);
      }
    }

    return {
      lastUpdated: null,
      verifiedContracts: {},
      providerStatus: {},
    };
  }

  saveCompatibilityCache() {
    const cacheDir = path.dirname(COMPATIBILITY_CACHE_PATH);
    if (!fs.existsSync(cacheDir)) {
      fs.mkdirSync(cacheDir, { recursive: true });
    }

    this.compatibilityCache.lastUpdated = new Date().toISOString();
    fs.writeFileSync(COMPATIBILITY_CACHE_PATH, JSON.stringify(this.compatibilityCache, null, 2));
  }

  async checkCompatibility() {
    console.log('🔍 Checking contract compatibility...');
    console.log(`Build: ${BUILD_NUMBER}, Branch: ${BRANCH}, Environment: ${ENVIRONMENT}`);

    // Load current contracts
    const contracts = this.loadCurrentContracts();

    if (Object.keys(contracts).length === 0) {
      console.log('⚠️  No contracts found to check');
      return { compatible: true, results: [] };
    }

    const compatibilityResults = [];

    for (const [contractFile, contract] of Object.entries(contracts)) {
      const provider = this.extractProviderName(contractFile);
      console.log(`\n🔍 Checking compatibility for ${provider}...`);

      const result = await this.checkProviderCompatibility(provider, contract, contractFile);
      compatibilityResults.push(result);

      // Update cache with results
      this.updateProviderStatus(provider, result);
    }

    // Save updated cache
    this.saveCompatibilityCache();

    // Determine overall compatibility
    const incompatibleProviders = compatibilityResults.filter((r) => !r.compatible);
    const overallCompatible = incompatibleProviders.length === 0;

    return {
      compatible: overallCompatible,
      results: compatibilityResults,
      incompatibleProviders: incompatibleProviders.map((r) => r.provider),
      summary: this.generateCompatibilitySummary(compatibilityResults),
    };
  }

  async checkProviderCompatibility(provider, contract, contractFile) {
    const result = {
      provider,
      contractFile,
      compatible: false,
      verificationStatus: 'unknown',
      lastVerified: null,
      verificationResults: null,
      issues: [],
      warnings: [],
    };

    try {
      // Check if we have recent verification results
      const verificationResults = await this.pactBrokerClient.getProviderVerificationResults(provider, BUILD_NUMBER);

      if (verificationResults.success && verificationResults.results) {
        result.verificationResults = verificationResults.results;
        result.verificationStatus = this.parseVerificationStatus(verificationResults.results);
        result.lastVerified = verificationResults.results.verifiedAt || null;

        if (result.verificationStatus === 'passed') {
          result.compatible = true;
          console.log(`✅ ${provider}: Verification passed`);
        } else {
          result.compatible = false;
          result.issues.push(`Provider verification failed: ${result.verificationStatus}`);
          console.log(`❌ ${provider}: Verification failed - ${result.verificationStatus}`);
        }
      } else {
        // No verification results available - check cache
        const cachedStatus = this.compatibilityCache.providerStatus[provider];

        if (cachedStatus) {
          const cacheAge = Date.now() - new Date(cachedStatus.lastChecked).getTime();
          const maxCacheAge = 24 * 60 * 60 * 1000; // 24 hours

          if (cacheAge < maxCacheAge && cachedStatus.compatible) {
            result.compatible = true;
            result.verificationStatus = 'cached';
            result.lastVerified = cachedStatus.lastVerified;
            result.warnings.push(`Using cached compatibility status (${Math.round(cacheAge / (60 * 60 * 1000))} hours old)`);
            console.log(`📋 ${provider}: Using cached compatibility status`);
          } else {
            result.compatible = false;
            result.issues.push('No recent verification results available');
            console.log(`⚠️  ${provider}: No recent verification results`);
          }
        } else {
          // No cache either - perform basic contract validation
          const basicValidation = this.performBasicContractValidation(contract);

          if (basicValidation.valid) {
            result.compatible = true;
            result.verificationStatus = 'basic-validation';
            result.warnings.push('Compatibility based on basic contract validation only');
            console.log(`⚠️  ${provider}: Basic validation passed (no provider verification available)`);
          } else {
            result.compatible = false;
            result.issues.push(...basicValidation.issues);
            console.log(`❌ ${provider}: Basic validation failed`);
          }
        }
      }
    } catch (error) {
      result.compatible = false;
      result.issues.push(`Error checking compatibility: ${error.message}`);
      console.log(`❌ ${provider}: Error - ${error.message}`);
    }

    return result;
  }

  parseVerificationStatus(verificationResults) {
    // This would parse the actual verification results from the Pact Broker
    // For now, we'll simulate the parsing
    if (verificationResults.success === true) {
      return 'passed';
    } else if (verificationResults.success === false) {
      return 'failed';
    } else {
      return 'pending';
    }
  }

  performBasicContractValidation(contract) {
    const validation = {
      valid: true,
      issues: [],
    };

    // Check basic contract structure
    if (!contract.consumer || !contract.consumer.name) {
      validation.valid = false;
      validation.issues.push('Contract missing consumer information');
    }

    if (!contract.provider || !contract.provider.name) {
      validation.valid = false;
      validation.issues.push('Contract missing provider information');
    }

    if (!contract.interactions || !Array.isArray(contract.interactions)) {
      validation.valid = false;
      validation.issues.push('Contract missing or invalid interactions');
    } else if (contract.interactions.length === 0) {
      validation.valid = false;
      validation.issues.push('Contract has no interactions defined');
    }

    // Validate each interaction
    for (let i = 0; i < contract.interactions.length; i++) {
      const interaction = contract.interactions[i];

      if (!interaction.description) {
        validation.issues.push(`Interaction ${i + 1} missing description`);
      }

      if (!interaction.request) {
        validation.valid = false;
        validation.issues.push(`Interaction ${i + 1} missing request specification`);
      } else {
        if (!interaction.request.method) {
          validation.issues.push(`Interaction ${i + 1} missing HTTP method`);
        }

        if (!interaction.request.path) {
          validation.issues.push(`Interaction ${i + 1} missing request path`);
        }
      }

      if (!interaction.response) {
        validation.valid = false;
        validation.issues.push(`Interaction ${i + 1} missing response specification`);
      } else {
        if (typeof interaction.response.status !== 'number') {
          validation.issues.push(`Interaction ${i + 1} missing or invalid response status`);
        }
      }
    }

    return validation;
  }

  updateProviderStatus(provider, result) {
    this.compatibilityCache.providerStatus[provider] = {
      compatible: result.compatible,
      lastChecked: new Date().toISOString(),
      lastVerified: result.lastVerified,
      verificationStatus: result.verificationStatus,
      issues: result.issues,
      warnings: result.warnings,
    };
  }

  generateCompatibilitySummary(results) {
    const total = results.length;
    const compatible = results.filter((r) => r.compatible).length;
    const incompatible = total - compatible;

    const verificationMethods = {};
    results.forEach((r) => {
      verificationMethods[r.verificationStatus] = (verificationMethods[r.verificationStatus] || 0) + 1;
    });

    return {
      total,
      compatible,
      incompatible,
      compatibilityRate: Math.round((compatible / total) * 100),
      verificationMethods,
    };
  }

  loadCurrentContracts() {
    const contracts = {};
    const pactsDir = path.join(__dirname, '../../tests/contracts/pacts');

    if (!fs.existsSync(pactsDir)) {
      return contracts;
    }

    const pactFiles = fs
      .readdirSync(pactsDir)
      .filter((file) => file.endsWith('.json'))
      .filter((file) => !file.includes('setup-verification'));

    for (const file of pactFiles) {
      try {
        const contractPath = path.join(pactsDir, file);
        const contractContent = fs.readFileSync(contractPath, 'utf8');
        contracts[file] = JSON.parse(contractContent);
      } catch (error) {
        console.log(`⚠️  Error loading contract ${file}: ${error.message}`);
      }
    }

    return contracts;
  }

  extractProviderName(contractFile) {
    return contractFile.replace('qantas-hotels-ui-', '').replace('.json', '');
  }
}

// Main execution
async function main() {
  const checker = new CompatibilityChecker();

  try {
    const compatibilityResults = await checker.checkCompatibility();

    // Save results
    const resultsPath = path.join(__dirname, '../../tests/contracts/logs/compatibility-check.json');
    const results = {
      timestamp: new Date().toISOString(),
      buildNumber: BUILD_NUMBER,
      branch: BRANCH,
      environment: ENVIRONMENT,
      compatible: compatibilityResults.compatible,
      summary: compatibilityResults.summary,
      results: compatibilityResults.results,
      incompatibleProviders: compatibilityResults.incompatibleProviders,
    };

    fs.writeFileSync(resultsPath, JSON.stringify(results, null, 2));

    // Output summary
    console.log('\n📊 Compatibility Check Summary:');
    console.log(`   Total providers: ${compatibilityResults.summary.total}`);
    console.log(`   Compatible: ${compatibilityResults.summary.compatible}`);
    console.log(`   Incompatible: ${compatibilityResults.summary.incompatible}`);
    console.log(`   Compatibility rate: ${compatibilityResults.summary.compatibilityRate}%`);

    if (compatibilityResults.incompatibleProviders.length > 0) {
      console.log('\n❌ Incompatible providers:');
      compatibilityResults.incompatibleProviders.forEach((provider) => {
        console.log(`   - ${provider}`);
      });

      console.log('\n📋 Detailed issues:');
      compatibilityResults.results
        .filter((r) => !r.compatible)
        .forEach((result) => {
          console.log(`\n   ${result.provider}:`);
          result.issues.forEach((issue) => {
            console.log(`     - ${issue}`);
          });
        });
    }

    // Output for build annotation
    if (process.env.BUILDKITE) {
      const annotation = generateBuildAnnotation(results);
      console.log('\n--- Compatibility Check Annotation ---');
      console.log(annotation);
    }

    if (!compatibilityResults.compatible) {
      console.log('\n❌ Compatibility check failed - deployment should be blocked');
      process.exit(1);
    }

    console.log('\n✅ All contracts are compatible - deployment can proceed');
  } catch (error) {
    console.error('❌ Error in compatibility check:', error);
    process.exit(1);
  }
}

function generateBuildAnnotation(results) {
  const statusIcon = results.compatible ? '✅' : '❌';
  const statusText = results.compatible ? 'COMPATIBLE' : 'INCOMPATIBLE';

  let incompatibleSection = '';
  if (results.incompatibleProviders.length > 0) {
    incompatibleSection = `

### Incompatible Providers ❌
${results.incompatibleProviders.map((provider) => `- **${provider}**`).join('\n')}

### Issues
${results.results
  .filter((r) => !r.compatible)
  .map((r) => `**${r.provider}:**\n${r.issues.map((issue) => `  - ${issue}`).join('\n')}`)
  .join('\n\n')}`;
  }

  return `## 🔍 Contract Compatibility Check ${statusIcon}

**Status:** ${statusText}  
**Build:** ${results.buildNumber}  
**Branch:** ${results.branch}  
**Environment:** ${results.environment}

### Summary
- **Total Providers:** ${results.summary.total}
- **Compatible:** ${results.summary.compatible}
- **Incompatible:** ${results.summary.incompatible}
- **Compatibility Rate:** ${results.summary.compatibilityRate}%${incompatibleSection}

${
  results.compatible
    ? '**Deployment approved** - all contracts are compatible'
    : '**Deployment blocked** - resolve compatibility issues before proceeding'
}`;
}

if (require.main === module) {
  main();
}

module.exports = { CompatibilityChecker };
