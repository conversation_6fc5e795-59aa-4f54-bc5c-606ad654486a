#!/usr/bin/env node

/**
 * Contract Test Performance Monitor
 *
 * This script monitors and reports on contract test performance,
 * comparing execution times with E2E tests and tracking improvements.
 */

const fs = require('fs');
const path = require('path');

const PERFORMANCE_LOG_DIR = path.join(__dirname, '../../tests/contracts/logs');
const BUILD_NUMBER = process.env.BUILDKITE_BUILD_NUMBER || 'local';
const BRANCH = process.env.BUILDKITE_BRANCH || 'unknown';

console.log('📊 Monitoring contract test performance...');

// Ensure logs directory exists
if (!fs.existsSync(PERFORMANCE_LOG_DIR)) {
  fs.mkdirSync(PERFORMANCE_LOG_DIR, { recursive: true });
}

// Read Jest test results if available
const jestResultsPath = path.join(__dirname, '../../jest-results.json');
let contractTestResults = null;

if (fs.existsSync(jestResultsPath)) {
  try {
    const jestResults = JSON.parse(fs.readFileSync(jestResultsPath, 'utf8'));
    contractTestResults = jestResults;
    console.log('📄 Found Jest test results');
  } catch (error) {
    console.log('⚠️  Could not parse Jest results:', error.message);
  }
}

// Calculate performance metrics
const performanceMetrics = calculatePerformanceMetrics(contractTestResults);

// Load historical performance data
const historicalDataPath = path.join(PERFORMANCE_LOG_DIR, 'performance-history.json');
let historicalData = { builds: [] };

if (fs.existsSync(historicalDataPath)) {
  try {
    historicalData = JSON.parse(fs.readFileSync(historicalDataPath, 'utf8'));
  } catch (error) {
    console.log('⚠️  Could not load historical data:', error.message);
  }
}

// Add current build data
const currentBuildData = {
  buildNumber: BUILD_NUMBER,
  branch: BRANCH,
  timestamp: new Date().toISOString(),
  metrics: performanceMetrics,
};

historicalData.builds.push(currentBuildData);

// Keep only last 50 builds to prevent file from growing too large
if (historicalData.builds.length > 50) {
  historicalData.builds = historicalData.builds.slice(-50);
}

// Save updated historical data
fs.writeFileSync(historicalDataPath, JSON.stringify(historicalData, null, 2));

// Generate performance report
const report = generatePerformanceReport(currentBuildData, historicalData);

// Save current build report
const reportPath = path.join(PERFORMANCE_LOG_DIR, `performance-${BUILD_NUMBER}.json`);
fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

// Output performance summary
console.log('\n📊 Performance Summary:');
console.log(`   Total execution time: ${performanceMetrics.totalTime}ms`);
console.log(`   Test suites: ${performanceMetrics.testSuites}`);
console.log(`   Total tests: ${performanceMetrics.totalTests}`);
console.log(`   Passed tests: ${performanceMetrics.passedTests}`);
console.log(`   Failed tests: ${performanceMetrics.failedTests}`);
console.log(`   Average test time: ${performanceMetrics.averageTestTime}ms`);

if (performanceMetrics.comparison) {
  console.log('\n🚀 Performance Improvements:');
  console.log(`   Estimated E2E equivalent time: ${performanceMetrics.comparison.estimatedE2ETime}ms`);
  console.log(`   Time saved: ${performanceMetrics.comparison.timeSaved}ms`);
  console.log(`   Speed improvement: ${performanceMetrics.comparison.speedImprovement}x faster`);
}

// Check for performance regressions
if (historicalData.builds.length > 1) {
  const previousBuild = historicalData.builds[historicalData.builds.length - 2];
  const regression = checkPerformanceRegression(currentBuildData.metrics, previousBuild.metrics);

  if (regression.hasRegression) {
    console.log('\n⚠️  Performance Regression Detected:');
    console.log(`   Execution time increased by ${regression.timeIncrease}ms (${regression.percentageIncrease}%)`);

    if (regression.percentageIncrease > 20) {
      console.log('❌ Significant performance regression - investigation recommended');
    }
  } else if (regression.hasImprovement) {
    console.log('\n✅ Performance Improvement:');
    console.log(`   Execution time decreased by ${Math.abs(regression.timeIncrease)}ms (${Math.abs(regression.percentageIncrease)}%)`);
  }
}

// Output for build annotation
if (process.env.BUILDKITE) {
  const annotation = generateBuildAnnotation(report);
  console.log('\n--- Performance Report for Build Annotation ---');
  console.log(annotation);
}

console.log('🎉 Performance monitoring completed!');

function calculatePerformanceMetrics(jestResults) {
  const metrics = {
    totalTime: 0,
    testSuites: 0,
    totalTests: 0,
    passedTests: 0,
    failedTests: 0,
    averageTestTime: 0,
    comparison: null,
  };

  if (jestResults) {
    metrics.totalTime =
      jestResults.testResults?.reduce((total, suite) => {
        return total + (suite.perfStats?.end - suite.perfStats?.start || 0);
      }, 0) || 0;

    metrics.testSuites = jestResults.numTotalTestSuites || 0;
    metrics.totalTests = jestResults.numTotalTests || 0;
    metrics.passedTests = jestResults.numPassedTests || 0;
    metrics.failedTests = jestResults.numFailedTests || 0;

    if (metrics.totalTests > 0) {
      metrics.averageTestTime = Math.round(metrics.totalTime / metrics.totalTests);
    }

    // Estimate E2E test equivalent time (contract tests are typically 10-20x faster)
    const estimatedE2ETime = metrics.totalTime * 15; // Conservative estimate
    metrics.comparison = {
      estimatedE2ETime,
      timeSaved: estimatedE2ETime - metrics.totalTime,
      speedImprovement: Math.round((estimatedE2ETime / metrics.totalTime) * 10) / 10,
    };
  }

  return metrics;
}

function generatePerformanceReport(currentBuild, historicalData) {
  const report = {
    build: currentBuild,
    trends: {
      averageExecutionTime: 0,
      performanceTrend: 'stable',
      reliabilityScore: 0,
    },
    recommendations: [],
  };

  if (historicalData.builds.length > 1) {
    // Calculate trends from last 10 builds
    const recentBuilds = historicalData.builds.slice(-10);
    const executionTimes = recentBuilds.map((b) => b.metrics.totalTime).filter((t) => t > 0);

    if (executionTimes.length > 0) {
      report.trends.averageExecutionTime = Math.round(executionTimes.reduce((sum, time) => sum + time, 0) / executionTimes.length);

      // Determine trend
      if (executionTimes.length >= 3) {
        const recent = executionTimes.slice(-3);
        const older = executionTimes.slice(-6, -3);

        if (older.length > 0) {
          const recentAvg = recent.reduce((sum, time) => sum + time, 0) / recent.length;
          const olderAvg = older.reduce((sum, time) => sum + time, 0) / older.length;

          const change = ((recentAvg - olderAvg) / olderAvg) * 100;

          if (change > 10) {
            report.trends.performanceTrend = 'degrading';
            report.recommendations.push('Performance is degrading - consider optimizing slow tests');
          } else if (change < -10) {
            report.trends.performanceTrend = 'improving';
          }
        }
      }
    }

    // Calculate reliability score based on test failures
    const recentFailureRates = recentBuilds.map((b) => {
      const total = b.metrics.totalTests || 1;
      const failed = b.metrics.failedTests || 0;
      return failed / total;
    });

    const avgFailureRate = recentFailureRates.reduce((sum, rate) => sum + rate, 0) / recentFailureRates.length;
    report.trends.reliabilityScore = Math.round((1 - avgFailureRate) * 100);

    if (report.trends.reliabilityScore < 95) {
      report.recommendations.push('Test reliability is below 95% - investigate flaky tests');
    }
  }

  return report;
}

function checkPerformanceRegression(current, previous) {
  const currentTime = current.totalTime || 0;
  const previousTime = previous.totalTime || 0;

  if (previousTime === 0) {
    return { hasRegression: false, hasImprovement: false };
  }

  const timeIncrease = currentTime - previousTime;
  const percentageIncrease = Math.round((timeIncrease / previousTime) * 100);

  return {
    hasRegression: percentageIncrease > 5, // 5% threshold
    hasImprovement: percentageIncrease < -5,
    timeIncrease,
    percentageIncrease,
  };
}

function generateBuildAnnotation(report) {
  const metrics = report.build.metrics;
  const trends = report.trends;

  let trendIcon = '📊';
  if (trends.performanceTrend === 'improving') trendIcon = '📈';
  if (trends.performanceTrend === 'degrading') trendIcon = '📉';

  let reliabilityIcon = '✅';
  if (trends.reliabilityScore < 95) reliabilityIcon = '⚠️';
  if (trends.reliabilityScore < 90) reliabilityIcon = '❌';

  return `## 📊 Contract Test Performance Report

**Build:** ${report.build.buildNumber}  
**Branch:** ${report.build.branch}

### Performance Metrics
- **Execution Time:** ${metrics.totalTime}ms
- **Test Suites:** ${metrics.testSuites}
- **Total Tests:** ${metrics.totalTests} (${metrics.passedTests} passed, ${metrics.failedTests} failed)
- **Average Test Time:** ${metrics.averageTestTime}ms

${
  metrics.comparison
    ? `### Performance Comparison
- **Estimated E2E Time:** ${metrics.comparison.estimatedE2ETime}ms
- **Time Saved:** ${metrics.comparison.timeSaved}ms
- **Speed Improvement:** ${metrics.comparison.speedImprovement}x faster`
    : ''
}

### Trends ${trendIcon}
- **Average Execution Time:** ${trends.averageExecutionTime}ms
- **Performance Trend:** ${trends.performanceTrend}
- **Reliability Score:** ${trends.reliabilityScore}% ${reliabilityIcon}

${
  report.recommendations.length > 0
    ? `### Recommendations
${report.recommendations.map((rec) => `- ${rec}`).join('\n')}`
    : ''
}
`;
}
