#!/usr/bin/env node

/**
 * Contract Deployment Gate
 *
 * This script acts as a deployment gate, checking contract compatibility
 * before allowing deployment to proceed. It ensures that breaking contract
 * changes have been verified by provider teams.
 */

const fs = require('fs');
const path = require('path');

const VERIFICATION_REPORT_PATH = path.join(__dirname, '../../tests/contracts/logs/verification-report.json');
const COMPATIBILITY_CACHE_PATH = path.join(__dirname, '../../.contract-cache/compatibility-status.json');
const BUILD_NUMBER = process.env.BUILDKITE_BUILD_NUMBER || 'local';
const BRANCH = process.env.BUILDKITE_BRANCH || 'unknown';
const ENVIRONMENT = process.env.ENVIRONMENT || 'unknown';

console.log('🚪 Contract deployment gate check...');
console.log(`Build: ${BUILD_NUMBER}, Branch: ${BRANCH}, Environment: ${ENVIRONMENT}`);

// Skip gate for non-production environments in development branches
if (BRANCH !== 'master' && ENVIRONMENT !== 'production') {
  console.log('✅ Skipping deployment gate for non-production deployment');
  process.exit(0);
}

// Read verification report
if (!fs.existsSync(VERIFICATION_REPORT_PATH)) {
  console.log('⚠️  No verification report found');

  // For master branch, require verification report
  if (BRANCH === 'master') {
    console.log('❌ Verification report required for master branch deployment');
    process.exit(1);
  }

  console.log('✅ No verification report needed for this branch');
  process.exit(0);
}

let verificationReport;
try {
  verificationReport = JSON.parse(fs.readFileSync(VERIFICATION_REPORT_PATH, 'utf8'));
} catch (error) {
  console.log('❌ Error reading verification report:', error.message);
  process.exit(1);
}

const { changes } = verificationReport;

if (!changes || changes.length === 0) {
  console.log('✅ No contract changes detected - deployment approved');
  process.exit(0);
}

console.log(`📋 Checking ${changes.length} contract changes...`);

// Load compatibility status cache
let compatibilityStatus = { verifiedContracts: {} };
if (fs.existsSync(COMPATIBILITY_CACHE_PATH)) {
  try {
    compatibilityStatus = JSON.parse(fs.readFileSync(COMPATIBILITY_CACHE_PATH, 'utf8'));
  } catch (error) {
    console.log('⚠️  Could not load compatibility cache:', error.message);
  }
}

// Check each contract change
const deploymentBlocks = [];
const warnings = [];

changes.forEach((change) => {
  const contractKey = `${change.provider}-${change.hash}`;
  const verification = compatibilityStatus.verifiedContracts[contractKey];

  console.log(`\n🔍 Checking ${change.provider} (${change.type}):`);

  if (change.type === 'breaking') {
    if (verification && verification.status === 'verified' && verification.timestamp) {
      const verificationAge = Date.now() - new Date(verification.timestamp).getTime();
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours

      if (verificationAge < maxAge) {
        console.log(`   ✅ Breaking change verified by ${verification.verifiedBy} at ${verification.timestamp}`);
      } else {
        console.log(`   ⚠️  Verification is stale (${Math.round(verificationAge / (60 * 60 * 1000))} hours old)`);
        warnings.push({
          provider: change.provider,
          issue: 'Stale verification',
          age: Math.round(verificationAge / (60 * 60 * 1000)),
        });
      }
    } else {
      console.log(`   ❌ Breaking change not verified by provider team`);
      deploymentBlocks.push({
        provider: change.provider,
        reason: 'Breaking contract change not verified',
        changeType: change.type,
      });
    }
  } else if (change.type === 'new') {
    console.log(`   ℹ️  New contract - provider verification recommended but not required`);
    warnings.push({
      provider: change.provider,
      issue: 'New contract should be verified',
      changeType: change.type,
    });
  } else {
    console.log(`   ✅ Non-breaking change - deployment approved`);
  }
});

// Generate deployment decision
const deploymentDecision = {
  timestamp: new Date().toISOString(),
  buildNumber: BUILD_NUMBER,
  branch: BRANCH,
  environment: ENVIRONMENT,
  approved: deploymentBlocks.length === 0,
  blocks: deploymentBlocks,
  warnings: warnings,
  contractChanges: changes.length,
  breakingChanges: changes.filter((c) => c.type === 'breaking').length,
};

// Save deployment decision
const decisionPath = path.join(__dirname, '../../tests/contracts/logs/deployment-decision.json');
fs.writeFileSync(decisionPath, JSON.stringify(deploymentDecision, null, 2));

// Output results
console.log('\n🚪 Deployment Gate Results:');
console.log(`   Contract changes: ${deploymentDecision.contractChanges}`);
console.log(`   Breaking changes: ${deploymentDecision.breakingChanges}`);
console.log(`   Deployment blocks: ${deploymentDecision.blocks.length}`);
console.log(`   Warnings: ${deploymentDecision.warnings.length}`);

if (deploymentDecision.blocks.length > 0) {
  console.log('\n❌ Deployment BLOCKED:');
  deploymentDecision.blocks.forEach((block) => {
    console.log(`   - ${block.provider}: ${block.reason}`);
  });

  console.log('\n📋 To resolve:');
  console.log('   1. Contact the provider teams to verify contract changes');
  console.log('   2. Update compatibility status once verification is complete');
  console.log('   3. Re-run deployment');

  // Output for build annotation
  if (process.env.BUILDKITE) {
    const annotation = generateBlockedDeploymentAnnotation(deploymentDecision);
    console.log('\n--- Deployment Gate Block Annotation ---');
    console.log(annotation);
  }

  process.exit(1);
}

if (deploymentDecision.warnings.length > 0) {
  console.log('\n⚠️  Deployment APPROVED with warnings:');
  deploymentDecision.warnings.forEach((warning) => {
    console.log(`   - ${warning.provider}: ${warning.issue}`);
  });
}

console.log('\n✅ Deployment APPROVED - all contract changes verified');

// Output for build annotation
if (process.env.BUILDKITE) {
  const annotation = generateApprovedDeploymentAnnotation(deploymentDecision);
  console.log('\n--- Deployment Gate Approval Annotation ---');
  console.log(annotation);
}

console.log('🎉 Contract deployment gate check completed!');

function generateBlockedDeploymentAnnotation(decision) {
  return `## 🚪❌ Deployment Gate: BLOCKED

**Build:** ${decision.buildNumber}  
**Branch:** ${decision.branch}  
**Environment:** ${decision.environment}

### Blocking Issues
${decision.blocks.map((block) => `- **${block.provider}**: ${block.reason}`).join('\n')}

### Contract Changes Summary
- **Total Changes:** ${decision.contractChanges}
- **Breaking Changes:** ${decision.breakingChanges}
- **Warnings:** ${decision.warnings.length}

### Resolution Required
1. Contact provider teams to verify breaking contract changes
2. Update compatibility status once verification is complete
3. Re-run deployment pipeline

**Deployment cannot proceed until all blocking issues are resolved.**
`;
}

function generateApprovedDeploymentAnnotation(decision) {
  const warningSection =
    decision.warnings.length > 0
      ? `

### Warnings ⚠️
${decision.warnings.map((w) => `- **${w.provider}**: ${w.issue}`).join('\n')}`
      : '';

  return `## 🚪✅ Deployment Gate: APPROVED

**Build:** ${decision.buildNumber}  
**Branch:** ${decision.branch}  
**Environment:** ${decision.environment}

### Contract Changes Summary
- **Total Changes:** ${decision.contractChanges}
- **Breaking Changes:** ${decision.breakingChanges} (all verified)
- **Warnings:** ${decision.warnings.length}${warningSection}

**Deployment approved - all contract compatibility requirements met.**
`;
}
