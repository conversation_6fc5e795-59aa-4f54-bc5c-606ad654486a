#!/usr/bin/env node

/**
 * Contract Publishing Script
 *
 * This script publishes generated Pact contracts to a shared location
 * where provider teams can access them for verification.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const PACTS_DIR = path.join(__dirname, '../../tests/contracts/pacts');
const SHARED_CONTRACTS_DIR = process.env.SHARED_CONTRACTS_DIR || path.join(__dirname, '../../shared-contracts');
const BUILD_NUMBER = process.env.BUILDKITE_BUILD_NUMBER || 'local';
const BRANCH = process.env.BUILDKITE_BRANCH || 'unknown';
const COMMIT = process.env.BUILDKITE_COMMIT || 'unknown';

console.log('📋 Publishing Pact contracts...');
console.log(`Build: ${BUILD_NUMBER}, Branch: ${BRANCH}, Commit: ${COMMIT.substring(0, 8)}`);

// Ensure shared contracts directory exists
if (!fs.existsSync(SHARED_CONTRACTS_DIR)) {
  fs.mkdirSync(SHARED_CONTRACTS_DIR, { recursive: true });
}

// Get all pact files
const pactFiles = fs
  .readdirSync(PACTS_DIR)
  .filter((file) => file.endsWith('.json'))
  .filter((file) => !file.includes('setup-verification')); // Exclude test files

if (pactFiles.length === 0) {
  console.log('⚠️  No contract files found to publish');
  process.exit(0);
}

console.log(`📄 Found ${pactFiles.length} contract files:`);
pactFiles.forEach((file) => console.log(`  - ${file}`));

// Copy contracts to shared location with metadata
pactFiles.forEach((file) => {
  const sourcePath = path.join(PACTS_DIR, file);
  const contract = JSON.parse(fs.readFileSync(sourcePath, 'utf8'));

  // Add build metadata to contract
  contract.metadata = {
    ...contract.metadata,
    buildInfo: {
      buildNumber: BUILD_NUMBER,
      branch: BRANCH,
      commit: COMMIT,
      timestamp: new Date().toISOString(),
      publishedBy: 'qantas-hotels-ui-ci',
    },
  };

  // Create versioned filename
  const versionedFile = file.replace('.json', `-${BUILD_NUMBER}.json`);
  const targetPath = path.join(SHARED_CONTRACTS_DIR, versionedFile);

  fs.writeFileSync(targetPath, JSON.stringify(contract, null, 2));
  console.log(`✅ Published: ${versionedFile}`);

  // Also create/update latest version
  const latestPath = path.join(SHARED_CONTRACTS_DIR, file);
  fs.writeFileSync(latestPath, JSON.stringify(contract, null, 2));
  console.log(`🔄 Updated latest: ${file}`);
});

// Create contract registry file
const registry = {
  lastUpdated: new Date().toISOString(),
  buildNumber: BUILD_NUMBER,
  branch: BRANCH,
  commit: COMMIT,
  contracts: pactFiles.map((file) => ({
    name: file,
    consumer: 'qantas-hotels-ui',
    provider: file.replace('qantas-hotels-ui-', '').replace('.json', ''),
    version: BUILD_NUMBER,
    path: file,
  })),
};

fs.writeFileSync(path.join(SHARED_CONTRACTS_DIR, 'contract-registry.json'), JSON.stringify(registry, null, 2));

console.log('📊 Contract registry updated');
console.log('🎉 Contract publishing completed successfully!');

// If running in CI, also output contract summary for build annotations
if (process.env.BUILDKITE) {
  const summary = `## 📋 Contract Test Results

**Build:** ${BUILD_NUMBER}  
**Branch:** ${BRANCH}  
**Commit:** ${COMMIT.substring(0, 8)}

### Published Contracts (${pactFiles.length})
${pactFiles.map((file) => `- ${file}`).join('\n')}

### Contract Details
${pactFiles
  .map((file) => {
    const contract = JSON.parse(fs.readFileSync(path.join(PACTS_DIR, file), 'utf8'));
    const interactionCount = contract.interactions ? contract.interactions.length : 0;
    return `- **${contract.provider.name}**: ${interactionCount} interactions`;
  })
  .join('\n')}
`;

  console.log('\n--- Contract Summary for Build Annotation ---');
  console.log(summary);
}
