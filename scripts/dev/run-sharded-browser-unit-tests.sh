#!/usr/bin/env bash

set -e

SHARD_TOTAL=${1:-2}

if [[ ! "$SHARD_TOTAL" =~ ^[0-9]+$ ]] || [[ $SHARD_TOTAL -lt 1 ]]; then
    echo "Error: SHARD_TOTAL must be a positive integer"
    echo "Usage: $0 [SHARD_TOTAL]"
    echo "  SHARD_TOTAL: Total number of shards to run in parallel (default: 2)"
    echo "Example: $0 4  # Run 4 shards in parallel"
    exit 1
fi

echo "Running browser unit tests with $SHARD_TOTAL shards in parallel..."

pids=()

for ((i=1; i<=SHARD_TOTAL; i++)); do
    echo "Starting shard $i of $SHARD_TOTAL"
    yarn test:browser --shard=$i/$SHARD_TOTAL &
    pids+=($!)
done

echo "Waiting for all $SHARD_TOTAL shards to complete..."
for pid in "${pids[@]}"; do
    wait $pid
done

echo "All shards completed successfully!"