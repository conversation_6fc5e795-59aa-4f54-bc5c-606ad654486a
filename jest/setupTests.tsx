/* eslint-disable @typescript-eslint/no-var-requires */
/* eslint-env jest */
/* eslint-disable no-console */
/* eslint-disable react/prop-types */
import React from 'react';
import ReactModal from 'react-modal';
import { matchers } from 'jest-emotion';
import { act } from 'react-dom/test-utils';
import { loadEnvConfig } from '@next/env';

import crypto from 'crypto';

import 'jest-canvas-mock';
import '@testing-library/jest-dom/jest-globals';
import 'core-js/es/array/flat';

require('dotenv').config({
  path: './.env.test',
});
loadEnvConfig(process.cwd(), false, {
  ...console,
  info() {
    // suppress log output for "Loaded env from ..."
  },
});

declare global {
  interface Window {
    qff_auth: object;
  }
}

expect.extend(matchers);

if (typeof document !== 'undefined') {
  Object.defineProperty(document, 'fonts', {
    value: {
      check: jest.fn().mockImplementation(() => {
        return true;
      }),
    },
  });
}

if (typeof global.self !== 'undefined') {
  Object.defineProperty(global.self, 'crypto', {
    value: {
      getRandomValues: (arr) => crypto.randomBytes(arr.length),
    },
  });
}

if (typeof window !== 'undefined') {
  window.qff_auth = {
    showLoginModal: jest.fn(),
    logout: jest.fn(),
    isInitialised: jest.fn(),
    subscribeInitCompleted: jest.fn(),
    subscribeLoginSuccess: jest.fn(),
    subscribeLoginFailure: jest.fn(),
    subscribeLogoutSuccess: jest.fn(),
    subscribeSyncProfileUpdate: jest.fn(),
  };
  require('intersection-observer'); // Polyfill js-dom to support intersectionObserver
  require('mutationobserver-shim');
}

global.flushPromises = async () => {
  jest.useFakeTimers();
  const immediatePromise = new Promise((resolve) => setTimeout(resolve, 0));
  jest.runOnlyPendingTimers();

  await immediatePromise;
};

global.flushPromisesAct = async () => {
  jest.useFakeTimers();

  await act(async () => {
    const immediatePromise = new Promise((resolve) => setTimeout(resolve, 0));
    jest.runOnlyPendingTimers();

    await immediatePromise;
  });
};

if (typeof document !== 'undefined') ReactModal.setAppElement(document.body);

// Next/link accepts a child link and passes href props to it. This mocks that behaviour
jest.mock('next/link', () => {
  const Mock = ({ href, children }) => {
    const child = (Array.isArray(children) ? children[0] : children) || null;
    const newChild = {
      ...child,
      props: { ...child.props, href },
    };
    return <>{newChild}</>;
  };
  Mock.displayName = 'NextLink';
  return Mock;
});

// Next/head sets the header metadata for each page
jest.mock('next/head', () => {
  const Mock = ({ children }) => {
    return <>{children}</>;
  };
  Mock.displayName = 'NextHead';
  return {
    __esModule: true,
    default: Mock,
  };
});

jest.mock('pages/_app.page', () => {
  return {
    __esModule: true,
    wrapper: {
      getStaticProps: (callback) => callback,
      getServerSideProps: (callback) => callback,
    },
  };
});

jest.mock('@sentry/nextjs', () => {
  return {
    __esModule: true,
    captureException: jest.fn(),
    addBreadcrumb: jest.fn(),
    withScope: jest.fn((cb) => cb({ setExtra: jest.fn() })),
  };
});
