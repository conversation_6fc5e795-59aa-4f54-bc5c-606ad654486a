process.env.BUILD_TARGET = 'contracts';
process.env.ENVIRONMENT = 'test';
process.env.BABEL_ENV = 'test';
process.env.PUBLIC_URL = '';

module.exports = {
  maxWorkers: 3,
  modulePaths: ['node_modules', '<rootDir>/src'],
  rootDir: '../../',
  setupFilesAfterEnv: ['<rootDir>/tests/contracts/setup/contractTestSetup.js'],
  testEnvironment: 'node',
  coverageProvider: 'v8',

  transform: {
    '\\.[jt]sx?$': 'babel-jest',
    '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga|css)$': '<rootDir>/tests/jest/fileTransformer.js',
  },
  transformIgnorePatterns: ['/node_modules/(?!(@pact-foundation|@testing-library)/).+\\.js$'],
  moduleNameMapper: { '^uuid$': 'uuid' },

  coveragePathIgnorePatterns: ['/node_modules/', '/tests/', '/coverage/', '\\.stories\\.', '\\.test\\.', '\\.spec\\.'],

  displayName: 'Contract Tests',
  testMatch: ['<rootDir>/tests/contracts/**/*.contract.test.js', '<rootDir>/tests/contracts/**/*.contract.test.ts'],

  // Pact tests need longer timeout for mock server setup/teardown
  testTimeout: 30000,

  // Coverage settings for contract tests
  collectCoverageFrom: [
    'src/lib/clients/**/*.{js,jsx,ts,tsx}',
    'src/api/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.test.{js,jsx,ts,tsx}',
    '!src/**/index.{js,jsx,ts,tsx}',
  ],
  coverageDirectory: 'coverage/contracts',
  coverageReporters: ['text-summary', 'html'],
};
