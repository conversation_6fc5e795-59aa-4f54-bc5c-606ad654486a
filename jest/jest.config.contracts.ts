import baseConfig from './jest.config.base';

process.env.BUILD_TARGET = 'contracts';

export default {
  ...baseConfig,
  displayName: 'Contract Tests',
  testMatch: ['<rootDir>/tests/contracts/**/*.contract.test.js', '<rootDir>/tests/contracts/**/*.contract.test.ts'],
  testEnvironment: 'node',

  // Pact tests need longer timeout for mock server setup/teardown
  testTimeout: 60000,

  // Limit concurrency to prevent port conflicts and resource exhaustion
  maxWorkers: 4,
  maxConcurrency: 4,

  // Transform patterns for contract tests - allow ES modules from specific packages
  transformIgnorePatterns: ['/node_modules/(?!(@pact-foundation|@testing-library|@qantasexperiences)/).+\\.(js|mjs|ts|tsx)$'],

  // Setup file for contract tests
  setupFilesAfterEnv: ['<rootDir>/tests/contracts/setup/contractTestSetup.js'],

  // Coverage settings for contract tests
  collectCoverageFrom: [
    'src/lib/clients/**/*.{js,jsx,ts,tsx}',
    'src/api/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.test.{js,jsx,ts,tsx}',
    '!src/**/index.{js,jsx,ts,tsx}',
  ],
  coverageDirectory: 'coverage/contracts',
  coverageReporters: ['text-summary', 'html'],

  // Module name mapping for contract tests
  moduleNameMapper: {
    ...baseConfig.moduleNameMapper,
    '^tests/contracts/(.*)$': '<rootDir>/tests/contracts/$1',
  },
};
