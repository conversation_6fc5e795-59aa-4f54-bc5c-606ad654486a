import { JestConfigWithTsJest } from 'ts-jest/dist/types';

process.env.ENVIRONMENT = 'test';
process.env.BABEL_ENV = 'test';
process.env.PUBLIC_URL = '';

const config: JestConfigWithTsJest = {
  maxWorkers: 3,
  modulePaths: ['node_modules', '<rootDir>/src'],
  rootDir: '..',
  setupFilesAfterEnv: ['<rootDir>/jest/setupTests.tsx'],
  testEnvironment: 'jsdom',
  coverageProvider: 'babel',

  transform: {
    '\\.[jt]sx?$': 'babel-jest',
    '\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga|css)$': '<rootDir>/jest/fileTransformer.js',
  },
  transformIgnorePatterns: ['[/\\\\]node_modules[/\\\\].+\\.(js|jsx|mjs|ts|tsx)$'],
  moduleNameMapper: { '^uuid$': 'uuid' },

  coveragePathIgnorePatterns: ['/node_modules/', '/tests/', '/coverage/', '\\.test\\.'],
};

export default config;
