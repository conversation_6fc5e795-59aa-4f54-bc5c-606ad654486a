/**
 * Authentication token mock utilities for contract tests
 * Provides mock authentication tokens and headers for API testing
 */

/**
 * Mock authentication tokens
 */
const mockTokens = {
  // Valid authentication token
  validToken:
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c',

  // Expired authentication token
  expiredToken:
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyLCJleHAiOjE1MTYyMzkwMjJ9.invalid',

  // Invalid authentication token
  invalidToken: 'invalid.token.format',

  // Member ID for authenticated requests
  memberId: '12345678',

  // QH User ID
  qhUserId: 'qh-user-123',
};

/**
 * Authentication header generators
 */
const authHeaders = {
  /**
   * Generate Authorization header with Bearer token
   * @param {string} token - Authentication token
   * @returns {Object} Headers object with Authorization
   */
  bearerToken(token = mockTokens.validToken) {
    return {
      Authorization: `Bearer ${token}`,
    };
  },

  /**
   * Generate headers for authenticated API requests
   * @param {string} token - Authentication token
   * @param {Object} additionalHeaders - Additional headers to include
   * @returns {Object} Complete headers object
   */
  authenticatedHeaders(token = mockTokens.validToken, additionalHeaders = {}) {
    return {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
      Accept: 'application/json',
      'X-Requested-With': 'XMLHttpRequest',
      ...additionalHeaders,
    };
  },

  /**
   * Generate headers for guest (unauthenticated) requests
   * @param {Object} additionalHeaders - Additional headers to include
   * @returns {Object} Headers object for guest requests
   */
  guestHeaders(additionalHeaders = {}) {
    return {
      'Content-Type': 'application/json',
      Accept: 'application/json',
      'X-Requested-With': 'XMLHttpRequest',
      ...additionalHeaders,
    };
  },

  /**
   * Generate headers with member context
   * @param {string} memberId - Member ID
   * @param {string} token - Authentication token
   * @returns {Object} Headers with member context
   */
  memberHeaders(memberId = mockTokens.memberId, token = mockTokens.validToken) {
    return {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
      Accept: 'application/json',
      'X-Member-Id': memberId,
      'X-Requested-With': 'XMLHttpRequest',
    };
  },

  /**
   * Generate headers for QH user context
   * @param {string} qhUserId - QH User ID
   * @param {string} token - Authentication token
   * @returns {Object} Headers with QH user context
   */
  qhUserHeaders(qhUserId = mockTokens.qhUserId, token = mockTokens.validToken) {
    return {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
      Accept: 'application/json',
      'X-QH-User-Id': qhUserId,
      'X-Requested-With': 'XMLHttpRequest',
    };
  },
};

/**
 * Authentication state generators for different user scenarios
 */
const authStates = {
  /**
   * Authenticated QFF member state
   */
  authenticatedMember: {
    token: mockTokens.validToken,
    memberId: mockTokens.memberId,
    tierStatus: 'Bronze',
    pointsBalance: 50000,
    isAuthenticated: true,
    isGuest: false,
  },

  /**
   * Authenticated QFF member with high tier status
   */
  authenticatedPlatinumMember: {
    token: mockTokens.validToken,
    memberId: '87654321',
    tierStatus: 'Platinum',
    pointsBalance: 250000,
    isAuthenticated: true,
    isGuest: false,
  },

  /**
   * Guest user state (not authenticated)
   */
  guestUser: {
    token: null,
    memberId: null,
    tierStatus: null,
    pointsBalance: 0,
    isAuthenticated: false,
    isGuest: true,
  },

  /**
   * Expired authentication state
   */
  expiredAuth: {
    token: mockTokens.expiredToken,
    memberId: null,
    tierStatus: null,
    pointsBalance: 0,
    isAuthenticated: false,
    isGuest: false,
  },

  /**
   * Invalid authentication state
   */
  invalidAuth: {
    token: mockTokens.invalidToken,
    memberId: null,
    tierStatus: null,
    pointsBalance: 0,
    isAuthenticated: false,
    isGuest: false,
  },
};

/**
 * Helper functions for authentication in contract tests
 */
const authHelpers = {
  /**
   * Get headers for a specific auth state
   * @param {string} stateName - Name of auth state (e.g., 'authenticatedMember')
   * @returns {Object} Headers for the auth state
   */
  getHeadersForState(stateName) {
    const state = authStates[stateName];
    if (!state) {
      throw new Error(`Unknown auth state: ${stateName}`);
    }

    if (state.isGuest) {
      return authHeaders.guestHeaders();
    }

    if (state.isAuthenticated && state.token) {
      return authHeaders.memberHeaders(state.memberId, state.token);
    }

    // For expired or invalid auth, still send the token
    return authHeaders.authenticatedHeaders(state.token);
  },

  /**
   * Get auth context for API requests
   * @param {string} stateName - Name of auth state
   * @returns {Object} Auth context object
   */
  getAuthContext(stateName) {
    const state = authStates[stateName];
    if (!state) {
      throw new Error(`Unknown auth state: ${stateName}`);
    }

    return {
      headers: this.getHeadersForState(stateName),
      state: { ...state },
    };
  },

  /**
   * Create provider state for authentication scenarios
   * @param {string} stateName - Name of auth state
   * @returns {string} Provider state description
   */
  getProviderState(stateName) {
    const stateDescriptions = {
      authenticatedMember: 'user is authenticated as QFF member',
      authenticatedPlatinumMember: 'user is authenticated as QFF Platinum member',
      guestUser: 'user is not authenticated',
      expiredAuth: 'user has expired authentication token',
      invalidAuth: 'user has invalid authentication token',
    };

    return stateDescriptions[stateName] || `user is in ${stateName} state`;
  },
};

module.exports = {
  mockTokens,
  authHeaders,
  authStates,
  authHelpers,
};
