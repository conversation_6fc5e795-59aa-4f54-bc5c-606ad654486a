/* eslint-disable no-console */
/**
 * Base class for contract tests
 * Provides common setup, teardown, and utility methods for Pact consumer tests
 */

const { Pact } = require('@pact-foundation/pact');
const path = require('path');
const PACT_CONFIG = require('../pact.config');

// Port manager to avoid conflicts between concurrent tests
class PortManager {
  static usedPorts = new Set();
  static basePort = 3000;

  static getAvailablePort() {
    let port = this.basePort;
    while (this.usedPorts.has(port)) {
      port++;
    }
    this.usedPorts.add(port);
    return port;
  }

  static releasePort(port) {
    this.usedPorts.delete(port);
  }
}

class ContractTestBase {
  constructor(providerName, options = {}) {
    this.providerName = providerName;
    this.provider = null;
    this.mockServerPort = null;
    this.options = {
      logLevel: 'ERROR',
      ...options,
    };
    this.isSetup = false;
  }

  /**
   * Set up Pact provider before tests
   * Creates mock server and configures provider with dynamic port allocation
   */
  async setup() {
    if (this.isSetup) {
      return;
    }

    // Get a unique port for this test instance
    this.mockServerPort = PortManager.getAvailablePort();

    this.provider = new Pact({
      consumer: PACT_CONFIG.consumer,
      provider: this.providerName,
      port: this.mockServerPort,
      log: path.resolve(process.cwd(), 'tests/contracts/logs', `${this.providerName}-${this.mockServerPort}-pact.log`),
      dir: PACT_CONFIG.dir,
      spec: PACT_CONFIG.spec,
      logLevel: this.options.logLevel,
      cors: PACT_CONFIG.cors,
    });

    try {
      // Suppress tracing subscriber errors during setup
      const originalConsoleError = console.error;
      console.error = (...args) => {
        const message = args.join(' ');
        if (
          message.includes('Failed to initialise global tracing subscriber') ||
          message.includes('a global default trace dispatcher has already been set')
        ) {
          return;
        }
        originalConsoleError.apply(console, args);
      };

      await this.provider.setup();

      // Restore original console.error
      console.error = originalConsoleError;

      // Wait a moment for the server to be fully ready
      await new Promise((resolve) => setTimeout(resolve, 100));

      this.isSetup = true;
    } catch (error) {
      // Release port if setup fails
      PortManager.releasePort(this.mockServerPort);
      this.mockServerPort = null;
      this.provider = null;
      throw new Error(`Failed to setup Pact provider for ${this.providerName}: ${error.message}`);
    }
  }

  /**
   * Tear down Pact provider after tests
   * Writes contracts and cleans up mock server
   */
  async teardown() {
    if (this.provider && this.isSetup) {
      try {
        await this.provider.finalize();
      } catch (error) {
        console.warn(`Warning: Error during provider finalization: ${error.message}`);
      } finally {
        // Always release the port, even if finalization fails
        if (this.mockServerPort) {
          PortManager.releasePort(this.mockServerPort);
        }
        this.isSetup = false;
        this.provider = null;
        this.mockServerPort = null;
      }
    }
  }

  /**
   * Verify interactions and write contracts
   * Should be called after each test
   */
  async verify() {
    if (this.provider && this.isSetup) {
      await this.provider.verify();
    }
  }

  /**
   * Add interaction to the provider
   * @param {Object} interaction - Pact interaction object
   */
  addInteraction(interaction) {
    if (this.provider && this.isSetup) {
      return this.provider.addInteraction(interaction);
    }
    throw new Error('Provider not initialized. Call setup() first.');
  }

  /**
   * Get mock server URL
   * @returns {string} Mock server base URL
   */
  getMockServerUrl() {
    if (this.mockServerPort && this.isSetup) {
      return `http://${PACT_CONFIG.host}:${this.mockServerPort}`;
    }
    throw new Error('Mock server not started. Call setup() first.');
  }

  /**
   * Clean up interactions between tests
   */
  async cleanupInteractions() {
    if (this.provider && this.isSetup) {
      try {
        // Remove all interactions to prevent state leakage
        await this.provider.removeInteractions();

        // Add a small delay to ensure cleanup is complete
        await new Promise((resolve) => setTimeout(resolve, 100));
      } catch (error) {
        console.warn(`Warning: Error during interaction cleanup: ${error.message}`);
      }
    }
  }

  /**
   * Reset mock server state completely
   * This is more aggressive than cleanupInteractions and should be used
   * when tests are experiencing connection reset errors
   */
  async resetMockServerState() {
    if (this.provider && this.isSetup) {
      try {
        // Remove all interactions
        await this.provider.removeInteractions();

        // Wait for any pending operations to complete
        await new Promise((resolve) => setTimeout(resolve, 200));

        // Verify the server is responsive
        const mockServerUrl = this.getMockServerUrl();
        const response = await fetch(`${mockServerUrl}/_pact/health`, {
          method: 'GET',
          timeout: 5000,
        }).catch(() => null);

        if (!response || !response.ok) {
          console.warn('Mock server health check failed, server may need restart');
        }
      } catch (error) {
        console.warn(`Warning: Error during mock server state reset: ${error.message}`);
      }
    }
  }

  /**
   * Force cleanup - for emergency cleanup in case of test failures
   */
  async forceCleanup() {
    if (this.mockServerPort) {
      PortManager.releasePort(this.mockServerPort);
    }
    this.isSetup = false;
    this.provider = null;
    this.mockServerPort = null;
  }
}

module.exports = ContractTestBase;
