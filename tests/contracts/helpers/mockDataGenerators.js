/**
 * Mock data generators for contract tests
 * Provides realistic test data for API requests and responses
 */

const { like, eachLike, term, iso8601DateTime } = require('@pact-foundation/pact').MatchersV3;

/**
 * Authentication mock data
 */
const authMockData = {
  validAuthToken: 'mock-auth-token-12345',

  authenticationRequest: () => ({
    accessToken: like('mock-access-token'),
    qhUserId: like('user-123'),
  }),

  authenticationResponse: () => ({
    valid: like(true),
    memberDetails: like({
      memberId: like('12345678'),
      tierStatus: like('Bronze'),
      pointsBalance: like(50000),
    }),
  }),

  invalidAuthResponse: () => ({
    valid: like(false),
    error: like({
      code: like('INVALID_TOKEN'),
      message: like('Invalid or expired token'),
    }),
  }),
};

/**
 * Booking mock data
 */
const bookingMockData = {
  bookingRequest: () => ({
    propertyId: like('property-123'),
    checkIn: term({
      matcher: '\\d{4}-\\d{2}-\\d{2}',
      generate: '2024-12-01',
    }),
    checkOut: term({
      matcher: '\\d{4}-\\d{2}-\\d{2}',
      generate: '2024-12-05',
    }),
    guests: like({
      adults: like(2),
      children: like(1),
      infants: like(0),
    }),
    paymentMethod: like('card'),
  }),

  bookingResponse: () => ({
    booking: like({
      id: like('booking-456'),
      status: term({
        matcher: 'confirmed|pending|cancelled',
        generate: 'confirmed',
      }),
      propertyId: like('property-123'),
      checkIn: term({
        matcher: '\\d{4}-\\d{2}-\\d{2}',
        generate: '2024-12-01',
      }),
      checkOut: term({
        matcher: '\\d{4}-\\d{2}-\\d{2}',
        generate: '2024-12-05',
      }),
      guests: like({
        adults: like(2),
        children: like(1),
        infants: like(0),
      }),
      totalAmount: like({
        currency: like('AUD'),
        amount: like(450.0),
      }),
    }),
  }),

  bookingErrorResponse: () => ({
    error: like({
      code: like('BOOKING_FAILED'),
      message: like('Unable to create booking'),
      details: like({
        propertyId: like('Property not available for selected dates'),
      }),
    }),
    timestamp: iso8601DateTime(),
    requestId: like('req-789'),
  }),
};

/**
 * Property mock data
 */
const propertyMockData = {
  propertySearchRequest: () => ({
    location: like('Sydney'),
    checkIn: term({
      matcher: '\\d{4}-\\d{2}-\\d{2}',
      generate: '2024-12-01',
    }),
    checkOut: term({
      matcher: '\\d{4}-\\d{2}-\\d{2}',
      generate: '2024-12-05',
    }),
    guests: like({
      adults: like(2),
      children: like(0),
    }),
  }),

  propertySearchResponse: () => ({
    properties: eachLike(
      {
        id: like('property-123'),
        name: like('Sydney Harbour Hotel'),
        location: like({
          city: like('Sydney'),
          country: like('Australia'),
          coordinates: like({
            latitude: like(-33.8688),
            longitude: like(151.2093),
          }),
        }),
        rating: like({
          type: term({
            matcher: 'star|circle',
            generate: 'star',
          }),
          value: like(4.5),
        }),
        pricing: like({
          currency: like('AUD'),
          baseRate: like(200.0),
          totalRate: like(250.0),
        }),
      },
      { min: 1 },
    ),
    pagination: like({
      page: like(1),
      totalPages: like(5),
      totalResults: like(50),
    }),
  }),

  propertyDetailsResponse: () => ({
    id: like('property-123'),
    name: like('Sydney Harbour Hotel'),
    description: like('Luxury hotel with harbour views'),
    location: like({
      address: like('123 Harbour Street, Sydney NSW 2000'),
      city: like('Sydney'),
      country: like('Australia'),
      coordinates: like({
        latitude: like(-33.8688),
        longitude: like(151.2093),
      }),
    }),
    amenities: eachLike(like('WiFi'), { min: 1 }),
    images: eachLike(
      {
        url: like('https://example.com/image.jpg'),
        alt: like('Hotel exterior'),
      },
      { min: 1 },
    ),
    rating: like({
      type: like('star'),
      value: like(4.5),
    }),
  }),
};

/**
 * Quote mock data
 */
const quoteMockData = {
  quoteRequest: () => ({
    propertyId: like('property-123'),
    checkIn: term({
      matcher: '\\d{4}-\\d{2}-\\d{2}',
      generate: '2024-12-01',
    }),
    checkOut: term({
      matcher: '\\d{4}-\\d{2}-\\d{2}',
      generate: '2024-12-05',
    }),
    guests: like({
      adults: like(2),
      children: like(0),
    }),
    paymentMethod: term({
      matcher: 'card|points|mixed',
      generate: 'card',
    }),
  }),

  quoteResponse: () => ({
    quote: like({
      id: like('quote-789'),
      propertyId: like('property-123'),
      totalAmount: like({
        currency: like('AUD'),
        amount: like(450.0),
      }),
      breakdown: like({
        baseRate: like(400.0),
        taxes: like(40.0),
        fees: like(10.0),
      }),
      pointsEarn: like(2250),
      expiresAt: iso8601DateTime(),
    }),
  }),
};

/**
 * Payment mock data
 */
const paymentMockData = {
  paymentMethodsResponse: () => ({
    paymentMethods: eachLike(
      {
        type: term({
          matcher: 'card|paypal|applepay',
          generate: 'card',
        }),
        name: like('Credit Card'),
        enabled: like(true),
      },
      { min: 1 },
    ),
  }),

  paymentRequest: () => ({
    amount: like({
      currency: like('AUD'),
      value: like(45000), // Amount in cents
    }),
    paymentMethod: like({
      type: like('card'),
      encryptedCardNumber: like('encrypted-card-data'),
      encryptedExpiryMonth: like('encrypted-month'),
      encryptedExpiryYear: like('encrypted-year'),
      encryptedSecurityCode: like('encrypted-cvv'),
    }),
    reference: like('booking-456'),
  }),

  paymentResponse: () => ({
    resultCode: term({
      matcher: 'Authorised|Refused|Error',
      generate: 'Authorised',
    }),
    pspReference: like('psp-ref-123'),
    merchantReference: like('booking-456'),
  }),
};

/**
 * Content (Sanity CMS) mock data
 */
const contentMockData = {
  campaignResponse: () => ({
    _id: like('campaign-123'),
    title: like('Summer Sale'),
    description: like('Save up to 30% on selected hotels'),
    startDate: iso8601DateTime(),
    endDate: iso8601DateTime(),
    active: like(true),
    content: like({
      heading: like('Summer Sale'),
      body: like('Book now and save!'),
    }),
  }),

  faqResponse: () => ({
    faqs: eachLike(
      {
        _id: like('faq-123'),
        question: like('How do I cancel my booking?'),
        answer: like('You can cancel your booking through your account.'),
        category: like('booking'),
      },
      { min: 1 },
    ),
  }),
};

/**
 * Error response generators
 */
const errorMockData = {
  badRequestError: () => ({
    error: like({
      code: like('BAD_REQUEST'),
      message: like('Invalid request parameters'),
      details: like({
        field: like('checkIn'),
        issue: like('Date must be in the future'),
      }),
    }),
    timestamp: iso8601DateTime(),
    requestId: like('req-error-123'),
  }),

  unauthorizedError: () => ({
    error: like({
      code: like('UNAUTHORIZED'),
      message: like('Authentication required'),
    }),
    timestamp: iso8601DateTime(),
    requestId: like('req-error-456'),
  }),

  notFoundError: () => ({
    error: like({
      code: like('NOT_FOUND'),
      message: like('Resource not found'),
    }),
    timestamp: iso8601DateTime(),
    requestId: like('req-error-789'),
  }),

  serverError: () => ({
    error: like({
      code: like('INTERNAL_ERROR'),
      message: like('An internal server error occurred'),
    }),
    timestamp: iso8601DateTime(),
    requestId: like('req-error-500'),
  }),
};

module.exports = {
  authMockData,
  bookingMockData,
  propertyMockData,
  quoteMockData,
  paymentMockData,
  contentMockData,
  errorMockData,
};
