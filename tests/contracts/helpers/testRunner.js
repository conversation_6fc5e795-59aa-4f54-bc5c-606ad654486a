/* eslint-disable no-console, no-useless-catch */
/**
 * Test runner utility for managing concurrent Pact contract tests
 * Handles proper setup, cleanup, and resource management
 */

const ContractTestBase = require('./ContractTestBase');

/**
 * Global test registry to track active test instances
 */
class TestRegistry {
  static instances = new Map();
  static cleanupHandlers = new Set();

  static register(testId, instance) {
    this.instances.set(testId, instance);
  }

  static unregister(testId) {
    this.instances.delete(testId);
  }

  static async cleanupAll() {
    const cleanupPromises = Array.from(this.instances.values()).map(async (instance) => {
      try {
        if (instance.teardown) {
          await instance.teardown();
        }
      } catch (error) {
        console.warn(`Warning: Error during cleanup: ${error.message}`);
      }
    });

    await Promise.allSettled(cleanupPromises);
    this.instances.clear();
  }

  static registerCleanupHandler() {
    if (this.cleanupHandlers.size === 0) {
      // Register cleanup handlers for various exit scenarios
      const cleanup = () => {
        this.cleanupAll().catch(console.error);
      };

      process.on('exit', cleanup);
      process.on('SIGINT', cleanup);
      process.on('SIGTERM', cleanup);
      process.on('uncaughtException', cleanup);
      process.on('unhandledRejection', cleanup);

      this.cleanupHandlers.add(cleanup);
    }
  }
}

/**
 * Creates a test suite with proper setup and teardown for Pact contract tests
 * @param {string} suiteName - Name of the test suite
 * @param {string} providerName - Name of the Pact provider
 * @param {Function} testFn - Test function that receives the contract test instance
 * @param {Object} options - Additional options for the test
 */
function createContractTestSuite(suiteName, providerName, testFn, options = {}) {
  describe(suiteName, () => {
    let contractTest;
    const testId = `${suiteName}-${providerName}-${Date.now()}-${Math.random()}`;

    beforeAll(async () => {
      // Register cleanup handler
      TestRegistry.registerCleanupHandler();

      // Create and setup contract test instance
      contractTest = new ContractTestBase(providerName, options);
      TestRegistry.register(testId, contractTest);

      await contractTest.setup();
    }, 30000); // Increased timeout for setup

    afterAll(async () => {
      if (contractTest) {
        await contractTest.teardown();
        TestRegistry.unregister(testId);
      }
    }, 30000); // Increased timeout for teardown

    afterEach(async () => {
      if (contractTest) {
        try {
          // Verify interactions first
          await contractTest.verify();
        } catch (error) {
          // Log verification errors but don't fail the test runner
          console.warn(`Pact verification failed: ${error.message}`);
        }

        try {
          // Clean up interactions to prevent state leakage between tests
          await contractTest.cleanupInteractions();
        } catch (error) {
          console.warn(`Interaction cleanup failed: ${error.message}`);
        }
      }
    });

    // Run the actual test function with a getter function for contractTest
    testFn(() => contractTest);
  });
}

/**
 * Helper function to create a mock server URL getter
 * @param {Function} contractTestGetter - Function that returns the contract test instance
 * @returns {Function} Function that returns the mock server URL
 */
function createMockServerUrlGetter(contractTestGetter) {
  return () => {
    const contractTest = contractTestGetter();
    if (!contractTest) {
      throw new Error('Contract test instance not available');
    }
    return contractTest.getMockServerUrl();
  };
}

/**
 * Helper function to add interaction with error handling
 * @param {Function} contractTestGetter - Function that returns the contract test instance
 * @param {Object} interaction - The Pact interaction
 */
async function addInteractionSafely(contractTestGetter, interaction) {
  try {
    const contractTest = contractTestGetter();
    if (!contractTest) {
      throw new Error('Contract test instance not available');
    }
    return await contractTest.addInteraction(interaction);
  } catch (error) {
    throw error;
  }
}

module.exports = {
  createContractTestSuite,
  createMockServerUrlGetter,
  addInteractionSafely,
  TestRegistry,
  ContractTestBase,
};
