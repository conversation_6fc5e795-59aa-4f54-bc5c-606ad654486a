/**
 * Schema validation helpers for contract tests
 * Uses Zod for runtime schema validation of API responses
 */

const { z } = require('zod');

/**
 * Common schema definitions
 */
const commonSchemas = {
  // ISO date string
  isoDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),

  // ISO datetime string
  isoDateTime: z.string().datetime(),

  // Currency amount
  currencyAmount: z.object({
    currency: z.string().length(3),
    amount: z.number().positive(),
  }),

  // Coordinates
  coordinates: z.object({
    latitude: z.number().min(-90).max(90),
    longitude: z.number().min(-180).max(180),
  }),

  // Error response
  errorResponse: z.object({
    error: z.object({
      code: z.string(),
      message: z.string(),
      details: z.record(z.any()).optional(),
    }),
    timestamp: z.string().datetime(),
    requestId: z.string(),
  }),
};

/**
 * Authentication schemas
 */
const authSchemas = {
  authenticationRequest: z.object({
    accessToken: z.string(),
    qhUserId: z.string().optional(),
  }),

  authenticationResponse: z.object({
    valid: z.boolean(),
    memberDetails: z
      .object({
        memberId: z.string(),
        tierStatus: z.string(),
        pointsBalance: z.number().nonnegative(),
      })
      .optional(),
  }),
};

/**
 * Booking schemas
 */
const bookingSchemas = {
  bookingRequest: z.object({
    propertyId: z.string(),
    checkIn: commonSchemas.isoDate,
    checkOut: commonSchemas.isoDate,
    guests: z.object({
      adults: z.number().positive(),
      children: z.number().nonnegative(),
      infants: z.number().nonnegative(),
    }),
    paymentMethod: z.enum(['card', 'points', 'mixed']),
  }),

  bookingResponse: z.object({
    booking: z.object({
      id: z.string(),
      status: z.enum(['confirmed', 'pending', 'cancelled']),
      propertyId: z.string(),
      checkIn: commonSchemas.isoDate,
      checkOut: commonSchemas.isoDate,
      guests: z.object({
        adults: z.number().positive(),
        children: z.number().nonnegative(),
        infants: z.number().nonnegative(),
      }),
      totalAmount: commonSchemas.currencyAmount,
    }),
  }),
};

/**
 * Property schemas
 */
const propertySchemas = {
  propertySearchRequest: z.object({
    location: z.string(),
    checkIn: commonSchemas.isoDate,
    checkOut: commonSchemas.isoDate,
    guests: z.object({
      adults: z.number().positive(),
      children: z.number().nonnegative(),
    }),
  }),

  property: z.object({
    id: z.string(),
    name: z.string(),
    location: z.object({
      city: z.string(),
      country: z.string(),
      coordinates: commonSchemas.coordinates,
    }),
    rating: z.object({
      type: z.enum(['star', 'circle']),
      value: z.number().min(0).max(5),
    }),
    pricing: z.object({
      currency: z.string().length(3),
      baseRate: z.number().positive(),
      totalRate: z.number().positive(),
    }),
  }),

  propertySearchResponse: z.object({
    properties: z.array(z.lazy(() => propertySchemas.property)),
    pagination: z.object({
      page: z.number().positive(),
      totalPages: z.number().positive(),
      totalResults: z.number().nonnegative(),
    }),
  }),

  propertyDetailsResponse: z.object({
    id: z.string(),
    name: z.string(),
    description: z.string(),
    location: z.object({
      address: z.string(),
      city: z.string(),
      country: z.string(),
      coordinates: commonSchemas.coordinates,
    }),
    amenities: z.array(z.string()),
    images: z.array(
      z.object({
        url: z.string().url(),
        alt: z.string(),
      }),
    ),
    rating: z.object({
      type: z.enum(['star', 'circle']),
      value: z.number().min(0).max(5),
    }),
  }),
};

/**
 * Quote schemas
 */
const quoteSchemas = {
  quoteRequest: z.object({
    propertyId: z.string(),
    checkIn: commonSchemas.isoDate,
    checkOut: commonSchemas.isoDate,
    guests: z.object({
      adults: z.number().positive(),
      children: z.number().nonnegative(),
    }),
    paymentMethod: z.enum(['card', 'points', 'mixed']),
  }),

  quoteResponse: z.object({
    quote: z.object({
      id: z.string(),
      propertyId: z.string(),
      totalAmount: commonSchemas.currencyAmount,
      breakdown: z.object({
        baseRate: z.number().positive(),
        taxes: z.number().nonnegative(),
        fees: z.number().nonnegative(),
      }),
      pointsEarn: z.number().nonnegative(),
      expiresAt: commonSchemas.isoDateTime,
    }),
  }),
};

/**
 * Payment schemas
 */
const paymentSchemas = {
  paymentMethodsResponse: z.object({
    paymentMethods: z.array(
      z.object({
        type: z.enum(['card', 'paypal', 'applepay']),
        name: z.string(),
        enabled: z.boolean(),
      }),
    ),
  }),

  paymentRequest: z.object({
    amount: z.object({
      currency: z.string().length(3),
      value: z.number().positive(), // Amount in cents
    }),
    paymentMethod: z.object({
      type: z.string(),
      encryptedCardNumber: z.string(),
      encryptedExpiryMonth: z.string(),
      encryptedExpiryYear: z.string(),
      encryptedSecurityCode: z.string(),
    }),
    reference: z.string(),
  }),

  paymentResponse: z.object({
    resultCode: z.enum(['Authorised', 'Refused', 'Error']),
    pspReference: z.string(),
    merchantReference: z.string(),
  }),
};

/**
 * Content (Sanity CMS) schemas
 */
const contentSchemas = {
  campaignResponse: z.object({
    _id: z.string(),
    title: z.string(),
    description: z.string(),
    startDate: commonSchemas.isoDateTime,
    endDate: commonSchemas.isoDateTime,
    active: z.boolean(),
    content: z.object({
      heading: z.string(),
      body: z.string(),
    }),
  }),

  faqResponse: z.object({
    faqs: z.array(
      z.object({
        _id: z.string(),
        question: z.string(),
        answer: z.string(),
        category: z.string(),
      }),
    ),
  }),
};

/**
 * Validation helper functions
 */
const validators = {
  /**
   * Validate data against a schema
   * @param {any} data - Data to validate
   * @param {z.ZodSchema} schema - Zod schema to validate against
   * @returns {Object} Validation result with success flag and errors
   */
  validate(data, schema) {
    try {
      const result = schema.parse(data);
      return { success: true, data: result, errors: null };
    } catch (error) {
      return {
        success: false,
        data: null,
        errors: error.errors || [{ message: error.message }],
      };
    }
  },

  /**
   * Assert that data matches schema (throws on validation failure)
   * @param {any} data - Data to validate
   * @param {z.ZodSchema} schema - Zod schema to validate against
   */
  assertValid(data, schema) {
    const result = this.validate(data, schema);
    if (!result.success) {
      const errorMessages = result.errors.map((err) => `${err.path ? err.path.join('.') + ': ' : ''}${err.message}`).join(', ');
      throw new Error(`Schema validation failed: ${errorMessages}`);
    }
    return result.data;
  },

  /**
   * Create a Jest matcher for schema validation
   * @param {z.ZodSchema} schema - Zod schema to validate against
   * @returns {Function} Jest matcher function
   */
  toMatchSchema(schema) {
    return function (received) {
      const result = validators.validate(received, schema);

      if (result.success) {
        return {
          message: () => `Expected data not to match schema`,
          pass: true,
        };
      } else {
        const errorMessages = result.errors.map((err) => `${err.path ? err.path.join('.') + ': ' : ''}${err.message}`).join('\n  ');

        return {
          message: () => `Expected data to match schema:\n  ${errorMessages}`,
          pass: false,
        };
      }
    };
  },
};

// Extend Jest matchers
expect.extend({
  toMatchSchema: validators.toMatchSchema,
});

module.exports = {
  commonSchemas,
  authSchemas,
  bookingSchemas,
  propertySchemas,
  quoteSchemas,
  paymentSchemas,
  contentSchemas,
  validators,
};
