/**
 * Contract tests for Authentication API endpoints
 * Tests the consumer contract for token validation and member authentication
 */

const { Matchers } = require('@pact-foundation/pact');
const { createContractTestSuite, createMockServerUrlGetter, addInteractionSafely } = require('../helpers/testRunner');
const axios = require('axios');
const { API_PATHS, HTTP_STATUS, HTTP_HEADERS, CONTENT_TYPES, TEST_DATA, PROVIDER_STATES } = require('../shared/constants');
const { fail } = require('../shared/testHelpers');

createContractTestSuite('Authentication API Contract Tests', 'unknown-provider', (contractTest) => {
  const getMockServerUrl = createMockServerUrlGetter(contractTest);

  describe('GET /validate - Token Validation', () => {
    describe('valid token validation', () => {
      const validToken = 'valid-jwt-token-123';
      const expectedValidationResponse = {
        valid: true,
        memberDetails: {
          memberId: TEST_DATA.MEMBER_IDS.VALID,
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          tierStatus: 'Gold',
          pointsBalance: Matchers.integer(50000),
          isVip: false,
        },
        tokenInfo: {
          expiresAt: Matchers.iso8601DateTime(),
          issuedAt: Matchers.iso8601DateTime(),
          scope: ['hotels', 'profile'],
        },
      };

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.AUTH.USER_AUTHENTICATED,
          uponReceiving: 'a request to validate a valid authentication token',
          withRequest: {
            method: 'GET',
            path: API_PATHS.AUTH.VALIDATE,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: `Bearer ${validToken}`,
              [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedValidationResponse,
          },
        });
      });

      it('should validate token and return member details', async () => {
        const response = await axios({
          method: 'GET',
          url: `${getMockServerUrl()}${API_PATHS.AUTH.VALIDATE}`,
          headers: {
            [HTTP_HEADERS.AUTHORIZATION]: `Bearer ${validToken}`,
            [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
          },
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toMatchObject({
          valid: true,
          memberDetails: {
            memberId: TEST_DATA.MEMBER_IDS.VALID,
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            tierStatus: 'Gold',
            isVip: false,
          },
          tokenInfo: {
            scope: ['hotels', 'profile'],
          },
        });
        expect(response.data.memberDetails.pointsBalance).toBeGreaterThan(0);
        expect(response.data.tokenInfo.expiresAt).toBeDefined();
        expect(response.data.tokenInfo.issuedAt).toBeDefined();
      });
    });

    describe('invalid token validation', () => {
      const invalidToken = 'invalid-jwt-token';

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.AUTH.TOKEN_INVALID,
          uponReceiving: 'a request to validate an invalid authentication token',
          withRequest: {
            method: 'GET',
            path: API_PATHS.AUTH.VALIDATE,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: `Bearer ${invalidToken}`,
              [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.UNAUTHORIZED,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              valid: false,
              error: {
                code: 'INVALID_TOKEN',
                message: 'Authentication token is invalid or malformed',
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 401 for invalid token', async () => {
        try {
          await axios({
            method: 'GET',
            url: `${getMockServerUrl()}${API_PATHS.AUTH.VALIDATE}`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: `Bearer ${invalidToken}`,
              [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
            },
          });
          fail('Expected request to fail with 401');
        } catch (error) {
          expect(error.response?.status).toBe(HTTP_STATUS.UNAUTHORIZED);
          if (error.response) {
            expect(error.response.data).toMatchObject({
              valid: false,
              error: {
                code: 'INVALID_TOKEN',
                message: 'Authentication token is invalid or malformed',
              },
            });
            expect(error.response.data.timestamp).toBeDefined();
            expect(error.response.data.requestId).toBeDefined();
          }
        }
      });
    });

    describe('expired token validation', () => {
      const expiredToken = 'expired-jwt-token-456';

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.AUTH.TOKEN_EXPIRED,
          uponReceiving: 'a request to validate an expired authentication token',
          withRequest: {
            method: 'GET',
            path: API_PATHS.AUTH.VALIDATE,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: `Bearer ${expiredToken}`,
              [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.UNAUTHORIZED,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              valid: false,
              error: {
                code: 'TOKEN_EXPIRED',
                message: 'Authentication token has expired',
                details: {
                  expiredAt: Matchers.iso8601DateTime(),
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 401 for expired token', async () => {
        try {
          await axios({
            method: 'GET',
            url: `${getMockServerUrl()}${API_PATHS.AUTH.VALIDATE}`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: `Bearer ${expiredToken}`,
              [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
            },
          });
          fail('Expected request to fail with 401');
        } catch (error) {
          expect(error.response?.status).toBe(HTTP_STATUS.UNAUTHORIZED);
          if (error.response) {
            expect(error.response.data).toMatchObject({
              valid: false,
              error: {
                code: 'TOKEN_EXPIRED',
                message: 'Authentication token has expired',
              },
            });
            expect(error.response.data.error.details.expiredAt).toBeDefined();
          }
        }
      });
    });

    describe('missing authorization header', () => {
      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.AUTH.USER_GUEST,
          uponReceiving: 'a request to validate token without authorization header',
          withRequest: {
            method: 'GET',
            path: API_PATHS.AUTH.VALIDATE,
            headers: {
              [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.UNAUTHORIZED,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              valid: false,
              error: {
                code: 'MISSING_AUTHORIZATION',
                message: 'Authorization header is required',
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 401 for missing authorization header', async () => {
        try {
          await axios({
            method: 'GET',
            url: `${getMockServerUrl()}${API_PATHS.AUTH.VALIDATE}`,
            headers: {
              [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
            },
          });
          fail('Expected request to fail with 401');
        } catch (error) {
          expect(error.response?.status).toBe(HTTP_STATUS.UNAUTHORIZED);
          if (error.response) {
            expect(error.response.data).toMatchObject({
              valid: false,
              error: {
                code: 'MISSING_AUTHORIZATION',
                message: 'Authorization header is required',
              },
            });
          }
        }
      });
    });
  });

  describe('POST /authenticate - Member Authentication', () => {
    describe('successful QFF member authentication', () => {
      const authPayload = {
        accessToken: 'qff-access-token-123',
        qhUserId: 'qh-user-456',
        loginType: 'qff',
      };

      const expectedAuthResponse = {
        success: true,
        authToken: Matchers.somethingLike('jwt-auth-token-789'),
        memberDetails: {
          memberId: TEST_DATA.MEMBER_IDS.VALID,
          firstName: 'Jane',
          lastName: 'Smith',
          email: '<EMAIL>',
          tierStatus: 'Platinum',
          pointsBalance: Matchers.integer(125000),
          isVip: true,
          preferences: {
            currency: 'AUD',
            language: 'en-AU',
          },
        },
        tokenInfo: {
          expiresAt: Matchers.iso8601DateTime(),
          issuedAt: Matchers.iso8601DateTime(),
          scope: ['hotels', 'profile', 'bookings'],
        },
      };

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.AUTH.USER_AUTHENTICATED,
          uponReceiving: 'a request to authenticate QFF member',
          withRequest: {
            method: 'POST',
            path: API_PATHS.AUTH.AUTHENTICATE,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
            },
            body: authPayload,
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedAuthResponse,
          },
        });
      });

      it('should authenticate QFF member successfully', async () => {
        const response = await axios({
          method: 'POST',
          url: `${getMockServerUrl()}${API_PATHS.AUTH.AUTHENTICATE}`,
          headers: {
            [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
          },
          data: authPayload,
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toMatchObject({
          success: true,
          memberDetails: {
            memberId: TEST_DATA.MEMBER_IDS.VALID,
            firstName: 'Jane',
            lastName: 'Smith',
            email: '<EMAIL>',
            tierStatus: 'Platinum',
            isVip: true,
            preferences: {
              currency: 'AUD',
              language: 'en-AU',
            },
          },
          tokenInfo: {
            scope: ['hotels', 'profile', 'bookings'],
          },
        });
        expect(response.data.authToken).toBeDefined();
        expect(response.data.memberDetails.pointsBalance).toBeGreaterThan(0);
        expect(response.data.tokenInfo.expiresAt).toBeDefined();
        expect(response.data.tokenInfo.issuedAt).toBeDefined();
      });
    });

    describe('guest checkout authentication', () => {
      const guestAuthPayload = {
        loginType: 'guest',
        guestDetails: {
          email: '<EMAIL>',
          firstName: 'Guest',
          lastName: 'User',
        },
      };

      const expectedGuestAuthResponse = {
        success: true,
        authToken: Matchers.somethingLike('guest-jwt-token-123'),
        guestDetails: {
          email: '<EMAIL>',
          firstName: 'Guest',
          lastName: 'User',
          isGuest: true,
        },
        tokenInfo: {
          expiresAt: Matchers.iso8601DateTime(),
          issuedAt: Matchers.iso8601DateTime(),
          scope: ['hotels', 'guest-bookings'],
        },
      };

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.AUTH.USER_GUEST,
          uponReceiving: 'a request to authenticate guest user',
          withRequest: {
            method: 'POST',
            path: API_PATHS.AUTH.AUTHENTICATE,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
            },
            body: guestAuthPayload,
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedGuestAuthResponse,
          },
        });
      });

      it('should authenticate guest user successfully', async () => {
        const response = await axios({
          method: 'POST',
          url: `${getMockServerUrl()}${API_PATHS.AUTH.AUTHENTICATE}`,
          headers: {
            [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
          },
          data: guestAuthPayload,
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toMatchObject({
          success: true,
          guestDetails: {
            email: '<EMAIL>',
            firstName: 'Guest',
            lastName: 'User',
            isGuest: true,
          },
          tokenInfo: {
            scope: ['hotels', 'guest-bookings'],
          },
        });
        expect(response.data.authToken).toBeDefined();
        expect(response.data.tokenInfo.expiresAt).toBeDefined();
        expect(response.data.tokenInfo.issuedAt).toBeDefined();
      });
    });

    describe('authentication failure scenarios', () => {
      describe('invalid QFF access token', () => {
        const invalidAuthPayload = {
          accessToken: 'invalid-qff-token',
          qhUserId: 'qh-user-456',
          loginType: 'qff',
        };

        beforeEach(async () => {
          await addInteractionSafely(contractTest, {
            state: PROVIDER_STATES.AUTH.TOKEN_INVALID,
            uponReceiving: 'a request to authenticate with invalid QFF token',
            withRequest: {
              method: 'POST',
              path: API_PATHS.AUTH.AUTHENTICATE,
              headers: {
                [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
                [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
              },
              body: invalidAuthPayload,
            },
            willRespondWith: {
              status: HTTP_STATUS.UNAUTHORIZED,
              headers: {
                [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              },
              body: {
                success: false,
                error: {
                  code: 'INVALID_QFF_TOKEN',
                  message: 'QFF access token is invalid or expired',
                },
                timestamp: Matchers.iso8601DateTime(),
                requestId: Matchers.uuid(),
              },
            },
          });
        });

        it('should return 401 for invalid QFF token', async () => {
          try {
            await axios({
              method: 'POST',
              url: `${getMockServerUrl()}${API_PATHS.AUTH.AUTHENTICATE}`,
              headers: {
                [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
                [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
              },
              data: invalidAuthPayload,
            });
            fail('Expected request to fail with 401');
          } catch (error) {
            expect(error.response?.status).toBe(HTTP_STATUS.UNAUTHORIZED);
            if (error.response) {
              expect(error.response.data).toMatchObject({
                success: false,
                error: {
                  code: 'INVALID_QFF_TOKEN',
                  message: 'QFF access token is invalid or expired',
                },
              });
            }
          }
        });
      });

      describe('missing required fields', () => {
        const incompletePayload = {
          loginType: 'qff',
          // Missing accessToken and qhUserId
        };

        beforeEach(async () => {
          await addInteractionSafely(contractTest, {
            state: PROVIDER_STATES.AUTH.USER_GUEST,
            uponReceiving: 'a request to authenticate with missing required fields',
            withRequest: {
              method: 'POST',
              path: API_PATHS.AUTH.AUTHENTICATE,
              headers: {
                [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
                [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
              },
              body: incompletePayload,
            },
            willRespondWith: {
              status: HTTP_STATUS.BAD_REQUEST,
              headers: {
                [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              },
              body: {
                success: false,
                error: {
                  code: 'VALIDATION_ERROR',
                  message: 'Required fields are missing',
                  details: {
                    missingFields: ['accessToken', 'qhUserId'],
                  },
                },
                timestamp: Matchers.iso8601DateTime(),
                requestId: Matchers.uuid(),
              },
            },
          });
        });

        it('should return 400 for missing required fields', async () => {
          try {
            await axios({
              method: 'POST',
              url: `${getMockServerUrl()}${API_PATHS.AUTH.AUTHENTICATE}`,
              headers: {
                [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
                [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
              },
              data: incompletePayload,
            });
            fail('Expected request to fail with 400');
          } catch (error) {
            expect(error.response?.status).toBe(HTTP_STATUS.BAD_REQUEST);
            if (error.response) {
              expect(error.response.data).toMatchObject({
                success: false,
                error: {
                  code: 'VALIDATION_ERROR',
                  message: 'Required fields are missing',
                  details: {
                    missingFields: ['accessToken', 'qhUserId'],
                  },
                },
              });
            }
          }
        });
      });
    });
  });
});
