# TODO

## Done ✅

- [x] Fix Pact mock server concurrency limitations, mock server cleanup issues and failing tests (all the tests seem to pass, when run individually, but together half of all tests fail)
- [x] Fix lint errors in first commit
- [x] Move `/pacts` to `/tests/contracts/pacts`
- [x] DRY fail function
- [x] Check if `run-contract-tests.js` is needed

## To Be Done 🚧

- [ ] Move `/tests/contracts` to `/contracts`
- [ ] Remove redudant `.gitkeep`s and directories
- [ ] Remove redundant comments
- [ ] Fix `Failed to initialise global tracing subscriber - a global default trace dispatcher has already been set` error
- [ ] Check if actual providers are being tested
- [ ] Currently, 31 endpoints are being tested, check if there are any remaining endpoints that need to be tested
