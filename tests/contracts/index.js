/**
 * Contract test utilities index
 * Exports all contract testing utilities and helpers
 */

// Core utilities
const ContractTestBase = require('./helpers/ContractTestBase');
const mockDataGenerators = require('./helpers/mockDataGenerators');
const schemaValidators = require('./helpers/schemaValidators');
const authTokenMocks = require('./helpers/authTokenMocks');

// Shared utilities
const constants = require('./shared/constants');
const testUtils = require('./shared/testUtils');

// Configuration
const pactConfig = require('./pact.config');

module.exports = {
  // Core classes and utilities
  ContractTestBase,

  // Mock data generators
  ...mockDataGenerators,

  // Schema validation
  ...schemaValidators,

  // Authentication utilities
  ...authTokenMocks,

  // Constants and configuration
  ...constants,

  // Test utilities
  ...testUtils,

  // Pact configuration
  pactConfig,
};
