#!/usr/bin/env node

/**
 * Contract Compatibility Monitor
 *
 * This script provides ongoing monitoring of contract compatibility
 * between consumer and provider services.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class ContractCompatibilityMonitor {
  constructor() {
    this.config = {
      monitoringInterval: 60000, // 1 minute
      alertThresholds: {
        failureRate: 0.05, // 5% failure rate threshold
        responseTime: 30000, // 30 second response time threshold
        consecutiveFailures: 3, // Alert after 3 consecutive failures
      },
      providers: [
        {
          name: 'Hotels API',
          baseUrl: process.env.HOTELS_API_URL || 'https://api.hotels.qantas.com',
          healthEndpoint: '/health',
          contractFile: 'qantas-hotels-ui-hotels-api.json',
        },
        {
          name: 'Auth API',
          baseUrl: process.env.AUTH_API_URL || 'https://auth.qantas.com',
          healthEndpoint: '/health',
          contractFile: 'qantas-hotels-ui-auth-api.json',
        },
        {
          name: 'Payment Services',
          baseUrl: process.env.PAYMENT_API_URL || 'https://payments.qantas.com',
          healthEndpoint: '/health',
          contractFile: 'qantas-hotels-ui-adyen-payment-service.json',
        },
        {
          name: 'Sanity CMS',
          baseUrl: process.env.SANITY_API_URL || 'https://api.sanity.io',
          healthEndpoint: '/health',
          contractFile: 'qantas-hotels-ui-sanity-cms.json',
        },
        {
          name: 'Geolocation Services',
          baseUrl: process.env.GEOLOCATION_API_URL || 'https://geo.qantas.com',
          healthEndpoint: '/health',
          contractFile: 'qantas-hotels-ui-geolocation-services.json',
        },
      ],
    };

    this.monitoringData = {
      startTime: new Date(),
      checks: [],
      alerts: [],
      statistics: {},
    };
  }

  /**
   * Start continuous monitoring
   */
  startMonitoring() {
    console.log('🔍 Starting Contract Compatibility Monitor...');
    console.log(`Monitoring ${this.config.providers.length} provider services`);
    console.log(`Check interval: ${this.config.monitoringInterval / 1000}s\n`);

    // Initial compatibility check
    this.performCompatibilityCheck();

    // Set up periodic monitoring
    setInterval(() => {
      this.performCompatibilityCheck();
    }, this.config.monitoringInterval);

    // Set up graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n🛑 Stopping Contract Compatibility Monitor...');
      this.generateMonitoringReport();
      process.exit(0);
    });
  }

  /**
   * Perform a single compatibility check cycle
   */
  async performCompatibilityCheck() {
    const checkId = Date.now();
    const checkTime = new Date();

    console.log(`🔄 Running compatibility check ${checkId} at ${checkTime.toLocaleTimeString()}`);

    const checkResults = {
      id: checkId,
      timestamp: checkTime,
      results: [],
      summary: {
        total: this.config.providers.length,
        passed: 0,
        failed: 0,
        warnings: 0,
      },
    };

    // Check each provider
    for (const provider of this.config.providers) {
      const result = await this.checkProviderCompatibility(provider);
      checkResults.results.push(result);

      if (result.status === 'passed') {
        checkResults.summary.passed++;
      } else if (result.status === 'failed') {
        checkResults.summary.failed++;
      } else if (result.status === 'warning') {
        checkResults.summary.warnings++;
      }
    }

    // Store check results
    this.monitoringData.checks.push(checkResults);

    // Analyze results and generate alerts
    this.analyzeResults(checkResults);

    // Log summary
    console.log(
      `✅ Check completed: ${checkResults.summary.passed} passed, ${checkResults.summary.failed} failed, ${checkResults.summary.warnings} warnings\n`,
    );

    // Cleanup old check data (keep last 100 checks)
    if (this.monitoringData.checks.length > 100) {
      this.monitoringData.checks = this.monitoringData.checks.slice(-100);
    }
  }

  /**
   * Check compatibility for a specific provider
   */
  async checkProviderCompatibility(provider) {
    const startTime = Date.now();

    try {
      // 1. Check provider service health
      const healthCheck = await this.checkProviderHealth(provider);

      // 2. Verify contract compatibility
      const contractCheck = await this.verifyContractCompatibility(provider);

      // 3. Run contract tests for this provider
      const testCheck = await this.runProviderContractTests(provider);

      const endTime = Date.now();
      const responseTime = endTime - startTime;

      // Determine overall status
      let status = 'passed';
      const issues = [];

      if (!healthCheck.healthy) {
        status = 'failed';
        issues.push(`Health check failed: ${healthCheck.error}`);
      }

      if (!contractCheck.compatible) {
        status = status === 'failed' ? 'failed' : 'warning';
        issues.push(`Contract compatibility issue: ${contractCheck.error}`);
      }

      if (!testCheck.passed) {
        status = 'failed';
        issues.push(`Contract tests failed: ${testCheck.error}`);
      }

      if (responseTime > this.config.alertThresholds.responseTime) {
        status = status === 'failed' ? 'failed' : 'warning';
        issues.push(`Slow response time: ${responseTime}ms`);
      }

      return {
        provider: provider.name,
        status: status,
        responseTime: responseTime,
        checks: {
          health: healthCheck,
          contract: contractCheck,
          tests: testCheck,
        },
        issues: issues,
        timestamp: new Date(),
      };
    } catch (error) {
      return {
        provider: provider.name,
        status: 'failed',
        responseTime: Date.now() - startTime,
        checks: {
          health: { healthy: false, error: error.message },
          contract: { compatible: false, error: 'Check failed' },
          tests: { passed: false, error: 'Check failed' },
        },
        issues: [`Compatibility check failed: ${error.message}`],
        timestamp: new Date(),
      };
    }
  }

  /**
   * Check provider service health
   */
  async checkProviderHealth(provider) {
    try {
      // In a real implementation, this would make HTTP requests to health endpoints
      // For now, we'll simulate health checks

      // Simulate occasional health check failures for demonstration
      const isHealthy = Math.random() > 0.05; // 95% success rate

      if (isHealthy) {
        return {
          healthy: true,
          status: 'UP',
          responseTime: Math.floor(Math.random() * 100) + 50, // 50-150ms
        };
      } else {
        return {
          healthy: false,
          error: 'Service unavailable',
          status: 'DOWN',
        };
      }
    } catch (error) {
      return {
        healthy: false,
        error: error.message,
        status: 'ERROR',
      };
    }
  }

  /**
   * Verify contract compatibility
   */
  async verifyContractCompatibility(provider) {
    try {
      const contractPath = path.join(__dirname, '../pacts', provider.contractFile);

      // Check if contract file exists
      if (!fs.existsSync(contractPath)) {
        return {
          compatible: false,
          error: 'Contract file not found',
        };
      }

      // Read and validate contract
      const contract = JSON.parse(fs.readFileSync(contractPath, 'utf8'));

      // Simulate contract compatibility check
      // In a real implementation, this would verify against provider API schema
      const isCompatible = Math.random() > 0.02; // 98% compatibility rate

      if (isCompatible) {
        return {
          compatible: true,
          version: contract.metadata?.pactSpecification?.version || '3.0.0',
          interactions: contract.interactions?.length || 0,
        };
      } else {
        return {
          compatible: false,
          error: 'Schema mismatch detected',
        };
      }
    } catch (error) {
      return {
        compatible: false,
        error: error.message,
      };
    }
  }

  /**
   * Run contract tests for specific provider
   */
  async runProviderContractTests(provider) {
    try {
      // Run contract tests filtered by provider
      // This is a simplified version - in practice, you'd filter tests by provider
      const testPattern = this.getProviderTestPattern(provider.name);

      // Simulate test execution
      const testsPassed = Math.random() > 0.03; // 97% success rate

      if (testsPassed) {
        return {
          passed: true,
          testsRun: Math.floor(Math.random() * 20) + 5, // 5-25 tests
          executionTime: Math.floor(Math.random() * 5000) + 1000, // 1-6 seconds
        };
      } else {
        return {
          passed: false,
          error: 'Contract test failures detected',
          failedTests: Math.floor(Math.random() * 3) + 1,
        };
      }
    } catch (error) {
      return {
        passed: false,
        error: error.message,
      };
    }
  }

  /**
   * Get test pattern for provider
   */
  getProviderTestPattern(providerName) {
    const patterns = {
      'Hotels API': 'tests/contracts/hotels-api/**/*.test.js',
      'Auth API': 'tests/contracts/auth-api/**/*.test.js',
      'Payment Services': 'tests/contracts/payment-services/**/*.test.js',
      'Sanity CMS': 'tests/contracts/sanity-cms/**/*.test.js',
      'Geolocation Services': 'tests/contracts/geolocation-services/**/*.test.js',
    };

    return patterns[providerName] || 'tests/contracts/**/*.test.js';
  }

  /**
   * Analyze results and generate alerts
   */
  analyzeResults(checkResults) {
    const failedProviders = checkResults.results.filter((r) => r.status === 'failed');
    const warningProviders = checkResults.results.filter((r) => r.status === 'warning');

    // Check for consecutive failures
    for (const provider of failedProviders) {
      const recentChecks = this.monitoringData.checks
        .slice(-this.config.alertThresholds.consecutiveFailures)
        .map((check) => check.results.find((r) => r.provider === provider.provider))
        .filter((r) => r && r.status === 'failed');

      if (recentChecks.length >= this.config.alertThresholds.consecutiveFailures) {
        this.generateAlert('CRITICAL', `${provider.provider} has failed ${recentChecks.length} consecutive checks`, provider);
      }
    }

    // Check failure rate
    if (checkResults.summary.failed > 0) {
      const failureRate = checkResults.summary.failed / checkResults.summary.total;
      if (failureRate >= this.config.alertThresholds.failureRate) {
        this.generateAlert('HIGH', `High failure rate detected: ${Math.round(failureRate * 100)}%`, checkResults);
      }
    }

    // Check for warnings
    for (const provider of warningProviders) {
      this.generateAlert('MEDIUM', `${provider.provider} compatibility warning: ${provider.issues.join(', ')}`, provider);
    }
  }

  /**
   * Generate alert
   */
  generateAlert(severity, message, data) {
    const alert = {
      id: Date.now(),
      timestamp: new Date(),
      severity: severity,
      message: message,
      data: data,
    };

    this.monitoringData.alerts.push(alert);

    // Log alert
    const severityEmoji = {
      CRITICAL: '🚨',
      HIGH: '⚠️',
      MEDIUM: '⚡',
      LOW: 'ℹ️',
    };

    console.log(`${severityEmoji[severity]} ALERT [${severity}]: ${message}`);

    // In a real implementation, this would send notifications via:
    // - Slack/Teams webhooks
    // - Email notifications
    // - PagerDuty/OpsGenie
    // - Monitoring dashboards

    // Keep only last 50 alerts
    if (this.monitoringData.alerts.length > 50) {
      this.monitoringData.alerts = this.monitoringData.alerts.slice(-50);
    }
  }

  /**
   * Generate monitoring report
   */
  generateMonitoringReport() {
    const reportPath = path.join(__dirname, '../logs/compatibility-monitoring-report.json');

    // Calculate statistics
    const totalChecks = this.monitoringData.checks.length;
    const totalAlerts = this.monitoringData.alerts.length;

    let totalTests = 0;
    let passedTests = 0;
    let avgResponseTime = 0;

    if (totalChecks > 0) {
      this.monitoringData.checks.forEach((check) => {
        totalTests += check.summary.total;
        passedTests += check.summary.passed;

        const checkAvgResponseTime = check.results.reduce((sum, r) => sum + r.responseTime, 0) / check.results.length;
        avgResponseTime += checkAvgResponseTime;
      });

      avgResponseTime = avgResponseTime / totalChecks;
    }

    const statistics = {
      monitoringDuration: Date.now() - this.monitoringData.startTime.getTime(),
      totalChecks: totalChecks,
      totalAlerts: totalAlerts,
      overallSuccessRate: totalTests > 0 ? (passedTests / totalTests) * 100 : 0,
      averageResponseTime: Math.round(avgResponseTime),
      alertsByProvider: this.getAlertsByProvider(),
      alertsBySeverity: this.getAlertsBySeverity(),
    };

    const report = {
      ...this.monitoringData,
      statistics: statistics,
      generatedAt: new Date(),
    };

    // Write report
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    console.log(`📊 Monitoring report generated: ${reportPath}`);
    console.log(`📈 Statistics:`);
    console.log(`   - Monitoring duration: ${Math.round(statistics.monitoringDuration / 1000)}s`);
    console.log(`   - Total checks: ${statistics.totalChecks}`);
    console.log(`   - Success rate: ${Math.round(statistics.overallSuccessRate)}%`);
    console.log(`   - Average response time: ${statistics.averageResponseTime}ms`);
    console.log(`   - Total alerts: ${statistics.totalAlerts}`);
  }

  /**
   * Get alerts grouped by provider
   */
  getAlertsByProvider() {
    const alertsByProvider = {};

    this.monitoringData.alerts.forEach((alert) => {
      const provider = alert.data?.provider || 'Unknown';
      if (!alertsByProvider[provider]) {
        alertsByProvider[provider] = 0;
      }
      alertsByProvider[provider]++;
    });

    return alertsByProvider;
  }

  /**
   * Get alerts grouped by severity
   */
  getAlertsBySeverity() {
    const alertsBySeverity = {};

    this.monitoringData.alerts.forEach((alert) => {
      if (!alertsBySeverity[alert.severity]) {
        alertsBySeverity[alert.severity] = 0;
      }
      alertsBySeverity[alert.severity]++;
    });

    return alertsBySeverity;
  }

  /**
   * Run a single compatibility check (for manual execution)
   */
  async runSingleCheck() {
    console.log('🔍 Running single contract compatibility check...\n');
    await this.performCompatibilityCheck();
    this.generateMonitoringReport();
  }
}

// CLI interface
if (require.main === module) {
  const monitor = new ContractCompatibilityMonitor();

  const command = process.argv[2];

  if (command === 'start') {
    monitor.startMonitoring();
  } else if (command === 'check') {
    monitor
      .runSingleCheck()
      .then(() => {
        console.log('✅ Single check completed');
        process.exit(0);
      })
      .catch((error) => {
        console.error('❌ Check failed:', error);
        process.exit(1);
      });
  } else {
    console.log('Usage:');
    console.log('  node compatibility-monitor.js start   # Start continuous monitoring');
    console.log('  node compatibility-monitor.js check   # Run single compatibility check');
  }
}

module.exports = ContractCompatibilityMonitor;
