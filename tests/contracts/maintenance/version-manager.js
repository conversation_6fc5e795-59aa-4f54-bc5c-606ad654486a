#!/usr/bin/env node

/**
 * Contract Test Version Manager
 *
 * This script manages contract test evolution and versioning processes
 * to ensure backward compatibility and smooth transitions.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class ContractVersionManager {
  constructor() {
    this.config = {
      contractsDir: path.join(__dirname, '../pacts'),
      versionsDir: path.join(__dirname, '../versions'),
      backupDir: path.join(__dirname, '../backups'),
      versioningStrategy: 'semantic', // semantic, timestamp, or incremental
      retentionPolicy: {
        maxVersions: 10,
        maxAge: 90, // days
      },
    };

    this.currentVersion = this.getCurrentVersion();
  }

  /**
   * Initialize version management system
   */
  async initialize() {
    console.log('🚀 Initializing Contract Version Management System...\n');

    // Create necessary directories
    this.ensureDirectories();

    // Initialize version tracking
    await this.initializeVersionTracking();

    // Create initial version if none exists
    if (!this.currentVersion) {
      await this.createInitialVersion();
    }

    console.log('✅ Contract version management system initialized\n');
  }

  /**
   * Create a new contract version
   */
  async createVersion(options = {}) {
    const { type = 'minor', description = 'Contract update', breaking = false, force = false } = options;

    console.log(`📦 Creating new contract version (${type})...`);

    try {
      // Validate current contracts
      await this.validateContracts();

      // Generate new version number
      const newVersion = this.generateVersionNumber(type);

      // Check for breaking changes
      const breakingChanges = await this.detectBreakingChanges();

      if (breakingChanges.length > 0 && !breaking && !force) {
        throw new Error(`Breaking changes detected: ${breakingChanges.join(', ')}. Use --breaking flag to proceed.`);
      }

      // Create version snapshot
      await this.createVersionSnapshot(newVersion, {
        type,
        description,
        breaking: breaking || breakingChanges.length > 0,
        breakingChanges,
        timestamp: new Date(),
        contracts: await this.getCurrentContracts(),
      });

      // Update version tracking
      await this.updateVersionTracking(newVersion);

      // Cleanup old versions
      await this.cleanupOldVersions();

      console.log(`✅ Version ${newVersion} created successfully`);

      return newVersion;
    } catch (error) {
      console.error(`❌ Failed to create version: ${error.message}`);
      throw error;
    }
  }

  /**
   * Compare contract versions
   */
  async compareVersions(version1, version2) {
    console.log(`🔍 Comparing versions ${version1} and ${version2}...`);

    const contracts1 = await this.getVersionContracts(version1);
    const contracts2 = await this.getVersionContracts(version2);

    const comparison = {
      version1: version1,
      version2: version2,
      timestamp: new Date(),
      changes: {
        added: [],
        removed: [],
        modified: [],
        breaking: [],
      },
      compatibility: 'compatible', // compatible, breaking, unknown
    };

    // Compare contracts
    const contractNames1 = Object.keys(contracts1);
    const contractNames2 = Object.keys(contracts2);

    // Find added contracts
    comparison.changes.added = contractNames2.filter((name) => !contractNames1.includes(name));

    // Find removed contracts
    comparison.changes.removed = contractNames1.filter((name) => !contractNames2.includes(name));

    // Find modified contracts
    for (const contractName of contractNames1.filter((name) => contractNames2.includes(name))) {
      const contract1 = contracts1[contractName];
      const contract2 = contracts2[contractName];

      const contractChanges = this.compareContracts(contract1, contract2);

      if (contractChanges.length > 0) {
        comparison.changes.modified.push({
          contract: contractName,
          changes: contractChanges,
        });

        // Check for breaking changes
        const breakingChanges = contractChanges.filter((change) => change.breaking);
        if (breakingChanges.length > 0) {
          comparison.changes.breaking.push({
            contract: contractName,
            changes: breakingChanges,
          });
        }
      }
    }

    // Determine overall compatibility
    if (comparison.changes.breaking.length > 0 || comparison.changes.removed.length > 0) {
      comparison.compatibility = 'breaking';
    } else if (comparison.changes.added.length > 0 || comparison.changes.modified.length > 0) {
      comparison.compatibility = 'compatible';
    }

    console.log(`✅ Version comparison completed - ${comparison.compatibility}`);

    return comparison;
  }

  /**
   * Rollback to a previous version
   */
  async rollbackToVersion(targetVersion) {
    console.log(`⏪ Rolling back to version ${targetVersion}...`);

    try {
      // Validate target version exists
      if (!(await this.versionExists(targetVersion))) {
        throw new Error(`Version ${targetVersion} does not exist`);
      }

      // Create backup of current state
      const backupVersion = await this.createBackup('pre-rollback');

      // Restore contracts from target version
      await this.restoreVersionContracts(targetVersion);

      // Update version tracking
      await this.updateVersionTracking(targetVersion);

      console.log(`✅ Successfully rolled back to version ${targetVersion}`);
      console.log(`📦 Backup created: ${backupVersion}`);

      return {
        targetVersion,
        backupVersion,
        timestamp: new Date(),
      };
    } catch (error) {
      console.error(`❌ Rollback failed: ${error.message}`);
      throw error;
    }
  }

  /**
   * List all available versions
   */
  async listVersions() {
    console.log('📋 Listing contract versions...\n');

    const versionHistory = await this.getVersionHistory();

    if (versionHistory.length === 0) {
      console.log('No versions found');
      return [];
    }

    console.log('Available versions:');
    console.log('==================');

    versionHistory.forEach((version, index) => {
      const isCurrent = version.version === this.currentVersion;
      const marker = isCurrent ? '→' : ' ';
      const breaking = version.breaking ? '⚠️ ' : '';

      console.log(`${marker} ${breaking}${version.version} - ${version.description}`);
      console.log(`   Created: ${new Date(version.timestamp).toLocaleString()}`);
      console.log(`   Type: ${version.type}${version.breaking ? ' (breaking)' : ''}`);

      if (version.breakingChanges && version.breakingChanges.length > 0) {
        console.log(`   Breaking changes: ${version.breakingChanges.join(', ')}`);
      }

      if (index < versionHistory.length - 1) {
        console.log('');
      }
    });

    return versionHistory;
  }

  /**
   * Generate migration guide between versions
   */
  async generateMigrationGuide(fromVersion, toVersion) {
    console.log(`📖 Generating migration guide from ${fromVersion} to ${toVersion}...`);

    const comparison = await this.compareVersions(fromVersion, toVersion);

    const migrationGuide = {
      fromVersion,
      toVersion,
      generatedAt: new Date(),
      compatibility: comparison.compatibility,
      migrationSteps: [],
      warnings: [],
      recommendations: [],
    };

    // Generate migration steps based on changes
    if (comparison.changes.added.length > 0) {
      migrationGuide.migrationSteps.push({
        step: 'Handle new contracts',
        description: 'New contracts have been added that may require provider implementation',
        contracts: comparison.changes.added,
        action: 'Coordinate with provider teams to implement new contracts',
      });
    }

    if (comparison.changes.removed.length > 0) {
      migrationGuide.migrationSteps.push({
        step: 'Remove deprecated contracts',
        description: 'Contracts have been removed and are no longer supported',
        contracts: comparison.changes.removed,
        action: 'Update consumer code to remove references to deprecated contracts',
      });

      migrationGuide.warnings.push('Breaking change: Contracts have been removed');
    }

    if (comparison.changes.modified.length > 0) {
      migrationGuide.migrationSteps.push({
        step: 'Update modified contracts',
        description: 'Existing contracts have been modified',
        contracts: comparison.changes.modified.map((m) => m.contract),
        action: 'Review contract changes and update consumer/provider code accordingly',
      });
    }

    if (comparison.changes.breaking.length > 0) {
      migrationGuide.migrationSteps.push({
        step: 'Address breaking changes',
        description: 'Breaking changes require immediate attention',
        contracts: comparison.changes.breaking.map((b) => b.contract),
        action: 'Update code to handle breaking contract changes before deployment',
      });

      migrationGuide.warnings.push('Breaking changes detected - coordinate with all teams');
    }

    // Add recommendations
    if (comparison.compatibility === 'breaking') {
      migrationGuide.recommendations.push('Plan migration carefully due to breaking changes');
      migrationGuide.recommendations.push('Coordinate with all provider teams before deployment');
      migrationGuide.recommendations.push('Consider phased rollout to minimize impact');
    }

    if (comparison.changes.added.length > 0) {
      migrationGuide.recommendations.push('Verify provider readiness for new contracts');
    }

    // Write migration guide
    const guideContent = this.formatMigrationGuide(migrationGuide);
    const guidePath = path.join(this.config.versionsDir, `migration-${fromVersion}-to-${toVersion}.md`);
    fs.writeFileSync(guidePath, guideContent);

    console.log(`✅ Migration guide generated: ${guidePath}`);

    return migrationGuide;
  }

  /**
   * Validate contracts for consistency and correctness
   */
  async validateContracts() {
    console.log('🔍 Validating contracts...');

    const contracts = await this.getCurrentContracts();
    const validationResults = {
      valid: true,
      errors: [],
      warnings: [],
    };

    for (const [contractName, contract] of Object.entries(contracts)) {
      try {
        // Validate JSON structure
        if (!contract.consumer || !contract.provider || !contract.interactions) {
          validationResults.errors.push(`${contractName}: Missing required fields`);
          validationResults.valid = false;
        }

        // Validate interactions
        if (contract.interactions) {
          contract.interactions.forEach((interaction, index) => {
            if (!interaction.description || !interaction.request || !interaction.response) {
              validationResults.errors.push(`${contractName}: Interaction ${index} missing required fields`);
              validationResults.valid = false;
            }
          });
        }

        // Check for common issues
        if (contract.interactions && contract.interactions.length === 0) {
          validationResults.warnings.push(`${contractName}: No interactions defined`);
        }
      } catch (error) {
        validationResults.errors.push(`${contractName}: ${error.message}`);
        validationResults.valid = false;
      }
    }

    if (!validationResults.valid) {
      throw new Error(`Contract validation failed: ${validationResults.errors.join(', ')}`);
    }

    if (validationResults.warnings.length > 0) {
      console.log(`⚠️ Validation warnings: ${validationResults.warnings.join(', ')}`);
    }

    console.log('✅ Contract validation passed');
    return validationResults;
  }

  // Helper methods

  ensureDirectories() {
    [this.config.versionsDir, this.config.backupDir].forEach((dir) => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
  }

  getCurrentVersion() {
    const versionFile = path.join(this.config.versionsDir, 'current-version.json');

    if (fs.existsSync(versionFile)) {
      const versionData = JSON.parse(fs.readFileSync(versionFile, 'utf8'));
      return versionData.version;
    }

    return null;
  }

  async initializeVersionTracking() {
    const historyFile = path.join(this.config.versionsDir, 'version-history.json');

    if (!fs.existsSync(historyFile)) {
      fs.writeFileSync(historyFile, JSON.stringify([], null, 2));
    }
  }

  async createInitialVersion() {
    console.log('📦 Creating initial version...');

    const initialVersion = '1.0.0';

    await this.createVersionSnapshot(initialVersion, {
      type: 'initial',
      description: 'Initial contract version',
      breaking: false,
      breakingChanges: [],
      timestamp: new Date(),
      contracts: await this.getCurrentContracts(),
    });

    await this.updateVersionTracking(initialVersion);

    console.log(`✅ Initial version ${initialVersion} created`);
  }

  generateVersionNumber(type) {
    if (!this.currentVersion) {
      return '1.0.0';
    }

    const [major, minor, patch] = this.currentVersion.split('.').map(Number);

    switch (type) {
      case 'major':
        return `${major + 1}.0.0`;
      case 'minor':
        return `${major}.${minor + 1}.0`;
      case 'patch':
        return `${major}.${minor}.${patch + 1}`;
      default:
        throw new Error(`Unknown version type: ${type}`);
    }
  }

  async getCurrentContracts() {
    const contracts = {};

    if (fs.existsSync(this.config.contractsDir)) {
      const files = fs.readdirSync(this.config.contractsDir);

      for (const file of files.filter((f) => f.endsWith('.json'))) {
        const contractPath = path.join(this.config.contractsDir, file);
        const contractContent = JSON.parse(fs.readFileSync(contractPath, 'utf8'));
        contracts[file] = contractContent;
      }
    }

    return contracts;
  }

  async createVersionSnapshot(version, metadata) {
    const versionDir = path.join(this.config.versionsDir, version);

    if (!fs.existsSync(versionDir)) {
      fs.mkdirSync(versionDir, { recursive: true });
    }

    // Save contracts
    const contractsDir = path.join(versionDir, 'contracts');
    if (!fs.existsSync(contractsDir)) {
      fs.mkdirSync(contractsDir);
    }

    for (const [contractName, contract] of Object.entries(metadata.contracts)) {
      const contractPath = path.join(contractsDir, contractName);
      fs.writeFileSync(contractPath, JSON.stringify(contract, null, 2));
    }

    // Save metadata
    const metadataPath = path.join(versionDir, 'metadata.json');
    fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));
  }

  async updateVersionTracking(version) {
    // Update current version
    const currentVersionFile = path.join(this.config.versionsDir, 'current-version.json');
    fs.writeFileSync(currentVersionFile, JSON.stringify({ version, updatedAt: new Date() }, null, 2));

    // Update version history
    const historyFile = path.join(this.config.versionsDir, 'version-history.json');
    const history = JSON.parse(fs.readFileSync(historyFile, 'utf8'));

    const versionDir = path.join(this.config.versionsDir, version);
    const metadataPath = path.join(versionDir, 'metadata.json');
    const metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));

    history.unshift({
      version,
      ...metadata,
    });

    fs.writeFileSync(historyFile, JSON.stringify(history, null, 2));

    this.currentVersion = version;
  }

  async getVersionHistory() {
    const historyFile = path.join(this.config.versionsDir, 'version-history.json');

    if (fs.existsSync(historyFile)) {
      return JSON.parse(fs.readFileSync(historyFile, 'utf8'));
    }

    return [];
  }

  async detectBreakingChanges() {
    // This would implement logic to detect breaking changes
    // For now, return empty array
    return [];
  }

  compareContracts(contract1, contract2) {
    // This would implement detailed contract comparison
    // For now, return empty array
    return [];
  }

  async versionExists(version) {
    const versionDir = path.join(this.config.versionsDir, version);
    return fs.existsSync(versionDir);
  }

  async getVersionContracts(version) {
    const contractsDir = path.join(this.config.versionsDir, version, 'contracts');
    const contracts = {};

    if (fs.existsSync(contractsDir)) {
      const files = fs.readdirSync(contractsDir);

      for (const file of files.filter((f) => f.endsWith('.json'))) {
        const contractPath = path.join(contractsDir, file);
        contracts[file] = JSON.parse(fs.readFileSync(contractPath, 'utf8'));
      }
    }

    return contracts;
  }

  async createBackup(reason) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupVersion = `backup-${timestamp}-${reason}`;

    const contracts = await this.getCurrentContracts();

    await this.createVersionSnapshot(backupVersion, {
      type: 'backup',
      description: `Backup created for ${reason}`,
      breaking: false,
      breakingChanges: [],
      timestamp: new Date(),
      contracts: contracts,
    });

    return backupVersion;
  }

  async restoreVersionContracts(version) {
    const contracts = await this.getVersionContracts(version);

    // Clear current contracts
    if (fs.existsSync(this.config.contractsDir)) {
      const files = fs.readdirSync(this.config.contractsDir);
      files.forEach((file) => {
        if (file.endsWith('.json')) {
          fs.unlinkSync(path.join(this.config.contractsDir, file));
        }
      });
    }

    // Restore contracts from version
    for (const [contractName, contract] of Object.entries(contracts)) {
      const contractPath = path.join(this.config.contractsDir, contractName);
      fs.writeFileSync(contractPath, JSON.stringify(contract, null, 2));
    }
  }

  async cleanupOldVersions() {
    const history = await this.getVersionHistory();

    // Remove versions beyond retention policy
    if (history.length > this.config.retentionPolicy.maxVersions) {
      const versionsToRemove = history.slice(this.config.retentionPolicy.maxVersions);

      for (const version of versionsToRemove) {
        const versionDir = path.join(this.config.versionsDir, version.version);
        if (fs.existsSync(versionDir)) {
          fs.rmSync(versionDir, { recursive: true });
        }
      }

      // Update history
      const updatedHistory = history.slice(0, this.config.retentionPolicy.maxVersions);
      const historyFile = path.join(this.config.versionsDir, 'version-history.json');
      fs.writeFileSync(historyFile, JSON.stringify(updatedHistory, null, 2));
    }
  }

  formatMigrationGuide(guide) {
    return `# Contract Migration Guide

## Migration from ${guide.fromVersion} to ${guide.toVersion}

**Generated:** ${guide.generatedAt.toLocaleString()}  
**Compatibility:** ${guide.compatibility.toUpperCase()}  

${
  guide.warnings.length > 0
    ? `
## ⚠️ Warnings
${guide.warnings.map((w) => `- ${w}`).join('\n')}
`
    : ''
}

## Migration Steps

${guide.migrationSteps
  .map(
    (step, index) => `
### ${index + 1}. ${step.step}

${step.description}

**Affected Contracts:** ${Array.isArray(step.contracts) ? step.contracts.join(', ') : 'N/A'}  
**Action Required:** ${step.action}
`,
  )
  .join('\n')}

## Recommendations

${guide.recommendations.map((r) => `- ${r}`).join('\n')}

---
*Generated by Contract Version Manager*`;
  }
}

// CLI interface
if (require.main === module) {
  const manager = new ContractVersionManager();

  const command = process.argv[2];
  const args = process.argv.slice(3);

  async function runCommand() {
    try {
      switch (command) {
        case 'init':
          await manager.initialize();
          break;

        case 'create':
          const type = args[0] || 'minor';
          const description = args[1] || 'Contract update';
          const version = await manager.createVersion({ type, description });
          console.log(`Created version: ${version}`);
          break;

        case 'list':
          await manager.listVersions();
          break;

        case 'compare':
          const v1 = args[0];
          const v2 = args[1];
          if (!v1 || !v2) {
            console.error('Usage: compare <version1> <version2>');
            process.exit(1);
          }
          const comparison = await manager.compareVersions(v1, v2);
          console.log(JSON.stringify(comparison, null, 2));
          break;

        case 'rollback':
          const targetVersion = args[0];
          if (!targetVersion) {
            console.error('Usage: rollback <version>');
            process.exit(1);
          }
          await manager.rollbackToVersion(targetVersion);
          break;

        case 'migrate':
          const fromVer = args[0];
          const toVer = args[1];
          if (!fromVer || !toVer) {
            console.error('Usage: migrate <from-version> <to-version>');
            process.exit(1);
          }
          await manager.generateMigrationGuide(fromVer, toVer);
          break;

        default:
          console.log('Usage:');
          console.log('  node version-manager.js init                    # Initialize version management');
          console.log('  node version-manager.js create [type] [desc]    # Create new version');
          console.log('  node version-manager.js list                    # List all versions');
          console.log('  node version-manager.js compare <v1> <v2>       # Compare versions');
          console.log('  node version-manager.js rollback <version>      # Rollback to version');
          console.log('  node version-manager.js migrate <from> <to>     # Generate migration guide');
      }
    } catch (error) {
      console.error('❌ Command failed:', error.message);
      process.exit(1);
    }
  }

  runCommand();
}

module.exports = ContractVersionManager;
