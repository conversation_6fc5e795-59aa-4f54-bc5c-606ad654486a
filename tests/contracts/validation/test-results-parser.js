#!/usr/bin/env node

/**
 * Simple test results parser for contract tests
 */

const { execSync } = require('child_process');

function runContractTestsAndParseResults() {
  try {
    // Run contract tests and capture output
    const output = execSync('yarn test:contracts --verbose', {
      encoding: 'utf8',
      stdio: 'pipe',
    });

    console.log('Raw output sample:');
    console.log(output.split('\n').slice(-10).join('\n'));

    // Parse the results
    const lines = output.split('\n');

    // Find summary lines
    const summaryLine = lines.find((line) => line.includes('Test Suites:') && line.includes('passed'));
    const testLine = lines.find((line) => line.includes('Tests:') && line.includes('passed'));

    console.log('\nFound summary line:', summaryLine);
    console.log('Found test line:', testLine);

    let testSuites = { passed: 0, total: 0 };
    let tests = { passed: 0, total: 0 };

    if (summaryLine) {
      const suiteMatch = summaryLine.match(/Test Suites:\s*(\d+)\s*passed.*?(\d+)\s*total/);
      if (suiteMatch) {
        testSuites = { passed: parseInt(suiteMatch[1]), total: parseInt(suiteMatch[2]) };
      }
      console.log('Parsed test suites:', testSuites);
    }

    if (testLine) {
      const testMatch = testLine.match(/Tests:\s+(\d+)\s*passed.*?(\d+)\s*total/);
      if (testMatch) {
        tests = { passed: parseInt(testMatch[1]), total: parseInt(testMatch[2]) };
      }
      console.log('Parsed tests:', tests);
    }

    return {
      totalTests: tests.total,
      passedTests: tests.passed,
      failedTests: tests.total - tests.passed,
      testSuites: testSuites,
    };
  } catch (error) {
    console.error('Error running tests:', error.message);
    return null;
  }
}

if (require.main === module) {
  const results = runContractTestsAndParseResults();
  console.log('\nFinal results:', results);
}

module.exports = { runContractTestsAndParseResults };
