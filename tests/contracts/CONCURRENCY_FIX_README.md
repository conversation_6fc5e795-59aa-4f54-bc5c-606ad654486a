# Pact Contract Tests - Concurrency Fix

This document explains the fixes implemented to resolve Pact mock server concurrency limitations, cleanup issues, and failing contract tests.

## Problems Identified

1. **Port Conflicts**: Tests were hardcoding ports (1234, 1237), causing conflicts when running concurrently
2. **Resource Leaks**: Mock servers weren't being properly cleaned up between test runs
3. **Shared State**: Tests were sharing state and not properly isolated
4. **No Concurrency Management**: Tests weren't designed to run in parallel safely

## Solutions Implemented

### 1. Dynamic Port Management

**File**: `tests/contracts/helpers/ContractTestBase.js`

- Implemented `PortManager` class to dynamically allocate available ports
- Each test instance gets a unique port to avoid conflicts
- Ports are properly released after test completion

```javascript
class PortManager {
  static getAvailablePort() {
    let port = this.basePort;
    while (this.usedPorts.has(port)) {
      port++;
    }
    this.usedPorts.add(port);
    return port;
  }
}
```

### 2. Enhanced Test Runner

**File**: `tests/contracts/helpers/testRunner.js`

- Created `createContractTestSuite()` function for consistent test setup
- Implemented `TestRegistry` for tracking and cleaning up test instances
- Added proper error handling and resource cleanup

```javascript
function createContractTestSuite(suiteName, providerName, testFn, options = {}) {
  describe(suiteName, () => {
    let contractTest;
    
    beforeAll(async () => {
      contractTest = new ContractTestBase(providerName, options);
      await contractTest.setup();
    });
    
    afterAll(async () => {
      await contractTest.teardown();
    });
    
    testFn(contractTest);
  });
}
```

### 3. Improved Jest Configuration

**File**: `jest/jest.config.contracts.ts`

- Limited concurrency to prevent resource exhaustion: `maxWorkers: 4, maxConcurrency: 4`
- Increased timeout for setup/teardown: `testTimeout: 60000`
- Optimized for contract test requirements

### 4. Robust Cleanup Mechanisms

**File**: `tests/contracts/setup/contractTestSetup.js`

- Added global cleanup handlers for various exit scenarios
- Handles process termination signals (SIGINT, SIGTERM)
- Manages uncaught exceptions and unhandled rejections

### 5. Migration Pattern

**Example**: Updated `tests/contracts/hotels-api/bookings.contract.test.js`

**Before**:
```javascript
describe('Hotels API Booking Endpoints Contract Tests', () => {
  let provider;
  const mockServerBaseUrl = 'http://localhost:1234';

  beforeAll(() => {
    provider = new Pact({
      consumer: 'qantas-hotels-ui',
      provider: 'hotels-api',
      port: 1234, // Hardcoded port!
      // ...
    });
    return provider.setup();
  });
  // ...
});
```

**After**:
```javascript
createContractTestSuite('Hotels API Booking Endpoints Contract Tests', 'hotels-api', (contractTest) => {
  const getMockServerUrl = createMockServerUrlGetter(contractTest);
  
  // Tests use dynamic URLs: getMockServerUrl()
  // No manual setup/teardown needed
});
```

## Usage Guide

### Writing New Contract Tests

1. Import the test runner utilities:
```javascript
const { createContractTestSuite, createMockServerUrlGetter, addInteractionSafely } = require('../helpers/testRunner');
```

2. Use the new test suite pattern:
```javascript
createContractTestSuite('My API Contract Tests', 'my-api', (contractTest) => {
  const getMockServerUrl = createMockServerUrlGetter(contractTest);
  
  describe('GET /endpoint', () => {
    beforeEach(async () => {
      await addInteractionSafely(contractTest, {
        // interaction definition
      });
    });
    
    it('should work correctly', async () => {
      const response = await axios.get(`${getMockServerUrl()}/endpoint`);
      // assertions
    });
  });
});
```

### Migrating Existing Tests

1. Run the migration script:
```bash
node scripts/migrate-contract-tests.js
```

2. Review and test the migrated files
3. Remove backup files once satisfied

### Running Contract Tests

```bash
# Run all contract tests
npm run test:contracts

# Run specific test file
npm run test:contracts -- tests/contracts/hotels-api/bookings.contract.test.js

# Run with watch mode
npm run test:contracts:watch
```

## Key Benefits

1. **Concurrency Safe**: Tests can run in parallel without port conflicts
2. **Resource Management**: Proper cleanup prevents resource leaks
3. **Isolation**: Each test gets its own mock server instance
4. **Reliability**: Robust error handling and cleanup mechanisms
5. **Maintainability**: Consistent patterns across all contract tests

## Troubleshooting

### Port Already in Use Errors
- The new system automatically handles port allocation
- If you still see port conflicts, check for manually hardcoded ports

### Mock Server Cleanup Issues
- The TestRegistry handles cleanup automatically
- Process termination signals are properly handled
- Emergency cleanup methods are available

### Test Timeouts
- Increased timeout to 60 seconds for setup/teardown
- Adjust `testTimeout` in Jest config if needed

### Memory Leaks
- All test instances are tracked and cleaned up
- Ports are properly released after use
- Process handlers ensure cleanup on exit

## Files Modified

- `tests/contracts/helpers/ContractTestBase.js` - Enhanced base class with port management
- `tests/contracts/helpers/testRunner.js` - New test runner utilities
- `jest/jest.config.contracts.ts` - Updated Jest configuration
- `tests/contracts/setup/contractTestSetup.js` - Enhanced setup with cleanup handlers
- `tests/contracts/hotels-api/bookings.contract.test.js` - Example migration
- `scripts/migrate-contract-tests.js` - Migration script for other test files

## Next Steps

1. Migrate remaining contract test files using the migration script
2. Monitor test execution for any remaining issues
3. Consider adding performance metrics for test execution times
4. Update CI/CD pipeline to take advantage of improved concurrency