{"metadata": {"generatedAt": "2025-09-04T13:27:13.256Z", "analysisWindow": 30, "dataPoints": 96, "version": "1.0.0"}, "summary": {"overallHealth": 70, "keyMetrics": {"successRate": 0, "averageExecutionTime": 27, "flakinessRate": 2.65, "endpointCoverage": 89}, "recommendationCount": 2}, "analysis": {"reliability": {"overall": {"totalRuns": 96, "successfulRuns": 0, "successRate": 0, "meetsThreshold": false}, "byEnvironment": {"ci": {"runs": 55, "successRate": 0}, "local": {"runs": 41, "successRate": 0}}, "byTrigger": {"pr": {"runs": 41, "successRate": 0}, "merge": {"runs": 55, "successRate": 0}}}, "performance": {"overall": {"averageTime": 27246, "minTime": 20433, "maxTime": 33809, "meetsThreshold": true}, "trend": {"recentAverage": 29029, "previousAverage": 27811, "changePercent": 4.38, "improving": false}, "outliers": {"count": 0, "threshold": 34070, "examples": []}}, "coverage": {"endpoints": {"total": 35, "covered": 31, "coveragePercent": 88.57142857142857}, "scenarios": {"total": 150, "covered": 135, "coveragePercent": 90}, "errorCases": {"total": 45, "covered": 42, "coveragePercent": 93.33333333333333}, "recentChanges": {"newEndpoints": 2, "newEndpointsCovered": 1, "removedEndpoints": 0, "modifiedEndpoints": 3}}, "flakiness": {"overall": {"flakyTestCount": 2, "averageFlakinessRate": 2.65, "meetsThreshold": true}, "flakyTests": [{"testName": "Hotels API - Property availability search with filters", "flakinessRate": 3.2, "totalRuns": 125, "intermittentFailures": 4, "lastFailure": "2025-09-02T13:27:13.253Z", "commonFailureReasons": ["Timeout", "Mock service startup delay"]}, {"testName": "Payment Services - Points calculation with promotional campaign", "flakinessRate": 2.1, "totalRuns": 95, "intermittentFailures": 2, "lastFailure": "2025-08-30T13:27:13.253Z", "commonFailureReasons": ["Race condition in mock setup"]}], "recommendations": ["Add retry logic for timeout-prone tests", "Improve mock service startup reliability", "Implement better test isolation"]}, "failurePatterns": {"totalFailures": 96, "byEnvironment": {"local": 41, "CI": 55}, "byBranch": {"main": 19, "feature": 77}, "byTime": {"afternoon": 11, "morning": 22, "night": 30, "evening": 33}, "commonCauses": ["Mock service timeout", "Schema validation failure", "Network connectivity issues", "Test data inconsistency"], "trends": {"increasingFailures": false, "mostProblematicEnvironment": "CI"}}, "recommendations": [{"category": "Reliability", "priority": "High", "issue": "Success rate (0%) below threshold (95%)", "recommendation": "Investigate and fix failing tests to improve overall reliability", "actions": ["Review recent test failures", "Improve test stability", "Add better error handling"]}, {"category": "Coverage", "priority": "Medium", "issue": "Endpoint coverage (89%) could be improved", "recommendation": "Add contract tests for uncovered API endpoints", "actions": ["Identify uncovered endpoints", "Prioritize critical endpoints", "Create contract tests for new endpoints"]}]}, "rawData": {"sampleSize": 96, "dateRange": {"from": "2025-08-06T13:27:13.251Z", "to": "2025-09-05T07:27:13.251Z"}}}