# Contract Test Endpoint Coverage Analysis

## Summary

**Current Status**: 31 unique API endpoints are being tested across 111 test interactions.

**Coverage Assessment**: Good coverage of core functionality, but several important endpoints are missing.

## Currently Tested Endpoints (31)

### Hotels API (15 endpoints)
- ✅ `POST /bookings` - Create booking
- ✅ `GET /bookings/{id}` - Fetch booking by ID
- ✅ `GET /properties` - Search properties
- ✅ `GET /properties/{id}` - Fetch property details
- ✅ `GET /properties/{id}/availability` - Search property availability
- ✅ `GET /properties/{id}/related` - Related property availability
- ✅ `GET /exclusive-offers` - Fetch exclusive offers
- ✅ `GET /locations/{location}/availability` - Location availability search
- ✅ `POST /quotes` - Create quote
- ✅ `GET /member-details/{memberId}` - Member details retrieval
- ✅ `GET /member-favourites` - Member favourites management
- ✅ `POST /member-favourites` - Add property to favourites
- ✅ `DELETE /member-favourites/{favouriteId}` - Remove property from favourites
- ✅ `PUT /member-details/{memberId}` - Update member profile

### Authentication API (2 endpoints)
- ✅ `GET /validate` - Token validation
- ✅ `POST /authenticate` - Member authentication

### Payment Services (8 endpoints)
- ✅ `POST /qepg/get-payment-methods` - Retrieve payment methods
- ✅ `POST /payments` - Process payment
- ✅ `GET /payment-status/{pspReference}` - Check payment status
- ✅ `GET /points-burn-v2` - Fetch points burn tiers
- ✅ `GET /points-burn-luxe` - Fetch luxury points burn tiers
- ✅ `POST /points/calculate-burn` - Calculate points burn amount
- ✅ `POST /points/calculate-earn` - Calculate points earn amount
- ✅ `POST /points/process-combination-payment` - Process points and cash payment

### Content Management (4 endpoints)
- ✅ `GET /content` - FAQ content
- ✅ `GET /content/preview` - Preview content
- ✅ `GET /campaigns` - Active campaigns
- ✅ `GET /site-message` - Site message

### Geolocation Services (3 endpoints)
- ✅ `GET /locations` - Location search
- ✅ `GET /geolocation` - User geolocation detection
- ✅ `GET /locations/{id}/boundaries` - Location boundaries

## Missing Endpoints (Identified 12)

### Hotels API (8 missing)
- ❌ `GET /locations/deals` - Location deals (fetchDeals.ts)
- ❌ `GET /locations/deals/{dealType}` - Deals by type (fetchDeals.ts)
- ❌ `POST /vouchers/check` - Voucher validation (checkVoucher.js)
- ❌ `GET /vouchers/balance` - Voucher balance check (checkVoucherBalance.js)
- ❌ `GET /iSeatz` - Smartfill/autocomplete (fetchSmartfill.ts)
- ❌ `POST /airbnb/search` - Airbnb search integration (searchAirbnb.ts)
- ❌ `GET /exclusive-offers/{propertyId}/availability` - Calendar availability (fetchCalendar.ts)
- ❌ `GET /exclusive-offers/{propertyId}/availability/{offerId}` - Offer-specific calendar (fetchCalendar.ts)

### Content Management (2 missing)
- ❌ `GET /content/{type}` - Specific content type (fetchContent.ts)
- ❌ `GET /campaigns/{slug}` - Specific campaign (fetchCampaign.js)

### Support/Enquiry (2 missing)
- ❌ `POST /enquiries` - Create enquiry (createEnquiry.js)
- ❌ `POST /callback-requests` - Create callback request (createCallbackRequest.js)

## Priority Recommendations

### High Priority (Core Business Logic)
1. **Voucher endpoints** - Critical for payment processing
   - `POST /vouchers/check`
   - `GET /vouchers/balance`

2. **Deals endpoints** - Important for marketing and promotions
   - `GET /locations/deals`
   - `GET /locations/deals/{dealType}`

3. **Calendar availability** - Essential for exclusive offers
   - `GET /exclusive-offers/{propertyId}/availability`

### Medium Priority (User Experience)
4. **Smartfill/Autocomplete** - Improves search UX
   - `GET /iSeatz`

5. **Support endpoints** - Customer service functionality
   - `POST /enquiries`
   - `POST /callback-requests`

### Lower Priority (Extended Features)
6. **Airbnb integration** - Third-party integration
   - `POST /airbnb/search`

7. **Specific content endpoints** - CMS functionality
   - `GET /content/{type}`
   - `GET /campaigns/{slug}`

## Test Coverage Quality Assessment

### Strengths
- ✅ Comprehensive error handling scenarios (400, 401, 404, 500 status codes)
- ✅ Authentication and authorization testing
- ✅ Schema validation for responses
- ✅ Both success and failure scenarios covered
- ✅ Proper use of Pact matchers for flexible matching

### Areas for Improvement
- ❌ Missing voucher validation (critical payment flow)
- ❌ No deals/promotions endpoint coverage
- ❌ Limited exclusive offers calendar testing
- ❌ No support/enquiry endpoint coverage

## Recommendations

1. **Add voucher contract tests** (High Priority)
2. **Add deals endpoint contract tests** (High Priority)
3. **Add calendar availability contract tests** (Medium Priority)
4. **Add support endpoint contract tests** (Medium Priority)
5. **Consider adding Airbnb integration tests** (Lower Priority)

## Next Steps

1. Create contract tests for high-priority missing endpoints
2. Review and validate the identified missing endpoints with the development team
3. Ensure provider verification is set up for new endpoints
4. Update endpoint coverage documentation
