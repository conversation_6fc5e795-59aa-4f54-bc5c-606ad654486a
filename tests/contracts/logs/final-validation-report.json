{"timestamp": "2025-09-04T13:22:00.117Z", "validationDuration": 96692, "summary": {"status": "PASSED", "totalTests": 111, "totalTestSuites": 14, "executionTime": 23968, "reliability": 100, "performanceImprovement": 96, "contractsGenerated": 5, "endpointsCovered": 31}, "details": {"testExecution": {"status": "passed", "executionTime": 23968, "totalTests": 111, "passedTests": 111, "failedTests": 0, "testSuites": {"passed": 14, "total": 14}}, "coverageAnalysis": {"contractTestEndpoints": 31, "contractTestCoverage": ["POST /bookings", "GET /bookings/{id}", "GET /properties", "GET /properties/{id}", "GET /properties/{id}/availability", "GET /exclusive-offers", "GET /properties/{id}/related", "GET /locations/{location}/availability", "POST /quotes", "GET /member-details/{memberId}", "GET /member-favourites", "POST /member-favourites", "DELETE /member-favourites/{favouriteId}", "PUT /member-details/{memberId}", "GET /validate", "POST /authenticate", "POST /qepg/get-payment-methods", "POST /payments", "GET /payment-status/{pspReference}", "GET /points-burn-v2", "GET /points-burn-luxe", "POST /points/calculate-burn", "POST /points/calculate-earn", "POST /points/process-combination-payment", "GET /content", "GET /content/preview", "GET /campaigns", "GET /site-message", "GET /locations", "GET /geolocation", "GET /locations/{id}/boundaries"], "e2eTestCoverage": {"testFiles": ["checkoutBookings.spec.js", "propertyInteractions.spec.js", "searchListInteractions.spec.js", "campaigns.spec.js"], "apiInteractions": ["POST /bookings", "GET /properties", "GET /locations/{location}/availability", "POST /authenticate", "GET /campaigns", "POST /quotes"]}, "coverageComparison": {"contractOnly": ["GET /bookings/{id}", "GET /properties/{id}", "GET /properties/{id}/availability", "GET /exclusive-offers", "GET /properties/{id}/related", "GET /member-details/{memberId}", "GET /member-favourites", "POST /member-favourites", "DELETE /member-favourites/{favouriteId}", "PUT /member-details/{memberId}", "GET /validate", "POST /qepg/get-payment-methods", "POST /payments", "GET /payment-status/{pspReference}", "GET /points-burn-v2", "GET /points-burn-luxe", "POST /points/calculate-burn", "POST /points/calculate-earn", "POST /points/process-combination-payment", "GET /content", "GET /content/preview", "GET /site-message", "GET /locations", "GET /geolocation", "GET /locations/{id}/boundaries"], "e2eOnly": [], "commonCoverage": ["POST /bookings", "GET /properties", "GET /locations/{location}/availability", "POST /quotes", "POST /authenticate", "GET /campaigns"]}}, "performanceMetrics": {"contractTestTime": 23968, "estimatedE2ETime": 600000, "performanceImprovement": 96, "targetTime": 300000, "meetsTarget": true, "resourceUsage": {"memory": "Low - Mock services only", "cpu": "Minimal - No browser rendering", "network": "None - Local mock servers"}}, "reliabilityMetrics": {"totalRuns": 3, "passedRuns": 3, "failedRuns": 0, "reliability": 100, "results": [{"run": 1, "status": "passed", "time": 25300.319542}, {"run": 2, "status": "passed", "time": 23671.982458}, {"run": 3, "status": "passed", "time": 23741.272375}]}, "contractVerification": {"contractsGenerated": 5, "contractFiles": ["qantas-hotels-ui-adyen-payment-service.json", "qantas-hotels-ui-geolocation-services.json", "qantas-hotels-ui-hotels-api.json", "qantas-hotels-ui-sanity-cms.json", "qantas-hotels-ui-unknown-provider.json"], "verificationStatus": "ready", "providerServices": [{"name": "Hotels API", "status": "compatible", "version": "1.0.0"}, {"name": "Auth API", "status": "compatible", "version": "1.0.0"}, {"name": "Payment Services", "status": "compatible", "version": "1.0.0"}, {"name": "Sanity CMS", "status": "compatible", "version": "1.0.0"}, {"name": "Geolocation Services", "status": "compatible", "version": "1.0.0"}]}}}