{"timestamp": "2025-09-04T13:16:31.652Z", "validationDuration": 119950, "summary": {"status": "PASSED", "totalTests": 0, "executionTime": 29315, "reliability": 100, "performanceImprovement": 95, "contractsGenerated": 5}, "details": {"testExecution": {"status": "passed", "executionTime": 29315, "totalTests": 0, "passedTests": 0, "failedTests": 0, "testSuites": {"passed": 0, "total": 0}, "coverage": "Available in coverage reports"}, "coverageAnalysis": {"contractTestEndpoints": 31, "contractTestCoverage": ["POST /bookings", "GET /bookings/{id}", "GET /properties", "GET /properties/{id}", "GET /properties/{id}/availability", "GET /exclusive-offers", "GET /locations/{location}/availability", "GET /properties/{id}/related", "POST /quotes", "GET /member-details/{memberId}", "GET /member-favourites", "POST /member-favourites", "DELETE /member-favourites/{favouriteId}", "PUT /member-details/{memberId}", "GET /validate", "POST /authenticate", "POST /qepg/get-payment-methods", "POST /payments", "GET /payment-status/{pspReference}", "GET /points-burn-v2", "GET /points-burn-luxe", "POST /points/calculate-burn", "POST /points/calculate-earn", "POST /points/process-combination-payment", "GET /content", "GET /content/preview", "GET /campaigns", "GET /site-message", "GET /locations", "GET /geolocation", "GET /locations/{id}/boundaries"], "e2eTestCoverage": {"testFiles": ["checkoutBookings.spec.js", "propertyInteractions.spec.js", "searchListInteractions.spec.js", "campaigns.spec.js"], "apiInteractions": ["POST /bookings", "GET /properties", "GET /locations/{location}/availability", "POST /authenticate", "GET /campaigns", "POST /quotes"], "uiInteractions": ["Search form interactions", "Property card clicks", "Booking form submission", "Payment form interactions"]}, "coverageComparison": {"contractOnly": ["GET /bookings/{id}", "GET /properties/{id}", "GET /properties/{id}/availability", "GET /exclusive-offers", "GET /properties/{id}/related", "GET /member-details/{memberId}", "GET /member-favourites", "POST /member-favourites", "DELETE /member-favourites/{favouriteId}", "PUT /member-details/{memberId}", "GET /validate", "POST /qepg/get-payment-methods", "POST /payments", "GET /payment-status/{pspReference}", "GET /points-burn-v2", "GET /points-burn-luxe", "POST /points/calculate-burn", "POST /points/calculate-earn", "POST /points/process-combination-payment", "GET /content", "GET /content/preview", "GET /site-message", "GET /locations", "GET /geolocation", "GET /locations/{id}/boundaries"], "e2eOnly": [], "commonCoverage": ["POST /bookings", "GET /properties", "GET /locations/{location}/availability", "POST /quotes", "POST /authenticate", "GET /campaigns"]}}, "performanceMetrics": {"contractTestTime": 29315, "estimatedE2ETime": 600000, "performanceImprovement": 95, "targetTime": 300000, "meetsTarget": true, "resourceUsage": {"memory": "Low - Mock services only", "cpu": "Minimal - No browser rendering", "network": "None - Local mock servers"}}, "reliabilityMetrics": {"totalRuns": 3, "passedRuns": 3, "failedRuns": 0, "reliability": 100, "averageExecutionTime": 30208, "consistencyVariance": 25722236.260786798, "results": [{"run": 1, "status": "passed", "time": 25378.217082999996}, {"run": 2, "status": "passed", "time": 37215.382874999996}, {"run": 3, "status": "passed", "time": 28031.651792000004}]}, "contractVerification": {"contractsGenerated": 5, "contractFiles": ["qantas-hotels-ui-adyen-payment-service.json", "qantas-hotels-ui-geolocation-services.json", "qantas-hotels-ui-hotels-api.json", "qantas-hotels-ui-sanity-cms.json", "qantas-hotels-ui-unknown-provider.json"], "verificationStatus": "ready", "providerServices": [{"name": "Hotels API", "status": "compatible", "version": "1.0.0"}, {"name": "Auth API", "status": "compatible", "version": "1.0.0"}, {"name": "Payment Services", "status": "compatible", "version": "1.0.0"}, {"name": "Sanity CMS", "status": "compatible", "version": "1.0.0"}, {"name": "Geolocation Services", "status": "compatible", "version": "1.0.0"}]}}}