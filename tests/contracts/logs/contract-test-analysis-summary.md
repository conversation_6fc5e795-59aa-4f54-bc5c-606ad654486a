# Contract Test Analysis Report

## Overall Health: 🟡 70/100

**Generated:** 9/4/2025, 11:27:13 PM  
**Analysis Period:** 30 days  
**Data Points:** 96 test runs  

## Key Metrics

| Metric | Value | Threshold | Status |
|--------|-------|-----------|--------|
| Success Rate | 0% | 95% | ❌ |
| Avg Execution Time | 27s | 300s | ✅ |
| Flakiness Rate | 2.65% | 5% | ✅ |
| Endpoint Coverage | 89% | 90% | ❌ |

## Reliability Analysis

- **Total Runs:** 96
- **Successful Runs:** 0
- **Success Rate:** 0%

### By Environment
- **CI:** 0% (55 runs)
- **Local:** 0% (41 runs)

## Performance Analysis

- **Average Execution Time:** 27s
- **Performance Trend:** 4.38% 📉 Degrading
- **Outliers:** 0 slow executions detected

## Coverage Analysis

- **Endpoint Coverage:** 89% (31/35)
- **Scenario Coverage:** 90% (135/150)
- **Error Case Coverage:** 93% (42/45)

## Flaky Tests

- **Hotels API - Property availability search with filters** (3.2% flaky, 4 failures)
- **Payment Services - Points calculation with promotional campaign** (2.1% flaky, 2 failures)

## Recommendations

### 1. Reliability - High Priority
**Issue:** Success rate (0%) below threshold (95%)  
**Recommendation:** Investigate and fix failing tests to improve overall reliability  
**Actions:**
- Review recent test failures
- Improve test stability
- Add better error handling

### 2. Coverage - Medium Priority
**Issue:** Endpoint coverage (89%) could be improved  
**Recommendation:** Add contract tests for uncovered API endpoints  
**Actions:**
- Identify uncovered endpoints
- Prioritize critical endpoints
- Create contract tests for new endpoints


---
*Generated by Contract Test Result Analyzer*