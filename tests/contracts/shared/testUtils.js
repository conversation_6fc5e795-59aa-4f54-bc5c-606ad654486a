/**
 * Shared test utilities for contract tests
 * Provides common helper functions used across different contract test suites
 */

const axios = require('axios');
const { TIMEOUTS } = require('./constants');

/**
 * HTTP client utilities for contract tests
 */
const httpUtils = {
  /**
   * Create an axios instance configured for contract testing
   * @param {string} baseURL - Base URL for the API
   * @param {Object} defaultHeaders - Default headers to include
   * @returns {Object} Configured axios instance
   */
  createClient(baseURL, defaultHeaders = {}) {
    return axios.create({
      baseURL,
      timeout: TIMEOUTS.DEFAULT,
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        ...defaultHeaders,
      },
    });
  },

  /**
   * Make HTTP request with error handling
   * @param {Object} client - Axios client instance
   * @param {Object} config - Request configuration
   * @returns {Promise<Object>} Response data and metadata
   */
  async makeRequest(client, config) {
    try {
      const response = await client.request(config);
      return {
        success: true,
        status: response.status,
        headers: response.headers,
        data: response.data,
        error: null,
      };
    } catch (error) {
      return {
        success: false,
        status: error.response?.status || 0,
        headers: error.response?.headers || {},
        data: error.response?.data || null,
        error: {
          message: error.message,
          code: error.code,
          response: error.response?.data,
        },
      };
    }
  },
};

/**
 * Date and time utilities
 */
const dateUtils = {
  /**
   * Get current date in ISO format (YYYY-MM-DD)
   * @returns {string} Current date in ISO format
   */
  getCurrentDate() {
    return new Date().toISOString().split('T')[0];
  },

  /**
   * Get future date in ISO format
   * @param {number} daysFromNow - Number of days from current date
   * @returns {string} Future date in ISO format
   */
  getFutureDate(daysFromNow = 1) {
    const date = new Date();
    date.setDate(date.getDate() + daysFromNow);
    return date.toISOString().split('T')[0];
  },

  /**
   * Get past date in ISO format
   * @param {number} daysAgo - Number of days before current date
   * @returns {string} Past date in ISO format
   */
  getPastDate(daysAgo = 1) {
    const date = new Date();
    date.setDate(date.getDate() - daysAgo);
    return date.toISOString().split('T')[0];
  },

  /**
   * Get current datetime in ISO format
   * @returns {string} Current datetime in ISO format
   */
  getCurrentDateTime() {
    return new Date().toISOString();
  },

  /**
   * Validate ISO date format
   * @param {string} dateString - Date string to validate
   * @returns {boolean} True if valid ISO date
   */
  isValidISODate(dateString) {
    const regex = /^\d{4}-\d{2}-\d{2}$/;
    return regex.test(dateString) && !isNaN(Date.parse(dateString));
  },

  /**
   * Validate ISO datetime format
   * @param {string} datetimeString - Datetime string to validate
   * @returns {boolean} True if valid ISO datetime
   */
  isValidISODateTime(datetimeString) {
    const regex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/;
    return regex.test(datetimeString) && !isNaN(Date.parse(datetimeString));
  },
};

/**
 * Data generation utilities
 */
const dataUtils = {
  /**
   * Generate random string of specified length
   * @param {number} length - Length of string to generate
   * @returns {string} Random string
   */
  randomString(length = 10) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },

  /**
   * Generate random number within range
   * @param {number} min - Minimum value (inclusive)
   * @param {number} max - Maximum value (inclusive)
   * @returns {number} Random number
   */
  randomNumber(min = 1, max = 100) {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  },

  /**
   * Generate random email address
   * @returns {string} Random email address
   */
  randomEmail() {
    const username = this.randomString(8).toLowerCase();
    const domain = this.randomString(6).toLowerCase();
    return `${username}@${domain}.com`;
  },

  /**
   * Generate random UUID v4
   * @returns {string} Random UUID
   */
  randomUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
      const r = (Math.random() * 16) | 0;
      const v = c === 'x' ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  },

  /**
   * Generate random price amount
   * @param {number} min - Minimum price
   * @param {number} max - Maximum price
   * @returns {number} Random price with 2 decimal places
   */
  randomPrice(min = 50, max = 1000) {
    return Math.round((Math.random() * (max - min) + min) * 100) / 100;
  },
};

/**
 * Assertion utilities for contract tests
 */
const assertionUtils = {
  /**
   * Assert HTTP status code
   * @param {number} actual - Actual status code
   * @param {number} expected - Expected status code
   */
  assertStatus(actual, expected) {
    expect(actual).toBe(expected);
  },

  /**
   * Assert response has required headers
   * @param {Object} headers - Response headers
   * @param {Array<string>} requiredHeaders - List of required header names
   */
  assertHeaders(headers, requiredHeaders) {
    requiredHeaders.forEach((headerName) => {
      expect(headers).toHaveProperty(headerName.toLowerCase());
    });
  },

  /**
   * Assert response body is valid JSON
   * @param {any} body - Response body
   */
  assertValidJSON(body) {
    expect(body).toBeDefined();
    expect(typeof body === 'object' || Array.isArray(body)).toBe(true);
  },

  /**
   * Assert response contains required fields
   * @param {Object} response - Response object
   * @param {Array<string>} requiredFields - List of required field paths
   */
  assertRequiredFields(response, requiredFields) {
    requiredFields.forEach((fieldPath) => {
      const fieldValue = this.getNestedProperty(response, fieldPath);
      expect(fieldValue).toBeDefined();
    });
  },

  /**
   * Get nested property from object using dot notation
   * @param {Object} obj - Object to search
   * @param {string} path - Dot-separated path (e.g., 'user.profile.name')
   * @returns {any} Property value or undefined
   */
  getNestedProperty(obj, path) {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  },

  /**
   * Assert error response format
   * @param {Object} errorResponse - Error response object
   */
  assertErrorFormat(errorResponse) {
    expect(errorResponse).toHaveProperty('error');
    expect(errorResponse.error).toHaveProperty('code');
    expect(errorResponse.error).toHaveProperty('message');
    expect(errorResponse).toHaveProperty('timestamp');
    expect(errorResponse).toHaveProperty('requestId');
  },
};

/**
 * Test environment utilities
 */
const envUtils = {
  /**
   * Check if running in CI environment
   * @returns {boolean} True if running in CI
   */
  isCI() {
    return process.env.CI === 'true' || process.env.BUILDKITE === 'true';
  },

  /**
   * Get test environment name
   * @returns {string} Environment name (test, development, etc.)
   */
  getEnvironment() {
    return process.env.NODE_ENV || 'test';
  },

  /**
   * Get API base URL for environment
   * @param {string} apiName - Name of API (hotels-api, auth-api, etc.)
   * @returns {string} Base URL for the API
   */
  getAPIBaseURL(apiName) {
    const envVarMap = {
      'hotels-api': process.env.NEXT_PUBLIC_HOTELS_API_HOST,
      'auth-api': process.env.AUTH_API_URL,
      'sanity-cms': process.env.SANITY_PROJECT_URL,
      'payment-services': process.env.PAYMENT_API_URL,
    };

    return envVarMap[apiName] || `https://api.${apiName}.qantas.com`;
  },
};

/**
 * Wait utilities for async operations
 */
const waitUtils = {
  /**
   * Wait for specified duration
   * @param {number} ms - Milliseconds to wait
   * @returns {Promise} Promise that resolves after delay
   */
  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  },

  /**
   * Wait for condition to be true with timeout
   * @param {Function} condition - Function that returns boolean
   * @param {number} timeout - Timeout in milliseconds
   * @param {number} interval - Check interval in milliseconds
   * @returns {Promise<boolean>} True if condition met, false if timeout
   */
  async waitForCondition(condition, timeout = TIMEOUTS.DEFAULT, interval = 100) {
    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      if (await condition()) {
        return true;
      }
      await this.delay(interval);
    }

    return false;
  },
};

module.exports = {
  httpUtils,
  dateUtils,
  dataUtils,
  assertionUtils,
  envUtils,
  waitUtils,
};
