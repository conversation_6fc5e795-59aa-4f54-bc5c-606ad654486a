/**
 * Shared constants for contract tests
 * Contains common values used across different contract test suites
 */

/**
 * API endpoint paths
 */
const API_PATHS = {
  // Hotels API paths
  HOTELS: {
    BOOKINGS: '/bookings',
    BOOKING_BY_ID: '/bookings/{id}',
    PROPERTIES: '/properties',
    PROPERTY_BY_ID: '/properties/{id}',
    PROPERTY_AVAILABILITY: '/properties/{id}/availability',
    PROPERTY_EXCLUSIVE_OFFERS: '/properties/{id}/exclusive-offers',
    LOCATION_AVAILABILITY: '/locations/{location}/availability',
    QUOTES: '/quotes',
    MEMBER_DETAILS: '/member-details/{memberId}',
    MEMBER_FAVOURITES: '/member-favourites',
  },

  // Auth API paths
  AUTH: {
    VALIDATE: '/validate',
    AUTHENTICATE: '/authenticate',
  },

  // Sanity CMS paths
  SANITY: {
    CONTENT: '/content/{type}',
    CONTENT_PREVIEW: '/content/preview',
    CAMPAIGNS: '/campaigns/{slug}',
    CAMPAIGNS_LIST: '/campaigns',
    FAQS: '/faqs',
    SITE_MESSAGE: '/site-message',
  },

  // Geolocation service paths
  GEOLOCATION: {
    LOCATIONS: '/locations',
    LOCATION_BOUNDARIES: '/locations/{id}/boundaries',
    GEOLOCATION: '/geolocation',
  },

  // Payment service paths
  PAYMENTS: {
    METHODS: '/payment-methods',
    PROCESS: '/payments',
  },
};

/**
 * HTTP status codes
 */
const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
};

/**
 * Common HTTP headers
 */
const HTTP_HEADERS = {
  CONTENT_TYPE: 'Content-Type',
  AUTHORIZATION: 'Authorization',
  ACCEPT: 'Accept',
  X_REQUESTED_WITH: 'X-Requested-With',
  X_MEMBER_ID: 'X-Member-Id',
  X_QH_USER_ID: 'X-QH-User-Id',
};

/**
 * Content types
 */
const CONTENT_TYPES = {
  JSON: 'application/json',
  FORM_URLENCODED: 'application/x-www-form-urlencoded',
  TEXT_PLAIN: 'text/plain',
};

/**
 * Test data constants
 */
const TEST_DATA = {
  // Sample property IDs
  PROPERTY_IDS: {
    VALID: 'property-123',
    INVALID: 'property-invalid',
    NOT_FOUND: 'property-404',
  },

  // Sample booking IDs
  BOOKING_IDS: {
    VALID: 'booking-456',
    INVALID: 'booking-invalid',
    NOT_FOUND: 'booking-404',
  },

  // Sample member IDs
  MEMBER_IDS: {
    VALID: '12345678',
    INVALID: 'invalid-member',
    NOT_FOUND: '99999999',
  },

  // Sample locations
  LOCATIONS: {
    SYDNEY: 'Sydney',
    MELBOURNE: 'Melbourne',
    INVALID: 'InvalidLocation',
  },

  // Sample dates
  DATES: {
    CHECK_IN: '2024-12-01',
    CHECK_OUT: '2024-12-05',
    PAST_DATE: '2023-01-01',
    INVALID_DATE: 'invalid-date',
  },

  // Sample guest configurations
  GUESTS: {
    SINGLE: { adults: 1, children: 0, infants: 0 },
    COUPLE: { adults: 2, children: 0, infants: 0 },
    FAMILY: { adults: 2, children: 2, infants: 1 },
  },

  // Sample currencies
  CURRENCIES: {
    AUD: 'AUD',
    USD: 'USD',
    EUR: 'EUR',
  },
};

/**
 * Provider states for different test scenarios
 */
const PROVIDER_STATES = {
  // Authentication states
  AUTH: {
    USER_AUTHENTICATED: 'user is authenticated as QFF member',
    USER_GUEST: 'user is not authenticated',
    TOKEN_EXPIRED: 'user has expired authentication token',
    TOKEN_INVALID: 'user has invalid authentication token',
  },

  // Property states
  PROPERTIES: {
    PROPERTY_EXISTS: 'property exists and is available',
    PROPERTY_NOT_FOUND: 'property does not exist',
    PROPERTY_UNAVAILABLE: 'property exists but is not available',
    PROPERTIES_AVAILABLE: 'properties are available for search',
  },

  // Booking states
  BOOKINGS: {
    BOOKING_EXISTS: 'booking exists for user',
    BOOKING_NOT_FOUND: 'booking does not exist',
    BOOKING_CAN_BE_CREATED: 'booking can be created for property',
    BOOKING_CANNOT_BE_CREATED: 'booking cannot be created for property',
  },

  // Payment states
  PAYMENTS: {
    PAYMENT_METHODS_AVAILABLE: 'payment methods are available',
    PAYMENT_CAN_BE_PROCESSED: 'payment can be processed',
    PAYMENT_WILL_FAIL: 'payment will fail',
  },

  // Content states
  CONTENT: {
    CONTENT_EXISTS: 'content exists',
    CONTENT_NOT_FOUND: 'content does not exist',
    CAMPAIGNS_ACTIVE: 'active campaigns exist',
    FAQS_AVAILABLE: 'FAQs are available',
  },

  // Geolocation states
  GEOLOCATION: {
    LOCATIONS_AVAILABLE: 'locations are available for search',
    LOCATION_NOT_FOUND: 'location does not exist',
    GEOLOCATION_DETECTABLE: 'user geolocation can be detected',
    GEOLOCATION_UNAVAILABLE: 'user geolocation cannot be detected',
    BOUNDARIES_AVAILABLE: 'location boundaries are available',
  },

  // Campaign states
  CAMPAIGNS_ACTIVE: 'active campaigns exist',
};

/**
 * Test timeouts (in milliseconds)
 */
const TIMEOUTS = {
  DEFAULT: 10000,
  LONG: 30000,
  SHORT: 5000,
};

/**
 * Regular expressions for validation
 */
const REGEX_PATTERNS = {
  UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
  ISO_DATE: /^\d{4}-\d{2}-\d{2}$/,
  ISO_DATETIME: /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/,
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^\+?[\d\s\-()]+$/,
};

module.exports = {
  API_PATHS,
  HTTP_STATUS,
  HTTP_HEADERS,
  CONTENT_TYPES,
  TEST_DATA,
  PROVIDER_STATES,
  TIMEOUTS,
  REGEX_PATTERNS,
};
