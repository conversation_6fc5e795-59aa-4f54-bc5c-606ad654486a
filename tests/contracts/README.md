# Contract Tests

This directory contains contract tests for the Qantas Hotels UI application using Pact.js framework.

## Overview

Contract tests verify the interface agreements between the front-end application and backend API services without the overhead of full end-to-end testing. They focus on API request/response validation rather than UI interactions.

## Directory Structure

```
tests/contracts/
├── helpers/                    # Contract test utilities and helpers
│   ├── ContractTestBase.js    # Base class for contract tests
│   ├── mockDataGenerators.js  # Mock data generators for API requests/responses
│   ├── schemaValidators.js    # Schema validation using Zod
│   └── authTokenMocks.js      # Authentication token mock utilities
├── shared/                     # Shared utilities and constants
│   ├── constants.js           # Common constants and test data
│   └── testUtils.js           # Utility functions for testing
├── hotels-api/                # Contract tests for Hotels API
├── auth-api/                  # Contract tests for Authentication API
├── sanity-cms/                # Contract tests for Sanity CMS
├── payment-services/          # Contract tests for Payment Services
├── setup/                     # Test setup and configuration
│   └── contractTestSetup.js   # Jest setup for contract tests
├── pacts/                     # Generated Pact contract files
├── logs/                      # Pact log files
├── pact.config.js            # Pact configuration
└── index.js                  # Main exports for contract test utilities
```

## Getting Started

### Prerequisites

- Node.js 22+ (see `.nvmrc`)
- Yarn package manager
- Jest testing framework (already configured)

### Installation

The Pact.js dependency is already installed. To run contract tests:

```bash
# Run all contract tests
yarn test:contracts

# Run contract tests in watch mode
yarn test:contracts:watch
```

### Writing Contract Tests

1. **Create a new contract test file** in the appropriate API directory:

   ```javascript
   // tests/contracts/hotels-api/bookings.contract.test.js
   const { ContractTestBase, bookingMockData, authHeaders } = require('../index');

   describe('Hotels API - Bookings Contract', () => {
     let contractTest;

     beforeAll(async () => {
       contractTest = new ContractTestBase('hotels-api');
       await contractTest.setup();
     });

     afterAll(async () => {
       await contractTest.teardown();
     });

     afterEach(async () => {
       await contractTest.verify();
     });

     test('should create booking successfully', async () => {
       // Define interaction
       await contractTest.addInteraction({
         state: 'booking can be created for property',
         uponReceiving: 'a request to create a booking',
         withRequest: {
           method: 'POST',
           path: '/bookings',
           headers: authHeaders.authenticatedHeaders(),
           body: bookingMockData.bookingRequest(),
         },
         willRespondWith: {
           status: 201,
           headers: { 'Content-Type': 'application/json' },
           body: bookingMockData.bookingResponse(),
         },
       });

       // Make actual API call to mock server
       const response = await makeAPICall();

       // Assertions
       expect(response.status).toBe(201);
       expect(response.data).toMatchSchema(bookingSchemas.bookingResponse);
     });
   });
   ```

2. **Use mock data generators** for consistent test data:

   ```javascript
   const { bookingMockData, propertyMockData } = require('../index');

   // Generate mock booking request
   const bookingRequest = bookingMockData.bookingRequest();

   // Generate mock property search response
   const searchResponse = propertyMockData.propertySearchResponse();
   ```

3. **Validate responses with schemas**:

   ```javascript
   const { bookingSchemas, validators } = require('../index');

   // Validate response matches schema
   expect(response.data).toMatchSchema(bookingSchemas.bookingResponse);

   // Or use validator directly
   const result = validators.validate(response.data, bookingSchemas.bookingResponse);
   expect(result.success).toBe(true);
   ```

4. **Use authentication helpers**:

   ```javascript
   const { authHeaders, authHelpers } = require('../index');

   // Get headers for authenticated member
   const headers = authHelpers.getHeadersForState('authenticatedMember');

   // Get headers for guest user
   const guestHeaders = authHeaders.guestHeaders();
   ```

## API Coverage

The contract tests cover the following APIs:

### Hotels API

- **Bookings**: Create, retrieve, and manage hotel bookings
- **Properties**: Search and retrieve property details
- **Quotes**: Generate pricing quotes for bookings
- **Availability**: Check property availability
- **Member Services**: Member details and favourites

### Authentication API

- **Token Validation**: Validate authentication tokens
- **Member Authentication**: QFF member login and authentication

### Sanity CMS

- **Content Retrieval**: Fetch content and campaigns
- **FAQs**: Retrieve frequently asked questions

### Payment Services

- **Payment Methods**: Retrieve available payment methods
- **Payment Processing**: Process payments via Adyen

## Configuration

### Environment Variables

Contract tests use the following environment variables:

- `NEXT_PUBLIC_HOTELS_API_HOST`: Hotels API base URL
- `AUTH_API_URL`: Authentication API base URL
- `SANITY_PROJECT_URL`: Sanity CMS API base URL
- `PAYMENT_API_URL`: Payment services API base URL

### Pact Configuration

The Pact configuration is defined in `pact.config.js`:

```javascript
const PACT_CONFIG = {
  consumer: 'qantas-hotels-ui',
  dir: './tests/contracts/pacts',
  logLevel: 'ERROR',
  spec: 2,
  cors: true,
  providers: {
    'hotels-api': { name: 'hotels-api', baseUrl: '...' },
    // ... other providers
  },
};
```

## Best Practices

1. **Focus on Contracts**: Test API request/response contracts, not business logic
2. **Use Provider States**: Define clear provider states for different test scenarios
3. **Mock Realistic Data**: Use realistic mock data that matches production patterns
4. **Validate Schemas**: Always validate response schemas to catch contract changes
5. **Handle Errors**: Test both success and error scenarios
6. **Keep Tests Independent**: Each test should be independent and not rely on others
7. **Use Descriptive Names**: Use clear, descriptive names for interactions and tests

## Troubleshooting

### Common Issues

1. **Mock Server Port Conflicts**: Pact automatically assigns available ports
2. **Schema Validation Failures**: Check that mock data matches expected schemas
3. **Authentication Issues**: Ensure correct auth headers are used for protected endpoints
4. **Timeout Issues**: Increase timeout for slow operations in test configuration

### Debugging

- Check Pact log files in `tests/contracts/logs/`
- Use `logLevel: 'DEBUG'` in Pact configuration for verbose logging
- Verify generated contracts in `tests/contracts/pacts/`

## CI/CD Integration

Contract tests are integrated into the build pipeline:

```bash
# Run in CI
yarn test:contracts
```

The tests will:

1. Generate consumer contracts
2. Validate API interactions
3. Produce contract files for provider verification
4. Report test results and coverage

## Performance

Contract tests are designed to be fast and reliable:

- **Execution Time**: < 5 minutes for full suite
- **Parallel Execution**: Tests run in parallel where possible
- **Resource Usage**: Minimal compared to E2E tests
- **Reliability**: 99%+ reliability with minimal flakiness

## Further Reading

- [Pact.js Documentation](https://docs.pact.io/implementation_guides/javascript)
- [Contract Testing Best Practices](https://docs.pact.io/getting_started/what_is_pact)
- [Zod Schema Validation](https://zod.dev/)
