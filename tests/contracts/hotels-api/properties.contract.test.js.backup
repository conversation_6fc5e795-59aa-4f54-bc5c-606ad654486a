/**
 * Contract tests for Hotels API property endpoints
 * Tests the consumer contract for property search, details, and exclusive offers
 */

const { Pact } = require('@pact-foundation/pact');
const path = require('path');
const { searchProperties } = require('../../../src/lib/clients/searchProperties');
const { fetchPropertyDetails } = require('../../../src/lib/clients/fetchPropertyDetails');
const { searchPropertyAvailability } = require('../../../src/lib/clients/searchPropertyAvailability');
const { fetchExclusiveOfferList } = require('../../../src/lib/clients/fetchExclusiveOfferList');
const { API_PATHS, HTTP_STATUS, HTTP_HEADERS, CONTENT_TYPES, TEST_DATA, PROVIDER_STATES } = require('../shared/constants');
const { dateUtils, dataUtils } = require('../shared/testUtils');

describe('Hotels API Property Endpoints Contract Tests', () => {
  let provider;

  beforeAll(() => {
    provider = new Pact({
      consumer: 'qantas-hotels-ui',
      provider: 'hotels-api',
      port: 1235,
      log: path.resolve(process.cwd(), 'logs', 'pact.log'),
      dir: path.resolve(process.cwd(), 'pacts'),
      logLevel: 'INFO',
    });

    return provider.setup();
  });

  afterAll(() => {
    return provider.finalize();
  });

  afterEach(() => {
    return provider.verify();
  });

  describe('GET /properties - Search Properties', () => {
    describe('successful property search', () => {
      const expectedProperties = [
        {
          type: 'property',
          id: TEST_DATA.PROPERTY_IDS.VALID,
          name: 'Test Hotel Sydney',
          location: {
            city: 'Sydney',
            state: 'NSW',
            country: 'Australia',
            coordinates: {
              latitude: -33.8688,
              longitude: 151.2093,
            },
          },
          rating: {
            type: 'star',
            value: 4,
          },
          pricing: {
            currency: TEST_DATA.CURRENCIES.AUD,
            baseRate: 200.0,
            totalRate: 220.0,
          },
          amenities: ['wifi', 'pool', 'gym'],
          images: [
            {
              url: 'https://example.com/hotel1.jpg',
              alt: 'Hotel exterior',
            },
          ],
        },
        {
          type: 'property',
          id: 'property-456',
          name: 'Luxury Resort Sydney',
          location: {
            city: 'Sydney',
            state: 'NSW',
            country: 'Australia',
            coordinates: {
              latitude: -33.865,
              longitude: 151.2094,
            },
          },
          rating: {
            type: 'star',
            value: 5,
          },
          pricing: {
            currency: TEST_DATA.CURRENCIES.AUD,
            baseRate: 450.0,
            totalRate: 495.0,
          },
          amenities: ['wifi', 'pool', 'gym', 'spa', 'restaurant'],
          images: [
            {
              url: 'https://example.com/resort1.jpg',
              alt: 'Resort view',
            },
          ],
        },
      ];

      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.PROPERTIES.PROPERTIES_AVAILABLE,
          uponReceiving: 'a request to search properties by name',
          withRequest: {
            method: 'GET',
            path: API_PATHS.HOTELS.PROPERTIES,
            query: {
              name: 'Sydney',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedProperties.map(({ type: _type, ...property }) => property),
          },
        });
      });

      it('should search properties successfully', async () => {
        const result = await searchProperties({ name: 'Sydney' });

        expect(result).toHaveLength(2);
        expect(result[0]).toHaveProperty('type', 'property');
        expect(result[0]).toHaveProperty('id');
        expect(result[0]).toHaveProperty('name');
        expect(result[0]).toHaveProperty('location');
        expect(result[0]).toHaveProperty('rating');
        expect(result[0]).toHaveProperty('pricing');
        expect(result[0].location).toHaveProperty('city');
        expect(result[0].location).toHaveProperty('coordinates');
        expect(result[0].rating).toHaveProperty('type');
        expect(result[0].rating).toHaveProperty('value');
        expect(result[0].pricing).toHaveProperty('currency');
        expect(result[0].pricing).toHaveProperty('baseRate');
      });
    });

    describe('empty search results', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.PROPERTIES.PROPERTIES_AVAILABLE,
          uponReceiving: 'a request to search properties with no results',
          withRequest: {
            method: 'GET',
            path: API_PATHS.HOTELS.PROPERTIES,
            query: {
              name: 'NonExistentLocation',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: [],
          },
        });
      });

      it('should return empty array for no results', async () => {
        const result = await searchProperties({ name: 'NonExistentLocation' });

        expect(result).toEqual([]);
      });
    });
  });

  describe('GET /properties/{id} - Fetch Property Details', () => {
    describe('successful property details retrieval', () => {
      const expectedPropertyDetails = {
        id: TEST_DATA.PROPERTY_IDS.VALID,
        name: 'Test Hotel Sydney',
        description: 'A beautiful hotel in the heart of Sydney',
        location: {
          address: '123 George Street, Sydney NSW 2000',
          city: 'Sydney',
          state: 'NSW',
          country: 'Australia',
          postcode: '2000',
          coordinates: {
            latitude: -33.8688,
            longitude: 151.2093,
          },
        },
        rating: {
          type: 'star',
          value: 4,
        },
        amenities: [
          {
            id: 'wifi',
            name: 'Free WiFi',
            category: 'connectivity',
          },
          {
            id: 'pool',
            name: 'Swimming Pool',
            category: 'recreation',
          },
        ],
        images: [
          {
            url: 'https://example.com/hotel1.jpg',
            alt: 'Hotel exterior',
            type: 'exterior',
          },
          {
            url: 'https://example.com/room1.jpg',
            alt: 'Standard room',
            type: 'room',
          },
        ],
        policies: {
          checkIn: '15:00',
          checkOut: '11:00',
          cancellation: 'Free cancellation up to 24 hours before check-in',
        },
        contact: {
          phone: '+61 2 1234 5678',
          email: '<EMAIL>',
        },
      };

      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.PROPERTIES.PROPERTY_EXISTS,
          uponReceiving: 'a request to fetch property details',
          withRequest: {
            method: 'GET',
            path: `/properties/${TEST_DATA.PROPERTY_IDS.VALID}`,
            headers: {
              [HTTP_HEADERS.X_QH_USER_ID]: 'user-123',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedPropertyDetails,
          },
        });
      });

      it('should fetch property details successfully', async () => {
        const result = await fetchPropertyDetails({
          propertyId: TEST_DATA.PROPERTY_IDS.VALID,
          qhUserId: 'user-123',
        });

        expect(result).toEqual(expectedPropertyDetails);
        expect(result.id).toBe(TEST_DATA.PROPERTY_IDS.VALID);
        expect(result.name).toBeDefined();
        expect(result.location).toHaveProperty('address');
        expect(result.location).toHaveProperty('coordinates');
        expect(result.rating).toHaveProperty('type');
        expect(result.rating).toHaveProperty('value');
        expect(result.amenities).toBeInstanceOf(Array);
        expect(result.images).toBeInstanceOf(Array);
        expect(result.policies).toBeDefined();
        expect(result.contact).toBeDefined();
      });
    });

    describe('property not found', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.PROPERTIES.PROPERTY_NOT_FOUND,
          uponReceiving: 'a request to fetch non-existent property details',
          withRequest: {
            method: 'GET',
            path: `/properties/${TEST_DATA.PROPERTY_IDS.NOT_FOUND}`,
            headers: {
              [HTTP_HEADERS.X_QH_USER_ID]: 'user-123',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.NOT_FOUND,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'PROPERTY_NOT_FOUND',
                message: 'Property not found',
                details: {
                  propertyId: TEST_DATA.PROPERTY_IDS.NOT_FOUND,
                },
              },
              timestamp: dateUtils.getCurrentDateTime(),
              requestId: dataUtils.randomUUID(),
            },
          },
        });
      });

      it('should return 404 for non-existent property', async () => {
        await expect(
          fetchPropertyDetails({
            propertyId: TEST_DATA.PROPERTY_IDS.NOT_FOUND,
            qhUserId: 'user-123',
          }),
        ).rejects.toThrow();
      });
    });
  });

  describe('GET /properties/{id}/availability - Search Property Availability', () => {
    describe('successful property availability search', () => {
      const expectedAvailability = {
        propertyId: TEST_DATA.PROPERTY_IDS.VALID,
        checkIn: TEST_DATA.DATES.CHECK_IN,
        checkOut: TEST_DATA.DATES.CHECK_OUT,
        available: true,
        rooms: [
          {
            id: 'room-type-123',
            name: 'Standard Room',
            description: 'Comfortable room with city view',
            capacity: {
              adults: 2,
              children: 1,
              infants: 1,
            },
            pricing: {
              currency: TEST_DATA.CURRENCIES.AUD,
              baseRate: 200.0,
              totalRate: 220.0,
              taxes: 20.0,
            },
            offers: [
              {
                id: 'offer-456',
                name: 'Early Bird Special',
                description: '10% off for bookings made 30 days in advance',
                discount: {
                  type: 'percentage',
                  value: 10,
                },
              },
            ],
            amenities: ['wifi', 'minibar', 'aircon'],
            images: [
              {
                url: 'https://example.com/room1.jpg',
                alt: 'Standard room',
              },
            ],
          },
        ],
      };

      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.PROPERTIES.PROPERTY_EXISTS,
          uponReceiving: 'a request to search property availability',
          withRequest: {
            method: 'GET',
            path: `/properties/${TEST_DATA.PROPERTY_IDS.VALID}/availability`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              [HTTP_HEADERS.X_QH_USER_ID]: 'user-123',
            },
            query: {
              checkIn: TEST_DATA.DATES.CHECK_IN,
              checkOut: TEST_DATA.DATES.CHECK_OUT,
              adults: '2',
              children: '0',
              infants: '0',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedAvailability,
          },
        });
      });

      it('should search property availability successfully', async () => {
        const result = await searchPropertyAvailability({
          propertyId: TEST_DATA.PROPERTY_IDS.VALID,
          checkIn: TEST_DATA.DATES.CHECK_IN,
          checkOut: TEST_DATA.DATES.CHECK_OUT,
          adults: 2,
          children: 0,
          infants: 0,
          accessToken: 'valid-token',
          qhUserId: 'user-123',
        });

        expect(result).toEqual(expectedAvailability);
        expect(result.propertyId).toBe(TEST_DATA.PROPERTY_IDS.VALID);
        expect(result.available).toBe(true);
        expect(result.rooms).toBeInstanceOf(Array);
        expect(result.rooms[0]).toHaveProperty('id');
        expect(result.rooms[0]).toHaveProperty('name');
        expect(result.rooms[0]).toHaveProperty('capacity');
        expect(result.rooms[0]).toHaveProperty('pricing');
        expect(result.rooms[0].pricing).toHaveProperty('currency');
        expect(result.rooms[0].pricing).toHaveProperty('baseRate');
        expect(result.rooms[0].pricing).toHaveProperty('totalRate');
      });
    });

    describe('property unavailable for dates', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.PROPERTIES.PROPERTY_UNAVAILABLE,
          uponReceiving: 'a request to search unavailable property',
          withRequest: {
            method: 'GET',
            path: `/properties/${TEST_DATA.PROPERTY_IDS.VALID}/availability`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              [HTTP_HEADERS.X_QH_USER_ID]: 'user-123',
            },
            query: {
              checkIn: TEST_DATA.DATES.CHECK_IN,
              checkOut: TEST_DATA.DATES.CHECK_OUT,
              adults: '2',
              children: '0',
              infants: '0',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              propertyId: TEST_DATA.PROPERTY_IDS.VALID,
              checkIn: TEST_DATA.DATES.CHECK_IN,
              checkOut: TEST_DATA.DATES.CHECK_OUT,
              available: false,
              rooms: [],
              message: 'No rooms available for the selected dates',
            },
          },
        });
      });

      it('should return unavailable status when no rooms available', async () => {
        const result = await searchPropertyAvailability({
          propertyId: TEST_DATA.PROPERTY_IDS.VALID,
          checkIn: TEST_DATA.DATES.CHECK_IN,
          checkOut: TEST_DATA.DATES.CHECK_OUT,
          adults: 2,
          children: 0,
          infants: 0,
          accessToken: 'valid-token',
          qhUserId: 'user-123',
        });

        expect(result.available).toBe(false);
        expect(result.rooms).toEqual([]);
        expect(result.message).toBeDefined();
      });
    });
  });

  describe('GET /exclusive-offers - Fetch Exclusive Offers', () => {
    describe('successful exclusive offers retrieval', () => {
      const expectedExclusiveOffers = [
        {
          id: 'exclusive-offer-123',
          name: 'VIP Weekend Package',
          description: 'Exclusive weekend package with premium amenities',
          propertyId: TEST_DATA.PROPERTY_IDS.VALID,
          propertyName: 'Test Hotel Sydney',
          validFrom: dateUtils.getCurrentDate(),
          validTo: dateUtils.getFutureDate(30),
          discount: {
            type: 'percentage',
            value: 25,
          },
          inclusions: ['Complimentary breakfast', 'Late checkout', 'Welcome drink'],
          terms: 'Valid for weekend stays only. Subject to availability.',
          pricing: {
            currency: TEST_DATA.CURRENCIES.AUD,
            originalRate: 400.0,
            discountedRate: 300.0,
            savings: 100.0,
          },
        },
        {
          id: 'exclusive-offer-456',
          name: 'Extended Stay Special',
          description: 'Special rates for stays of 7 nights or more',
          propertyId: 'property-456',
          propertyName: 'Luxury Resort Sydney',
          validFrom: dateUtils.getCurrentDate(),
          validTo: dateUtils.getFutureDate(60),
          discount: {
            type: 'fixed',
            value: 150,
          },
          inclusions: ['Free WiFi', 'Daily housekeeping', 'Access to fitness center'],
          terms: 'Minimum 7 night stay required. Non-refundable.',
          pricing: {
            currency: TEST_DATA.CURRENCIES.AUD,
            originalRate: 500.0,
            discountedRate: 350.0,
            savings: 150.0,
          },
        },
      ];

      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.CONTENT.CONTENT_EXISTS,
          uponReceiving: 'a request to fetch exclusive offers',
          withRequest: {
            method: 'GET',
            path: '/exclusive-offers/',
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedExclusiveOffers,
          },
        });
      });

      it('should fetch exclusive offers successfully', async () => {
        const result = await fetchExclusiveOfferList();

        expect(result).toEqual(expectedExclusiveOffers);
        expect(result).toHaveLength(2);
        expect(result[0]).toHaveProperty('id');
        expect(result[0]).toHaveProperty('name');
        expect(result[0]).toHaveProperty('description');
        expect(result[0]).toHaveProperty('propertyId');
        expect(result[0]).toHaveProperty('discount');
        expect(result[0]).toHaveProperty('inclusions');
        expect(result[0]).toHaveProperty('pricing');
        expect(result[0].discount).toHaveProperty('type');
        expect(result[0].discount).toHaveProperty('value');
        expect(result[0].pricing).toHaveProperty('currency');
        expect(result[0].pricing).toHaveProperty('originalRate');
        expect(result[0].pricing).toHaveProperty('discountedRate');
      });
    });

    describe('no exclusive offers available', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.CONTENT.CONTENT_EXISTS,
          uponReceiving: 'a request to fetch exclusive offers with no results',
          withRequest: {
            method: 'GET',
            path: '/exclusive-offers/',
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: [],
          },
        });
      });

      it('should return empty array when no exclusive offers available', async () => {
        const result = await fetchExclusiveOfferList();

        expect(result).toEqual([]);
      });
    });
  });
});
