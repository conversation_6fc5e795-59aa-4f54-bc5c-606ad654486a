/**
 * Contract tests for Hotels API quote generation endpoints
 * Tests the consumer contract for quote creation with different payment methods
 */

const { Pact } = require('@pact-foundation/pact');
const path = require('path');
const { createQuote } = require('../../../src/lib/clients/createQuote');
const { API_PATHS, HTTP_STATUS, HTTP_HEADERS, CONTENT_TYPES, TEST_DATA, PROVIDER_STATES } = require('../shared/constants');
const { dateUtils, dataUtils } = require('../shared/testUtils');

describe('Hotels API Quote Generation Contract Tests', () => {
  let provider;

  beforeAll(() => {
    provider = new Pact({
      consumer: 'qantas-hotels-ui',
      provider: 'hotels-api',
      port: 1236,
      log: path.resolve(process.cwd(), 'logs', 'pact.log'),
      dir: path.resolve(process.cwd(), 'pacts'),
      logLevel: 'INFO',
    });

    return provider.setup();
  });

  afterAll(() => {
    return provider.finalize();
  });

  afterEach(() => {
    return provider.verify();
  });

  describe('POST /quotes - Create Quote', () => {
    describe('successful quote creation with cash payment', () => {
      const quotePayload = {
        propertyId: TEST_DATA.PROPERTY_IDS.VALID,
        roomTypeId: 'room-type-123',
        offerId: 'offer-456',
        checkIn: TEST_DATA.DATES.CHECK_IN,
        checkOut: TEST_DATA.DATES.CHECK_OUT,
        adults: TEST_DATA.GUESTS.COUPLE.adults,
        children: TEST_DATA.GUESTS.COUPLE.children,
        infants: TEST_DATA.GUESTS.COUPLE.infants,
      };

      const expectedQuoteResponse = {
        quote: {
          id: 'quote-789',
          propertyId: TEST_DATA.PROPERTY_IDS.VALID,
          roomTypeId: 'room-type-123',
          offerId: 'offer-456',
          checkIn: TEST_DATA.DATES.CHECK_IN,
          checkOut: TEST_DATA.DATES.CHECK_OUT,
          guests: TEST_DATA.GUESTS.COUPLE,
          pricing: {
            currency: TEST_DATA.CURRENCIES.AUD,
            baseRate: 200.0,
            taxes: 20.0,
            fees: 10.0,
            totalRate: 230.0,
            breakdown: [
              {
                description: 'Room rate (4 nights)',
                amount: 800.0,
              },
              {
                description: 'Taxes and fees',
                amount: 120.0,
              },
            ],
          },
          paymentOptions: {
            cash: {
              available: true,
              amount: 920.0,
              currency: TEST_DATA.CURRENCIES.AUD,
            },
            points: {
              available: false,
              reason: 'Insufficient points balance',
            },
            pointsAndCash: {
              available: false,
              reason: 'Insufficient points balance',
            },
          },
          policies: {
            cancellation: 'Free cancellation up to 24 hours before check-in',
            modification: 'Modifications allowed up to 48 hours before check-in',
          },
          validUntil: dateUtils.getFutureDate(1),
          createdAt: dateUtils.getCurrentDateTime(),
        },
      };

      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.PROPERTIES.PROPERTY_EXISTS,
          uponReceiving: 'a request to create a quote with cash payment',
          withRequest: {
            method: 'POST',
            path: API_PATHS.HOTELS.QUOTES,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              [HTTP_HEADERS.X_QH_USER_ID]: 'user-123',
            },
            body: {
              propertyId: TEST_DATA.PROPERTY_IDS.VALID,
              roomTypeId: 'room-type-123',
              offerId: 'offer-456',
              checkIn: TEST_DATA.DATES.CHECK_IN,
              checkOut: TEST_DATA.DATES.CHECK_OUT,
              adults: TEST_DATA.GUESTS.COUPLE.adults,
              children: TEST_DATA.GUESTS.COUPLE.children,
              infants: TEST_DATA.GUESTS.COUPLE.infants,
              flightBookerToken: null,
              paidByDeposit: 'false',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.CREATED,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedQuoteResponse,
          },
        });
      });

      it('should create a quote with cash payment successfully', async () => {
        const result = await createQuote({
          payload: quotePayload,
          accessToken: 'valid-token',
          qhUserId: 'user-123',
        });

        expect(result).toEqual(expectedQuoteResponse);
        expect(result.quote.id).toBeDefined();
        expect(result.quote.propertyId).toBe(TEST_DATA.PROPERTY_IDS.VALID);
        expect(result.quote.pricing).toHaveProperty('currency');
        expect(result.quote.pricing).toHaveProperty('totalRate');
        expect(result.quote.pricing.totalRate).toBeGreaterThan(0);
        expect(result.quote.paymentOptions.cash.available).toBe(true);
        expect(result.quote.validUntil).toBeDefined();
        expect(result.quote.createdAt).toBeDefined();
      });
    });

    describe('successful quote creation with points and cash payment', () => {
      const quotePayload = {
        propertyId: TEST_DATA.PROPERTY_IDS.VALID,
        roomTypeId: 'room-type-123',
        offerId: 'offer-456',
        checkIn: TEST_DATA.DATES.CHECK_IN,
        checkOut: TEST_DATA.DATES.CHECK_OUT,
        adults: TEST_DATA.GUESTS.COUPLE.adults,
        children: TEST_DATA.GUESTS.COUPLE.children,
        infants: TEST_DATA.GUESTS.COUPLE.infants,
        flightBookerToken: 'flight-token-123',
      };

      const expectedQuoteResponse = {
        quote: {
          id: 'quote-points-789',
          propertyId: TEST_DATA.PROPERTY_IDS.VALID,
          roomTypeId: 'room-type-123',
          offerId: 'offer-456',
          checkIn: TEST_DATA.DATES.CHECK_IN,
          checkOut: TEST_DATA.DATES.CHECK_OUT,
          guests: TEST_DATA.GUESTS.COUPLE,
          pricing: {
            currency: TEST_DATA.CURRENCIES.AUD,
            baseRate: 200.0,
            taxes: 20.0,
            fees: 10.0,
            totalRate: 230.0,
            breakdown: [
              {
                description: 'Room rate (4 nights)',
                amount: 800.0,
              },
              {
                description: 'Taxes and fees',
                amount: 120.0,
              },
            ],
          },
          paymentOptions: {
            cash: {
              available: true,
              amount: 920.0,
              currency: TEST_DATA.CURRENCIES.AUD,
            },
            points: {
              available: true,
              amount: 92000,
              currency: 'QFF_POINTS',
              conversionRate: 0.01,
            },
            pointsAndCash: {
              available: true,
              pointsAmount: 46000,
              cashAmount: 460.0,
              currency: TEST_DATA.CURRENCIES.AUD,
              pointsCurrency: 'QFF_POINTS',
            },
          },
          pointsEarn: {
            basePoints: 920,
            bonusPoints: 184,
            totalPoints: 1104,
          },
          policies: {
            cancellation: 'Free cancellation up to 24 hours before check-in',
            modification: 'Modifications allowed up to 48 hours before check-in',
          },
          validUntil: dateUtils.getFutureDate(1),
          createdAt: dateUtils.getCurrentDateTime(),
        },
      };

      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.AUTH.USER_AUTHENTICATED,
          uponReceiving: 'a request to create a quote with points and cash payment',
          withRequest: {
            method: 'POST',
            path: API_PATHS.HOTELS.QUOTES,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              [HTTP_HEADERS.X_QH_USER_ID]: 'user-123',
            },
            body: {
              propertyId: TEST_DATA.PROPERTY_IDS.VALID,
              roomTypeId: 'room-type-123',
              offerId: 'offer-456',
              checkIn: TEST_DATA.DATES.CHECK_IN,
              checkOut: TEST_DATA.DATES.CHECK_OUT,
              adults: TEST_DATA.GUESTS.COUPLE.adults,
              children: TEST_DATA.GUESTS.COUPLE.children,
              infants: TEST_DATA.GUESTS.COUPLE.infants,
              flightBookerToken: 'flight-token-123',
              paidByDeposit: 'false',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.CREATED,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedQuoteResponse,
          },
        });
      });

      it('should create a quote with points and cash payment options', async () => {
        const result = await createQuote({
          payload: quotePayload,
          accessToken: 'valid-token',
          flightBookerToken: 'flight-token-123',
          qhUserId: 'user-123',
        });

        expect(result).toEqual(expectedQuoteResponse);
        expect(result.quote.paymentOptions.points.available).toBe(true);
        expect(result.quote.paymentOptions.pointsAndCash.available).toBe(true);
        expect(result.quote.paymentOptions.points.amount).toBeGreaterThan(0);
        expect(result.quote.paymentOptions.pointsAndCash.pointsAmount).toBeGreaterThan(0);
        expect(result.quote.paymentOptions.pointsAndCash.cashAmount).toBeGreaterThan(0);
        expect(result.quote.pointsEarn).toBeDefined();
        expect(result.quote.pointsEarn.totalPoints).toBeGreaterThan(0);
      });
    });

    describe('successful quote creation with deposit payment', () => {
      const quotePayload = {
        propertyId: TEST_DATA.PROPERTY_IDS.VALID,
        roomTypeId: 'room-type-123',
        offerId: 'offer-456',
        checkIn: dateUtils.getFutureDate(30), // 30 days in future for deposit eligibility
        checkOut: dateUtils.getFutureDate(34),
        adults: TEST_DATA.GUESTS.COUPLE.adults,
        children: TEST_DATA.GUESTS.COUPLE.children,
        infants: TEST_DATA.GUESTS.COUPLE.infants,
      };

      const expectedQuoteResponse = {
        quote: {
          id: 'quote-deposit-789',
          propertyId: TEST_DATA.PROPERTY_IDS.VALID,
          roomTypeId: 'room-type-123',
          offerId: 'offer-456',
          checkIn: dateUtils.getFutureDate(30),
          checkOut: dateUtils.getFutureDate(34),
          guests: TEST_DATA.GUESTS.COUPLE,
          pricing: {
            currency: TEST_DATA.CURRENCIES.AUD,
            baseRate: 200.0,
            taxes: 20.0,
            fees: 10.0,
            totalRate: 230.0,
            breakdown: [
              {
                description: 'Room rate (4 nights)',
                amount: 800.0,
              },
              {
                description: 'Taxes and fees',
                amount: 120.0,
              },
            ],
          },
          paymentOptions: {
            cash: {
              available: true,
              amount: 920.0,
              currency: TEST_DATA.CURRENCIES.AUD,
            },
            deposit: {
              available: true,
              depositAmount: 184.0, // 20% of total
              remainingAmount: 736.0,
              currency: TEST_DATA.CURRENCIES.AUD,
              dueDate: dateUtils.getFutureDate(23), // 7 days before check-in
            },
          },
          policies: {
            cancellation: 'Free cancellation up to 24 hours before check-in',
            modification: 'Modifications allowed up to 48 hours before check-in',
            deposit: 'Deposit payment available for bookings made 21+ days before check-in',
          },
          validUntil: dateUtils.getFutureDate(1),
          createdAt: dateUtils.getCurrentDateTime(),
        },
      };

      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.PROPERTIES.PROPERTY_EXISTS,
          uponReceiving: 'a request to create a quote with deposit payment',
          withRequest: {
            method: 'POST',
            path: API_PATHS.HOTELS.QUOTES,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              [HTTP_HEADERS.X_QH_USER_ID]: 'user-123',
            },
            body: {
              propertyId: TEST_DATA.PROPERTY_IDS.VALID,
              roomTypeId: 'room-type-123',
              offerId: 'offer-456',
              checkIn: dateUtils.getFutureDate(30),
              checkOut: dateUtils.getFutureDate(34),
              adults: TEST_DATA.GUESTS.COUPLE.adults,
              children: TEST_DATA.GUESTS.COUPLE.children,
              infants: TEST_DATA.GUESTS.COUPLE.infants,
              flightBookerToken: null,
              paidByDeposit: 'true',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.CREATED,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedQuoteResponse,
          },
        });
      });

      it('should create a quote with deposit payment option', async () => {
        const result = await createQuote({
          payload: quotePayload,
          accessToken: 'valid-token',
          qhUserId: 'user-123',
          paidByDeposit: 'true',
        });

        expect(result).toEqual(expectedQuoteResponse);
        expect(result.quote.paymentOptions.deposit.available).toBe(true);
        expect(result.quote.paymentOptions.deposit.depositAmount).toBeGreaterThan(0);
        expect(result.quote.paymentOptions.deposit.remainingAmount).toBeGreaterThan(0);
        expect(result.quote.paymentOptions.deposit.dueDate).toBeDefined();
        expect(result.quote.policies.deposit).toBeDefined();
      });
    });

    describe('quote creation with invalid property', () => {
      const quotePayload = {
        propertyId: TEST_DATA.PROPERTY_IDS.NOT_FOUND,
        roomTypeId: 'room-type-123',
        offerId: 'offer-456',
        checkIn: TEST_DATA.DATES.CHECK_IN,
        checkOut: TEST_DATA.DATES.CHECK_OUT,
        adults: 2,
        children: 0,
        infants: 0,
      };

      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.PROPERTIES.PROPERTY_NOT_FOUND,
          uponReceiving: 'a request to create a quote for invalid property',
          withRequest: {
            method: 'POST',
            path: API_PATHS.HOTELS.QUOTES,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              [HTTP_HEADERS.X_QH_USER_ID]: 'user-123',
            },
            body: {
              propertyId: TEST_DATA.PROPERTY_IDS.NOT_FOUND,
              roomTypeId: 'room-type-123',
              offerId: 'offer-456',
              checkIn: TEST_DATA.DATES.CHECK_IN,
              checkOut: TEST_DATA.DATES.CHECK_OUT,
              adults: 2,
              children: 0,
              infants: 0,
              flightBookerToken: null,
              paidByDeposit: 'false',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.NOT_FOUND,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'PROPERTY_NOT_FOUND',
                message: 'Property not found',
                details: {
                  propertyId: TEST_DATA.PROPERTY_IDS.NOT_FOUND,
                },
              },
              timestamp: dateUtils.getCurrentDateTime(),
              requestId: dataUtils.randomUUID(),
            },
          },
        });
      });

      it('should return 404 for invalid property', async () => {
        await expect(
          createQuote({
            payload: quotePayload,
            accessToken: 'valid-token',
            qhUserId: 'user-123',
          }),
        ).rejects.toThrow();
      });
    });

    describe('quote creation with invalid dates', () => {
      const quotePayload = {
        propertyId: TEST_DATA.PROPERTY_IDS.VALID,
        roomTypeId: 'room-type-123',
        offerId: 'offer-456',
        checkIn: TEST_DATA.DATES.PAST_DATE,
        checkOut: TEST_DATA.DATES.CHECK_IN,
        adults: 2,
        children: 0,
        infants: 0,
      };

      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.PROPERTIES.PROPERTY_EXISTS,
          uponReceiving: 'a request to create a quote with invalid dates',
          withRequest: {
            method: 'POST',
            path: API_PATHS.HOTELS.QUOTES,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              [HTTP_HEADERS.X_QH_USER_ID]: 'user-123',
            },
            body: {
              propertyId: TEST_DATA.PROPERTY_IDS.VALID,
              roomTypeId: 'room-type-123',
              offerId: 'offer-456',
              checkIn: TEST_DATA.DATES.PAST_DATE,
              checkOut: TEST_DATA.DATES.CHECK_IN,
              adults: 2,
              children: 0,
              infants: 0,
              flightBookerToken: null,
              paidByDeposit: 'false',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.BAD_REQUEST,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'INVALID_DATES',
                message: 'Check-in date must be in the future and before check-out date',
                details: {
                  checkIn: TEST_DATA.DATES.PAST_DATE,
                  checkOut: TEST_DATA.DATES.CHECK_IN,
                  issues: ['Check-in date is in the past', 'Check-out date must be after check-in date'],
                },
              },
              timestamp: dateUtils.getCurrentDateTime(),
              requestId: dataUtils.randomUUID(),
            },
          },
        });
      });

      it('should return 400 for invalid dates', async () => {
        await expect(
          createQuote({
            payload: quotePayload,
            accessToken: 'valid-token',
            qhUserId: 'user-123',
          }),
        ).rejects.toThrow();
      });
    });

    describe('quote creation with authentication error', () => {
      const quotePayload = {
        propertyId: TEST_DATA.PROPERTY_IDS.VALID,
        roomTypeId: 'room-type-123',
        offerId: 'offer-456',
        checkIn: TEST_DATA.DATES.CHECK_IN,
        checkOut: TEST_DATA.DATES.CHECK_OUT,
        adults: 2,
        children: 0,
        infants: 0,
      };

      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.AUTH.TOKEN_INVALID,
          uponReceiving: 'a request to create a quote with invalid token',
          withRequest: {
            method: 'POST',
            path: API_PATHS.HOTELS.QUOTES,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer invalid-token',
              [HTTP_HEADERS.X_QH_USER_ID]: 'user-123',
            },
            body: {
              propertyId: TEST_DATA.PROPERTY_IDS.VALID,
              roomTypeId: 'room-type-123',
              offerId: 'offer-456',
              checkIn: TEST_DATA.DATES.CHECK_IN,
              checkOut: TEST_DATA.DATES.CHECK_OUT,
              adults: 2,
              children: 0,
              infants: 0,
              flightBookerToken: null,
              paidByDeposit: 'false',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.UNAUTHORIZED,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'UNAUTHORIZED',
                message: 'Invalid or expired authentication token',
              },
              timestamp: dateUtils.getCurrentDateTime(),
              requestId: dataUtils.randomUUID(),
            },
          },
        });
      });

      it('should return 401 for invalid authentication', async () => {
        await expect(
          createQuote({
            payload: quotePayload,
            accessToken: 'invalid-token',
            qhUserId: 'user-123',
          }),
        ).rejects.toThrow();
      });
    });
  });
});
