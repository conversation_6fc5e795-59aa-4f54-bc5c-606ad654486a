/**
 * Contract tests for Hotels API member services endpoints
 * Tests the consumer contract for member details, favourites, and profile management
 */

const { Matchers } = require('@pact-foundation/pact');
const { createContractTestSuite, createMockServerUrlGetter, addInteractionSafely } = require('../helpers/testRunner');
const axios = require('axios');
const { API_PATHS, HTTP_STATUS, HTTP_HEADERS, CONTENT_TYPES, TEST_DATA, PROVIDER_STATES } = require('../shared/constants');

// Define fail function for test assertions
const fail = (message) => {
  throw new Error(message);
};

createContractTestSuite('Hotels API Member Services Contract Tests', 'unknown-provider', (contractTest) => {
  const getMockServerUrl = createMockServerUrlGetter(contractTest);

  describe('GET /member-details/{memberId} - Member Details Retrieval', () => {
    describe('successful member details retrieval', () => {
      const expectedMemberDetails = {
        member: {
          memberId: TEST_DATA.MEMBER_IDS.VALID,
          personalDetails: {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            phone: '+***********',
            dateOfBirth: '1985-06-15',
          },
          qffDetails: {
            tierStatus: 'Gold',
            pointsBalance: Matchers.integer(75000),
            statusCredits: Matchers.integer(450),
            nextTierRequirement: {
              tier: 'Platinum',
              creditsNeeded: Matchers.integer(150),
            },
            isVip: false,
          },
          preferences: {
            currency: 'AUD',
            language: 'en-AU',
            timezone: 'Australia/Sydney',
            marketingOptIn: true,
            communicationPreferences: {
              email: true,
              sms: false,
              push: true,
            },
          },
          membershipDetails: {
            joinDate: '2018-03-15',
            lastLoginDate: Matchers.iso8601DateTime(),
            accountStatus: 'active',
          },
        },
      };

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.AUTH.USER_AUTHENTICATED,
          uponReceiving: 'a request to get member details',
          withRequest: {
            method: 'GET',
            path: `/member-details/${TEST_DATA.MEMBER_IDS.VALID}`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
              'qh-meta': Matchers.somethingLike('{"qhUserId":"user-123"}'),
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedMemberDetails,
          },
        });
      });

      it('should retrieve member details successfully', async () => {
        const response = await axios({
          method: 'GET',
          url: `${getMockServerUrl()}/member-details/${TEST_DATA.MEMBER_IDS.VALID}`,
          headers: {
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
            'qh-meta': '{"qhUserId":"user-123"}',
          },
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toMatchObject({
          member: {
            memberId: TEST_DATA.MEMBER_IDS.VALID,
            personalDetails: {
              firstName: 'John',
              lastName: 'Doe',
              email: '<EMAIL>',
              phone: '+***********',
              dateOfBirth: '1985-06-15',
            },
            qffDetails: {
              tierStatus: 'Gold',
              nextTierRequirement: {
                tier: 'Platinum',
              },
              isVip: false,
            },
            preferences: {
              currency: 'AUD',
              language: 'en-AU',
              timezone: 'Australia/Sydney',
              marketingOptIn: true,
              communicationPreferences: {
                email: true,
                sms: false,
                push: true,
              },
            },
            membershipDetails: {
              joinDate: '2018-03-15',
              accountStatus: 'active',
            },
          },
        });
        expect(response.data.member.qffDetails.pointsBalance).toBeGreaterThan(0);
        expect(response.data.member.qffDetails.statusCredits).toBeGreaterThan(0);
        expect(response.data.member.qffDetails.nextTierRequirement.creditsNeeded).toBeGreaterThan(0);
        expect(response.data.member.membershipDetails.lastLoginDate).toBeDefined();
      });
    });

    describe('member not found', () => {
      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.AUTH.USER_AUTHENTICATED,
          uponReceiving: 'a request to get non-existent member details',
          withRequest: {
            method: 'GET',
            path: `/member-details/${TEST_DATA.MEMBER_IDS.NOT_FOUND}`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
              'qh-meta': Matchers.somethingLike('{"qhUserId":"user-123"}'),
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.NOT_FOUND,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'MEMBER_NOT_FOUND',
                message: 'Member not found',
                details: {
                  memberId: TEST_DATA.MEMBER_IDS.NOT_FOUND,
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 404 for non-existent member', async () => {
        try {
          await axios({
            method: 'GET',
            url: `${getMockServerUrl()}/member-details/${TEST_DATA.MEMBER_IDS.NOT_FOUND}`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
              'qh-meta': '{"qhUserId":"user-123"}',
            },
          });
          fail('Expected request to fail with 404');
        } catch (error) {
          expect(error.response?.status).toBe(HTTP_STATUS.NOT_FOUND);
          if (error.response) {
            expect(error.response.data).toMatchObject({
              error: {
                code: 'MEMBER_NOT_FOUND',
                message: 'Member not found',
                details: {
                  memberId: TEST_DATA.MEMBER_IDS.NOT_FOUND,
                },
              },
            });
          }
        }
      });
    });

    describe('unauthorized access', () => {
      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.AUTH.TOKEN_INVALID,
          uponReceiving: 'a request to get member details with invalid token',
          withRequest: {
            method: 'GET',
            path: `/member-details/${TEST_DATA.MEMBER_IDS.VALID}`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer invalid-token',
              [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
              'qh-meta': Matchers.somethingLike('{"qhUserId":"user-123"}'),
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.UNAUTHORIZED,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'UNAUTHORIZED',
                message: 'Invalid or expired authentication token',
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 401 for invalid authentication', async () => {
        try {
          await axios({
            method: 'GET',
            url: `${getMockServerUrl()}/member-details/${TEST_DATA.MEMBER_IDS.VALID}`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer invalid-token',
              [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
              'qh-meta': '{"qhUserId":"user-123"}',
            },
          });
          fail('Expected request to fail with 401');
        } catch (error) {
          expect(error.response?.status).toBe(HTTP_STATUS.UNAUTHORIZED);
          if (error.response) {
            expect(error.response.data).toMatchObject({
              error: {
                code: 'UNAUTHORIZED',
                message: 'Invalid or expired authentication token',
              },
            });
          }
        }
      });
    });
  });

  describe('GET /member-favourites - Member Favourites Management', () => {
    describe('successful favourites retrieval', () => {
      const expectedFavourites = {
        favourites: [
          {
            id: 'fav-123',
            propertyId: TEST_DATA.PROPERTY_IDS.VALID,
            propertyName: 'Sydney Harbour Hotel',
            propertyLocation: {
              city: 'Sydney',
              country: 'Australia',
              coordinates: {
                latitude: Matchers.decimal(-33.8688),
                longitude: Matchers.decimal(151.2093),
              },
            },
            propertyRating: {
              type: 'star',
              value: Matchers.integer(5),
            },
            addedDate: '2024-01-15T10:30:00Z',
            lastViewedDate: '2024-02-20T14:45:00Z',
          },
          {
            id: 'fav-456',
            propertyId: 'property-456',
            propertyName: 'Melbourne Grand Hotel',
            propertyLocation: {
              city: 'Melbourne',
              country: 'Australia',
              coordinates: {
                latitude: Matchers.decimal(-37.8136),
                longitude: Matchers.decimal(144.9631),
              },
            },
            propertyRating: {
              type: 'star',
              value: Matchers.integer(4),
            },
            addedDate: '2024-02-01T09:15:00Z',
            lastViewedDate: '2024-02-18T16:20:00Z',
          },
        ],
        pagination: {
          page: 1,
          totalPages: 1,
          totalResults: 2,
          limit: 20,
        },
      };

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.AUTH.USER_AUTHENTICATED,
          uponReceiving: 'a request to get member favourites',
          withRequest: {
            method: 'GET',
            path: API_PATHS.HOTELS.MEMBER_FAVOURITES,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
              'qh-meta': Matchers.somethingLike('{"qhUserId":"user-123"}'),
            },
            query: {
              page: '1',
              limit: '20',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedFavourites,
          },
        });
      });

      it('should retrieve member favourites successfully', async () => {
        const response = await axios({
          method: 'GET',
          url: `${getMockServerUrl()}${API_PATHS.HOTELS.MEMBER_FAVOURITES}`,
          headers: {
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
            'qh-meta': '{"qhUserId":"user-123"}',
          },
          params: {
            page: '1',
            limit: '20',
          },
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toMatchObject({
          favourites: [
            {
              id: 'fav-123',
              propertyId: TEST_DATA.PROPERTY_IDS.VALID,
              propertyName: 'Sydney Harbour Hotel',
              propertyLocation: {
                city: 'Sydney',
                country: 'Australia',
              },
              propertyRating: {
                type: 'star',
              },
              addedDate: '2024-01-15T10:30:00Z',
              lastViewedDate: '2024-02-20T14:45:00Z',
            },
            {
              id: 'fav-456',
              propertyId: 'property-456',
              propertyName: 'Melbourne Grand Hotel',
              propertyLocation: {
                city: 'Melbourne',
                country: 'Australia',
              },
              propertyRating: {
                type: 'star',
              },
              addedDate: '2024-02-01T09:15:00Z',
              lastViewedDate: '2024-02-18T16:20:00Z',
            },
          ],
          pagination: {
            page: 1,
            totalPages: 1,
            totalResults: 2,
            limit: 20,
          },
        });

        // Validate coordinates and ratings are numbers
        expect(response.data.favourites[0].propertyLocation.coordinates.latitude).toBeCloseTo(-33.8688, 4);
        expect(response.data.favourites[0].propertyLocation.coordinates.longitude).toBeCloseTo(151.2093, 4);
        expect(response.data.favourites[0].propertyRating.value).toBeGreaterThan(0);
        expect(response.data.favourites[1].propertyRating.value).toBeGreaterThan(0);
      });
    });

    describe('empty favourites list', () => {
      const emptyFavourites = {
        favourites: [],
        pagination: {
          page: 1,
          totalPages: 0,
          totalResults: 0,
          limit: 20,
        },
      };

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.AUTH.USER_AUTHENTICATED,
          uponReceiving: 'a request to get member favourites with no favourites',
          withRequest: {
            method: 'GET',
            path: API_PATHS.HOTELS.MEMBER_FAVOURITES,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
              'qh-meta': Matchers.somethingLike('{"qhUserId":"user-123"}'),
            },
            query: {
              page: '1',
              limit: '20',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: emptyFavourites,
          },
        });
      });

      it('should return empty favourites list', async () => {
        const response = await axios({
          method: 'GET',
          url: `${getMockServerUrl()}${API_PATHS.HOTELS.MEMBER_FAVOURITES}`,
          headers: {
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
            'qh-meta': '{"qhUserId":"user-123"}',
          },
          params: {
            page: '1',
            limit: '20',
          },
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toMatchObject({
          favourites: [],
          pagination: {
            page: 1,
            totalPages: 0,
            totalResults: 0,
            limit: 20,
          },
        });
      });
    });
  });

  describe('POST /member-favourites - Add Property to Favourites', () => {
    describe('successful favourite addition', () => {
      const addFavouritePayload = {
        propertyId: TEST_DATA.PROPERTY_IDS.VALID,
      };

      const expectedAddResponse = {
        success: true,
        favourite: {
          id: Matchers.somethingLike('fav-789'),
          propertyId: TEST_DATA.PROPERTY_IDS.VALID,
          addedDate: Matchers.iso8601DateTime(),
        },
      };

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.AUTH.USER_AUTHENTICATED,
          uponReceiving: 'a request to add property to favourites',
          withRequest: {
            method: 'POST',
            path: API_PATHS.HOTELS.MEMBER_FAVOURITES,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
              'qh-meta': Matchers.somethingLike('{"qhUserId":"user-123"}'),
            },
            body: addFavouritePayload,
          },
          willRespondWith: {
            status: HTTP_STATUS.CREATED,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedAddResponse,
          },
        });
      });

      it('should add property to favourites successfully', async () => {
        const response = await axios({
          method: 'POST',
          url: `${getMockServerUrl()}${API_PATHS.HOTELS.MEMBER_FAVOURITES}`,
          headers: {
            [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
            'qh-meta': '{"qhUserId":"user-123"}',
          },
          data: addFavouritePayload,
        });

        expect(response.status).toBe(HTTP_STATUS.CREATED);
        expect(response.data).toMatchObject({
          success: true,
          favourite: {
            propertyId: TEST_DATA.PROPERTY_IDS.VALID,
          },
        });
        expect(response.data.favourite.id).toBeDefined();
        expect(response.data.favourite.addedDate).toBeDefined();
      });
    });

    describe('duplicate favourite addition', () => {
      const duplicateFavouritePayload = {
        propertyId: TEST_DATA.PROPERTY_IDS.VALID,
      };

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.AUTH.USER_AUTHENTICATED,
          uponReceiving: 'a request to add already favourited property',
          withRequest: {
            method: 'POST',
            path: API_PATHS.HOTELS.MEMBER_FAVOURITES,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
              'qh-meta': Matchers.somethingLike('{"qhUserId":"user-123"}'),
            },
            body: duplicateFavouritePayload,
          },
          willRespondWith: {
            status: HTTP_STATUS.CONFLICT,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              success: false,
              error: {
                code: 'FAVOURITE_ALREADY_EXISTS',
                message: 'Property is already in favourites',
                details: {
                  propertyId: TEST_DATA.PROPERTY_IDS.VALID,
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 409 for duplicate favourite', async () => {
        try {
          await axios({
            method: 'POST',
            url: `${getMockServerUrl()}${API_PATHS.HOTELS.MEMBER_FAVOURITES}`,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
              'qh-meta': '{"qhUserId":"user-123"}',
            },
            data: duplicateFavouritePayload,
          });
          fail('Expected request to fail with 409');
        } catch (error) {
          expect(error.response?.status).toBe(HTTP_STATUS.CONFLICT);
          if (error.response) {
            expect(error.response.data).toMatchObject({
              success: false,
              error: {
                code: 'FAVOURITE_ALREADY_EXISTS',
                message: 'Property is already in favourites',
                details: {
                  propertyId: TEST_DATA.PROPERTY_IDS.VALID,
                },
              },
            });
          }
        }
      });
    });
  });

  describe('DELETE /member-favourites/{favouriteId} - Remove Property from Favourites', () => {
    describe('successful favourite removal', () => {
      const favouriteId = 'fav-123';

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.AUTH.USER_AUTHENTICATED,
          uponReceiving: 'a request to remove property from favourites',
          withRequest: {
            method: 'DELETE',
            path: `/member-favourites/${favouriteId}`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
              'qh-meta': Matchers.somethingLike('{"qhUserId":"user-123"}'),
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              success: true,
              message: 'Property removed from favourites',
              removedFavourite: {
                id: favouriteId,
                removedDate: Matchers.iso8601DateTime(),
              },
            },
          },
        });
      });

      it('should remove property from favourites successfully', async () => {
        const response = await axios({
          method: 'DELETE',
          url: `${getMockServerUrl()}/member-favourites/${favouriteId}`,
          headers: {
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
            'qh-meta': '{"qhUserId":"user-123"}',
          },
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toMatchObject({
          success: true,
          message: 'Property removed from favourites',
          removedFavourite: {
            id: favouriteId,
          },
        });
        expect(response.data.removedFavourite.removedDate).toBeDefined();
      });
    });

    describe('favourite not found for removal', () => {
      const nonExistentFavouriteId = 'fav-404';

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.AUTH.USER_AUTHENTICATED,
          uponReceiving: 'a request to remove non-existent favourite',
          withRequest: {
            method: 'DELETE',
            path: `/member-favourites/${nonExistentFavouriteId}`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
              'qh-meta': Matchers.somethingLike('{"qhUserId":"user-123"}'),
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.NOT_FOUND,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              success: false,
              error: {
                code: 'FAVOURITE_NOT_FOUND',
                message: 'Favourite not found',
                details: {
                  favouriteId: nonExistentFavouriteId,
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 404 for non-existent favourite', async () => {
        try {
          await axios({
            method: 'DELETE',
            url: `${getMockServerUrl()}/member-favourites/${nonExistentFavouriteId}`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
              'qh-meta': '{"qhUserId":"user-123"}',
            },
          });
          fail('Expected request to fail with 404');
        } catch (error) {
          expect(error.response?.status).toBe(HTTP_STATUS.NOT_FOUND);
          if (error.response) {
            expect(error.response.data).toMatchObject({
              success: false,
              error: {
                code: 'FAVOURITE_NOT_FOUND',
                message: 'Favourite not found',
                details: {
                  favouriteId: nonExistentFavouriteId,
                },
              },
            });
          }
        }
      });
    });
  });

  describe('PUT /member-details/{memberId} - Update Member Profile', () => {
    describe('successful profile update', () => {
      const updatePayload = {
        personalDetails: {
          firstName: 'John',
          lastName: 'Smith', // Changed from Doe
          phone: '+61423456789', // Updated phone
        },
        preferences: {
          currency: 'USD', // Changed from AUD
          language: 'en-US', // Changed from en-AU
          timezone: 'America/New_York', // Changed timezone
          marketingOptIn: false, // Changed from true
          communicationPreferences: {
            email: true,
            sms: true, // Changed from false
            push: false, // Changed from true
          },
        },
      };

      const expectedUpdateResponse = {
        success: true,
        member: {
          memberId: TEST_DATA.MEMBER_IDS.VALID,
          personalDetails: {
            firstName: 'John',
            lastName: 'Smith',
            email: '<EMAIL>', // Email unchanged
            phone: '+61423456789',
            dateOfBirth: '1985-06-15', // Unchanged
          },
          preferences: {
            currency: 'USD',
            language: 'en-US',
            timezone: 'America/New_York',
            marketingOptIn: false,
            communicationPreferences: {
              email: true,
              sms: true,
              push: false,
            },
          },
          updatedAt: Matchers.iso8601DateTime(),
        },
      };

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.AUTH.USER_AUTHENTICATED,
          uponReceiving: 'a request to update member profile',
          withRequest: {
            method: 'PUT',
            path: `/member-details/${TEST_DATA.MEMBER_IDS.VALID}`,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
              'qh-meta': Matchers.somethingLike('{"qhUserId":"user-123"}'),
            },
            body: updatePayload,
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedUpdateResponse,
          },
        });
      });

      it('should update member profile successfully', async () => {
        const response = await axios({
          method: 'PUT',
          url: `${getMockServerUrl()}/member-details/${TEST_DATA.MEMBER_IDS.VALID}`,
          headers: {
            [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
            'qh-meta': '{"qhUserId":"user-123"}',
          },
          data: updatePayload,
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toMatchObject({
          success: true,
          member: {
            memberId: TEST_DATA.MEMBER_IDS.VALID,
            personalDetails: {
              firstName: 'John',
              lastName: 'Smith',
              email: '<EMAIL>',
              phone: '+61423456789',
              dateOfBirth: '1985-06-15',
            },
            preferences: {
              currency: 'USD',
              language: 'en-US',
              timezone: 'America/New_York',
              marketingOptIn: false,
              communicationPreferences: {
                email: true,
                sms: true,
                push: false,
              },
            },
          },
        });
        expect(response.data.member.updatedAt).toBeDefined();
      });
    });

    describe('validation error on profile update', () => {
      const invalidUpdatePayload = {
        personalDetails: {
          firstName: '', // Invalid empty name
          phone: 'invalid-phone', // Invalid phone format
        },
        preferences: {
          currency: 'INVALID', // Invalid currency code
          language: 'invalid-lang', // Invalid language code
        },
      };

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.AUTH.USER_AUTHENTICATED,
          uponReceiving: 'a request to update member profile with invalid data',
          withRequest: {
            method: 'PUT',
            path: `/member-details/${TEST_DATA.MEMBER_IDS.VALID}`,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
              'qh-meta': Matchers.somethingLike('{"qhUserId":"user-123"}'),
            },
            body: invalidUpdatePayload,
          },
          willRespondWith: {
            status: HTTP_STATUS.BAD_REQUEST,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              success: false,
              error: {
                code: 'VALIDATION_ERROR',
                message: 'Invalid data provided',
                details: {
                  validationErrors: [
                    {
                      field: 'personalDetails.firstName',
                      message: 'First name cannot be empty',
                    },
                    {
                      field: 'personalDetails.phone',
                      message: 'Invalid phone number format',
                    },
                    {
                      field: 'preferences.currency',
                      message: 'Invalid currency code',
                    },
                    {
                      field: 'preferences.language',
                      message: 'Invalid language code',
                    },
                  ],
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 400 for validation errors', async () => {
        try {
          await axios({
            method: 'PUT',
            url: `${getMockServerUrl()}/member-details/${TEST_DATA.MEMBER_IDS.VALID}`,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              [HTTP_HEADERS.ACCEPT]: CONTENT_TYPES.JSON,
              'qh-meta': '{"qhUserId":"user-123"}',
            },
            data: invalidUpdatePayload,
          });
          fail('Expected request to fail with 400');
        } catch (error) {
          expect(error.response?.status).toBe(HTTP_STATUS.BAD_REQUEST);
          if (error.response) {
            expect(error.response.data).toMatchObject({
              success: false,
              error: {
                code: 'VALIDATION_ERROR',
                message: 'Invalid data provided',
                details: {
                  validationErrors: expect.arrayContaining([
                    expect.objectContaining({
                      field: 'personalDetails.firstName',
                      message: 'First name cannot be empty',
                    }),
                    expect.objectContaining({
                      field: 'personalDetails.phone',
                      message: 'Invalid phone number format',
                    }),
                    expect.objectContaining({
                      field: 'preferences.currency',
                      message: 'Invalid currency code',
                    }),
                    expect.objectContaining({
                      field: 'preferences.language',
                      message: 'Invalid language code',
                    }),
                  ]),
                },
              },
            });
          }
        }
      });
    });
  });
});
