/**
 * Contract tests for Hotels API booking endpoints
 * Tests the consumer contract for booking creation and retrieval
 */

const { Matchers } = require('@pact-foundation/pact');
const axios = require('axios');
const { createContractTestSuite, createMockServerUrlGetter, addInteractionSafely } = require('../helpers/testRunner');
const { API_PATHS, HTTP_STATUS, HTTP_HEADERS, CONTENT_TYPES, TEST_DATA, PROVIDER_STATES } = require('../shared/constants');

// Define fail function for test assertions
const fail = (message) => {
  throw new Error(message);
};

createContractTestSuite('Hotels API Booking Endpoints Contract Tests', 'hotels-api', (getContractTest) => {
  const getMockServerUrl = createMockServerUrlGetter(getContractTest);

  describe('POST /bookings - Create Booking', () => {
    describe('successful booking creation', () => {
      const bookingPayload = {
        propertyId: TEST_DATA.PROPERTY_IDS.VALID,
        roomTypeId: 'room-type-123',
        offerId: 'offer-456',
        checkIn: TEST_DATA.DATES.CHECK_IN,
        checkOut: TEST_DATA.DATES.CHECK_OUT,
        adults: TEST_DATA.GUESTS.COUPLE.adults,
        children: TEST_DATA.GUESTS.COUPLE.children,
        infants: TEST_DATA.GUESTS.COUPLE.infants,
        guestDetails: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '+61412345678',
        },
        paymentDetails: {
          method: 'credit_card',
          cardToken: 'card-token-123',
        },
      };

      const expectedBookingResponse = {
        booking: {
          id: Matchers.somethingLike('booking-789'),
          status: 'confirmed',
          propertyId: TEST_DATA.PROPERTY_IDS.VALID,
          checkIn: TEST_DATA.DATES.CHECK_IN,
          checkOut: TEST_DATA.DATES.CHECK_OUT,
          guests: TEST_DATA.GUESTS.COUPLE,
          totalAmount: {
            currency: TEST_DATA.CURRENCIES.AUD,
            amount: Matchers.decimal(450.0),
          },
          confirmationNumber: Matchers.somethingLike('QH123456789'),
          createdAt: Matchers.iso8601DateTime(),
        },
      };

      beforeEach(async () => {
        await addInteractionSafely(getContractTest, {
          state: PROVIDER_STATES.BOOKINGS.BOOKING_CAN_BE_CREATED,
          uponReceiving: 'a request to create a booking',
          withRequest: {
            method: 'POST',
            path: API_PATHS.HOTELS.BOOKINGS,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              'qh-meta': Matchers.somethingLike('{"qhUserId":"user-123"}'),
            },
            body: bookingPayload,
          },
          willRespondWith: {
            status: HTTP_STATUS.CREATED,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedBookingResponse,
          },
        });
      });

      it('should create a booking successfully', async () => {
        const response = await axios({
          method: 'POST',
          url: `${getMockServerUrl()}${API_PATHS.HOTELS.BOOKINGS}`,
          headers: {
            [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            'qh-meta': '{"qhUserId":"user-123"}',
          },
          data: bookingPayload,
        });

        expect(response.status).toBe(HTTP_STATUS.CREATED);
        expect(response.data).toMatchObject({
          booking: {
            status: 'confirmed',
            propertyId: TEST_DATA.PROPERTY_IDS.VALID,
            checkIn: TEST_DATA.DATES.CHECK_IN,
            checkOut: TEST_DATA.DATES.CHECK_OUT,
            guests: TEST_DATA.GUESTS.COUPLE,
            totalAmount: {
              currency: TEST_DATA.CURRENCIES.AUD,
            },
          },
        });
        expect(response.data.booking.id).toBeDefined();
        expect(response.data.booking.confirmationNumber).toBeDefined();
        expect(response.data.booking.totalAmount.amount).toBeGreaterThan(0);
        expect(response.data.booking.createdAt).toBeDefined();
      });
    });

    describe('booking creation with authentication error', () => {
      const bookingPayload = {
        propertyId: TEST_DATA.PROPERTY_IDS.VALID,
        roomTypeId: 'room-type-123',
        offerId: 'offer-456',
        checkIn: TEST_DATA.DATES.CHECK_IN,
        checkOut: TEST_DATA.DATES.CHECK_OUT,
        adults: 2,
        children: 0,
        infants: 0,
      };

      beforeEach(async () => {
        await addInteractionSafely(getContractTest, {
          state: PROVIDER_STATES.AUTH.TOKEN_INVALID,
          uponReceiving: 'a request to create a booking with invalid token',
          withRequest: {
            method: 'POST',
            path: API_PATHS.HOTELS.BOOKINGS,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer invalid-token',
              'qh-meta': Matchers.somethingLike('{"qhUserId":"user-123"}'),
            },
            body: bookingPayload,
          },
          willRespondWith: {
            status: HTTP_STATUS.UNAUTHORIZED,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'UNAUTHORIZED',
                message: 'Invalid or expired authentication token',
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 401 for invalid authentication', async () => {
        try {
          await axios({
            method: 'POST',
            url: `${getMockServerUrl()}${API_PATHS.HOTELS.BOOKINGS}`,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer invalid-token',
              'qh-meta': '{"qhUserId":"user-123"}',
            },
            data: bookingPayload,
          });
          fail('Expected request to fail with 401');
        } catch (error) {
          expect(error.response?.status).toBe(HTTP_STATUS.UNAUTHORIZED);
          if (error.response) {
            expect(error.response.data).toMatchObject({
              error: {
                code: 'UNAUTHORIZED',
                message: 'Invalid or expired authentication token',
              },
            });
            expect(error.response.data.timestamp).toBeDefined();
            expect(error.response.data.requestId).toBeDefined();
          }
        }
      });
    });

    describe('booking creation with unavailable property', () => {
      const bookingPayload = {
        propertyId: TEST_DATA.PROPERTY_IDS.NOT_FOUND,
        roomTypeId: 'room-type-123',
        offerId: 'offer-456',
        checkIn: TEST_DATA.DATES.CHECK_IN,
        checkOut: TEST_DATA.DATES.CHECK_OUT,
        adults: 2,
        children: 0,
        infants: 0,
      };

      beforeEach(async () => {
        await addInteractionSafely(getContractTest, {
          state: PROVIDER_STATES.BOOKINGS.BOOKING_CANNOT_BE_CREATED,
          uponReceiving: 'a request to create a booking for unavailable property',
          withRequest: {
            method: 'POST',
            path: API_PATHS.HOTELS.BOOKINGS,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              'qh-meta': Matchers.somethingLike('{"qhUserId":"user-123"}'),
            },
            body: bookingPayload,
          },
          willRespondWith: {
            status: HTTP_STATUS.UNPROCESSABLE_ENTITY,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'PROPERTY_UNAVAILABLE',
                message: 'Property is not available for the selected dates',
                details: {
                  propertyId: TEST_DATA.PROPERTY_IDS.NOT_FOUND,
                  checkIn: TEST_DATA.DATES.CHECK_IN,
                  checkOut: TEST_DATA.DATES.CHECK_OUT,
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 422 for unavailable property', async () => {
        try {
          await axios({
            method: 'POST',
            url: `${getMockServerUrl()}${API_PATHS.HOTELS.BOOKINGS}`,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              'qh-meta': '{"qhUserId":"user-123"}',
            },
            data: bookingPayload,
          });
          fail('Expected request to fail with 422');
        } catch (error) {
          expect(error.response?.status).toBe(HTTP_STATUS.UNPROCESSABLE_ENTITY);
          if (error.response) {
            expect(error.response.data).toMatchObject({
              error: {
                code: 'PROPERTY_UNAVAILABLE',
                message: 'Property is not available for the selected dates',
                details: {
                  propertyId: TEST_DATA.PROPERTY_IDS.NOT_FOUND,
                  checkIn: TEST_DATA.DATES.CHECK_IN,
                  checkOut: TEST_DATA.DATES.CHECK_OUT,
                },
              },
            });
          }
        }
      });
    });
  });

  describe('GET /bookings/{id} - Fetch Booking by ID', () => {
    describe('successful booking retrieval', () => {
      const expectedBooking = {
        booking: {
          id: TEST_DATA.BOOKING_IDS.VALID,
          status: 'confirmed',
          propertyId: TEST_DATA.PROPERTY_IDS.VALID,
          propertyName: 'Test Hotel Sydney',
          checkIn: TEST_DATA.DATES.CHECK_IN,
          checkOut: TEST_DATA.DATES.CHECK_OUT,
          guests: TEST_DATA.GUESTS.COUPLE,
          totalAmount: {
            currency: TEST_DATA.CURRENCIES.AUD,
            amount: Matchers.decimal(450.0),
          },
          confirmationNumber: Matchers.somethingLike('QH123456789'),
          guestDetails: {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            phone: '+61412345678',
          },
          createdAt: Matchers.iso8601DateTime(),
          updatedAt: Matchers.iso8601DateTime(),
        },
      };

      beforeEach(async () => {
        await addInteractionSafely(getContractTest, {
          state: PROVIDER_STATES.BOOKINGS.BOOKING_EXISTS,
          uponReceiving: 'a request to fetch booking by ID',
          withRequest: {
            method: 'GET',
            path: `/bookings/${TEST_DATA.BOOKING_IDS.VALID}`,
            headers: {
              'qh-meta': Matchers.somethingLike('{"qhUserId":"user-123"}'),
            },
            query: {
              cachebuster: Matchers.somethingLike('0.123456789'),
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedBooking,
          },
        });
      });

      it('should fetch booking by ID successfully', async () => {
        const response = await axios({
          method: 'GET',
          url: `${getMockServerUrl()}/bookings/${TEST_DATA.BOOKING_IDS.VALID}`,
          headers: {
            'qh-meta': '{"qhUserId":"user-123"}',
          },
          params: {
            cachebuster: '0.123456789',
          },
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toMatchObject({
          booking: {
            id: TEST_DATA.BOOKING_IDS.VALID,
            status: 'confirmed',
            propertyId: TEST_DATA.PROPERTY_IDS.VALID,
            propertyName: 'Test Hotel Sydney',
            checkIn: TEST_DATA.DATES.CHECK_IN,
            checkOut: TEST_DATA.DATES.CHECK_OUT,
            guests: TEST_DATA.GUESTS.COUPLE,
            totalAmount: {
              currency: TEST_DATA.CURRENCIES.AUD,
            },
            guestDetails: {
              firstName: 'John',
              lastName: 'Doe',
              email: '<EMAIL>',
              phone: '+61412345678',
            },
          },
        });
        expect(response.data.booking.confirmationNumber).toBeDefined();
        expect(response.data.booking.totalAmount.amount).toBeGreaterThan(0);
        expect(response.data.booking.createdAt).toBeDefined();
        expect(response.data.booking.updatedAt).toBeDefined();
      });
    });

    describe('booking not found', () => {
      beforeEach(async () => {
        await addInteractionSafely(getContractTest, {
          state: PROVIDER_STATES.BOOKINGS.BOOKING_NOT_FOUND,
          uponReceiving: 'a request to fetch non-existent booking',
          withRequest: {
            method: 'GET',
            path: `/bookings/${TEST_DATA.BOOKING_IDS.NOT_FOUND}`,
            headers: {
              'qh-meta': Matchers.somethingLike('{"qhUserId":"user-123"}'),
            },
            query: {
              cachebuster: Matchers.somethingLike('0.123456789'),
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.NOT_FOUND,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'BOOKING_NOT_FOUND',
                message: 'Booking not found',
                details: {
                  bookingId: TEST_DATA.BOOKING_IDS.NOT_FOUND,
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 404 for non-existent booking', async () => {
        try {
          await axios({
            method: 'GET',
            url: `${getMockServerUrl()}/bookings/${TEST_DATA.BOOKING_IDS.NOT_FOUND}`,
            headers: {
              'qh-meta': '{"qhUserId":"user-123"}',
            },
            params: {
              cachebuster: '0.123456789',
            },
          });
          fail('Expected request to fail with 404');
        } catch (error) {
          expect(error.response?.status).toBe(HTTP_STATUS.NOT_FOUND);
          if (error.response) {
            expect(error.response.data).toMatchObject({
              error: {
                code: 'BOOKING_NOT_FOUND',
                message: 'Booking not found',
                details: {
                  bookingId: TEST_DATA.BOOKING_IDS.NOT_FOUND,
                },
              },
            });
          }
        }
      });
    });
  });
});
