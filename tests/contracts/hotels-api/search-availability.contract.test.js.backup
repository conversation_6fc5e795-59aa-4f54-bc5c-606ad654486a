/**
 * Contract tests for Hotels API search and availability endpoints
 * Tests the consumer contract for location availability search, property availability, and search filtering
 */

const { Pact } = require('@pact-foundation/pact');
const path = require('path');
const { searchLocationAvailability } = require('../../../src/lib/clients/searchLocationAvailability');
const { searchRelatedPropertyAvailability } = require('../../../src/lib/clients/searchRelatedPropertyAvailability');
const { HTTP_STATUS, HTTP_HEADERS, CONTENT_TYPES, TEST_DATA, PROVIDER_STATES } = require('../shared/constants');
const { dateUtils, dataUtils } = require('../shared/testUtils');

describe('Hotels API Search and Availability Contract Tests', () => {
  let provider;

  beforeAll(() => {
    provider = new Pact({
      consumer: 'qantas-hotels-ui',
      provider: 'hotels-api',
      port: 1237,
      log: path.resolve(process.cwd(), 'logs', 'pact.log'),
      dir: path.resolve(process.cwd(), 'pacts'),
      logLevel: 'INFO',
    });

    return provider.setup();
  });

  afterAll(() => {
    return provider.finalize();
  });

  afterEach(() => {
    return provider.verify();
  });

  describe('GET /locations/{location}/availability - Location Availability Search', () => {
    describe('successful location availability search', () => {
      const expectedAvailabilityResults = {
        location: TEST_DATA.LOCATIONS.SYDNEY,
        checkIn: TEST_DATA.DATES.CHECK_IN,
        checkOut: TEST_DATA.DATES.CHECK_OUT,
        guests: TEST_DATA.GUESTS.COUPLE,
        results: [
          {
            property: {
              id: TEST_DATA.PROPERTY_IDS.VALID,
              name: 'Test Hotel Sydney',
              location: {
                city: 'Sydney',
                state: 'NSW',
                country: 'Australia',
                coordinates: {
                  latitude: -33.8688,
                  longitude: 151.2093,
                },
                distance: {
                  value: 0.5,
                  unit: 'km',
                  description: '0.5km from city center',
                },
              },
              rating: {
                type: 'star',
                value: 4,
              },
              pricing: {
                currency: TEST_DATA.CURRENCIES.AUD,
                baseRate: 200.0,
                totalRate: 220.0,
                strikethroughRate: 250.0,
                savings: 30.0,
              },
              availability: {
                available: true,
                roomsLeft: 3,
                lastBookedMinutesAgo: 15,
              },
              amenities: ['wifi', 'pool', 'gym'],
              images: [
                {
                  url: 'https://example.com/hotel1.jpg',
                  alt: 'Hotel exterior',
                },
              ],
              offers: [
                {
                  id: 'offer-123',
                  name: 'Early Bird Special',
                  description: '10% off for early bookings',
                },
              ],
            },
          },
          {
            property: {
              id: 'property-456',
              name: 'Luxury Resort Sydney',
              location: {
                city: 'Sydney',
                state: 'NSW',
                country: 'Australia',
                coordinates: {
                  latitude: -33.865,
                  longitude: 151.2094,
                },
                distance: {
                  value: 1.2,
                  unit: 'km',
                  description: '1.2km from city center',
                },
              },
              rating: {
                type: 'star',
                value: 5,
              },
              pricing: {
                currency: TEST_DATA.CURRENCIES.AUD,
                baseRate: 450.0,
                totalRate: 495.0,
              },
              availability: {
                available: true,
                roomsLeft: 1,
                lastBookedMinutesAgo: 5,
              },
              amenities: ['wifi', 'pool', 'gym', 'spa', 'restaurant'],
              images: [
                {
                  url: 'https://example.com/resort1.jpg',
                  alt: 'Resort view',
                },
              ],
              offers: [],
            },
          },
        ],
        pagination: {
          page: 1,
          totalPages: 1,
          totalResults: 2,
          resultsPerPage: 20,
        },
        filters: {
          priceRange: {
            min: 200.0,
            max: 495.0,
            currency: TEST_DATA.CURRENCIES.AUD,
          },
          starRating: {
            available: [3, 4, 5],
            selected: [],
          },
          amenities: {
            available: ['wifi', 'pool', 'gym', 'spa', 'restaurant'],
            selected: [],
          },
          distance: {
            available: [1, 2, 5, 10],
            selected: [],
            unit: 'km',
          },
        },
      };

      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.PROPERTIES.PROPERTIES_AVAILABLE,
          uponReceiving: 'a request to search location availability',
          withRequest: {
            method: 'GET',
            path: `/locations/${TEST_DATA.LOCATIONS.SYDNEY}/availability`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              [HTTP_HEADERS.X_QH_USER_ID]: 'user-123',
            },
            query: {
              checkIn: TEST_DATA.DATES.CHECK_IN,
              checkOut: TEST_DATA.DATES.CHECK_OUT,
              adults: '2',
              children: '0',
              infants: '0',
              page: '1',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedAvailabilityResults,
          },
        });
      });

      it('should search location availability successfully', async () => {
        const result = await searchLocationAvailability({
          location: TEST_DATA.LOCATIONS.SYDNEY,
          checkIn: TEST_DATA.DATES.CHECK_IN,
          checkOut: TEST_DATA.DATES.CHECK_OUT,
          adults: 2,
          children: 0,
          infants: 0,
          accessToken: 'valid-token',
          qhUserId: 'user-123',
        });

        expect(result).toEqual(expectedAvailabilityResults);
        expect(result.location).toBe(TEST_DATA.LOCATIONS.SYDNEY);
        expect(result.results).toHaveLength(2);
        expect(result.results[0].property).toHaveProperty('id');
        expect(result.results[0].property).toHaveProperty('name');
        expect(result.results[0].property).toHaveProperty('location');
        expect(result.results[0].property).toHaveProperty('rating');
        expect(result.results[0].property).toHaveProperty('pricing');
        expect(result.results[0].property).toHaveProperty('availability');
        expect(result.results[0].property.location).toHaveProperty('coordinates');
        expect(result.results[0].property.location).toHaveProperty('distance');
        expect(result.results[0].property.pricing).toHaveProperty('currency');
        expect(result.results[0].property.pricing).toHaveProperty('totalRate');
        expect(result.results[0].property.availability).toHaveProperty('available');
        expect(result.pagination).toHaveProperty('totalResults');
        expect(result.filters).toHaveProperty('priceRange');
      });
    });

    describe('location availability search with filters', () => {
      const expectedFilteredResults = {
        location: TEST_DATA.LOCATIONS.SYDNEY,
        checkIn: TEST_DATA.DATES.CHECK_IN,
        checkOut: TEST_DATA.DATES.CHECK_OUT,
        guests: TEST_DATA.GUESTS.COUPLE,
        results: [
          {
            property: {
              id: 'property-456',
              name: 'Luxury Resort Sydney',
              location: {
                city: 'Sydney',
                state: 'NSW',
                country: 'Australia',
                coordinates: {
                  latitude: -33.865,
                  longitude: 151.2094,
                },
                distance: {
                  value: 1.2,
                  unit: 'km',
                  description: '1.2km from city center',
                },
              },
              rating: {
                type: 'star',
                value: 5,
              },
              pricing: {
                currency: TEST_DATA.CURRENCIES.AUD,
                baseRate: 450.0,
                totalRate: 495.0,
              },
              availability: {
                available: true,
                roomsLeft: 1,
                lastBookedMinutesAgo: 5,
              },
              amenities: ['wifi', 'pool', 'gym', 'spa', 'restaurant'],
              images: [
                {
                  url: 'https://example.com/resort1.jpg',
                  alt: 'Resort view',
                },
              ],
              offers: [],
            },
          },
        ],
        pagination: {
          page: 1,
          totalPages: 1,
          totalResults: 1,
          resultsPerPage: 20,
        },
        filters: {
          priceRange: {
            min: 450.0,
            max: 495.0,
            currency: TEST_DATA.CURRENCIES.AUD,
          },
          starRating: {
            available: [5],
            selected: [5],
          },
          amenities: {
            available: ['wifi', 'pool', 'gym', 'spa', 'restaurant'],
            selected: ['spa'],
          },
          distance: {
            available: [1, 2, 5, 10],
            selected: [],
            unit: 'km',
          },
        },
      };

      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.PROPERTIES.PROPERTIES_AVAILABLE,
          uponReceiving: 'a request to search location availability with filters',
          withRequest: {
            method: 'GET',
            path: `/locations/${TEST_DATA.LOCATIONS.SYDNEY}/availability`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              [HTTP_HEADERS.X_QH_USER_ID]: 'user-123',
            },
            query: {
              checkIn: TEST_DATA.DATES.CHECK_IN,
              checkOut: TEST_DATA.DATES.CHECK_OUT,
              adults: '2',
              children: '0',
              infants: '0',
              page: '1',
              starRating: '5',
              amenities: 'spa',
              sortBy: 'price',
              sortOrder: 'asc',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedFilteredResults,
          },
        });
      });

      it('should search location availability with filters successfully', async () => {
        const result = await searchLocationAvailability({
          location: TEST_DATA.LOCATIONS.SYDNEY,
          checkIn: TEST_DATA.DATES.CHECK_IN,
          checkOut: TEST_DATA.DATES.CHECK_OUT,
          adults: 2,
          children: 0,
          infants: 0,
          starRating: 5,
          amenities: 'spa',
          sortBy: 'price',
          sortOrder: 'asc',
          accessToken: 'valid-token',
          qhUserId: 'user-123',
        });

        expect(result).toEqual(expectedFilteredResults);
        expect(result.results).toHaveLength(1);
        expect(result.results[0].property.rating.value).toBe(5);
        expect(result.results[0].property.amenities).toContain('spa');
        expect(result.filters.starRating.selected).toContain(5);
        expect(result.filters.amenities.selected).toContain('spa');
      });
    });

    describe('location availability search with pagination', () => {
      const expectedPaginatedResults = {
        location: TEST_DATA.LOCATIONS.SYDNEY,
        checkIn: TEST_DATA.DATES.CHECK_IN,
        checkOut: TEST_DATA.DATES.CHECK_OUT,
        guests: TEST_DATA.GUESTS.COUPLE,
        results: [
          {
            property: {
              id: 'property-page2-1',
              name: 'Hotel Page 2 Item 1',
              location: {
                city: 'Sydney',
                state: 'NSW',
                country: 'Australia',
                coordinates: {
                  latitude: -33.87,
                  longitude: 151.21,
                },
                distance: {
                  value: 2.1,
                  unit: 'km',
                  description: '2.1km from city center',
                },
              },
              rating: {
                type: 'star',
                value: 3,
              },
              pricing: {
                currency: TEST_DATA.CURRENCIES.AUD,
                baseRate: 150.0,
                totalRate: 165.0,
              },
              availability: {
                available: true,
                roomsLeft: 5,
                lastBookedMinutesAgo: 30,
              },
              amenities: ['wifi', 'parking'],
              images: [
                {
                  url: 'https://example.com/hotel-page2.jpg',
                  alt: 'Hotel page 2',
                },
              ],
              offers: [],
            },
          },
        ],
        pagination: {
          page: 2,
          totalPages: 3,
          totalResults: 45,
          resultsPerPage: 20,
        },
        filters: {
          priceRange: {
            min: 100.0,
            max: 600.0,
            currency: TEST_DATA.CURRENCIES.AUD,
          },
          starRating: {
            available: [3, 4, 5],
            selected: [],
          },
          amenities: {
            available: ['wifi', 'pool', 'gym', 'spa', 'restaurant', 'parking'],
            selected: [],
          },
          distance: {
            available: [1, 2, 5, 10],
            selected: [],
            unit: 'km',
          },
        },
      };

      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.PROPERTIES.PROPERTIES_AVAILABLE,
          uponReceiving: 'a request to search location availability with pagination',
          withRequest: {
            method: 'GET',
            path: `/locations/${TEST_DATA.LOCATIONS.SYDNEY}/availability`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              [HTTP_HEADERS.X_QH_USER_ID]: 'user-123',
            },
            query: {
              checkIn: TEST_DATA.DATES.CHECK_IN,
              checkOut: TEST_DATA.DATES.CHECK_OUT,
              adults: '2',
              children: '0',
              infants: '0',
              page: '2',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedPaginatedResults,
          },
        });
      });

      it('should search location availability with pagination successfully', async () => {
        const result = await searchLocationAvailability({
          location: TEST_DATA.LOCATIONS.SYDNEY,
          checkIn: TEST_DATA.DATES.CHECK_IN,
          checkOut: TEST_DATA.DATES.CHECK_OUT,
          adults: 2,
          children: 0,
          infants: 0,
          page: 2,
          accessToken: 'valid-token',
          qhUserId: 'user-123',
        });

        expect(result).toEqual(expectedPaginatedResults);
        expect(result.pagination.page).toBe(2);
        expect(result.pagination.totalPages).toBe(3);
        expect(result.pagination.totalResults).toBe(45);
        expect(result.pagination.resultsPerPage).toBe(20);
      });
    });

    describe('location availability search with no results', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.PROPERTIES.PROPERTIES_AVAILABLE,
          uponReceiving: 'a request to search location availability with no results',
          withRequest: {
            method: 'GET',
            path: `/locations/${TEST_DATA.LOCATIONS.INVALID}/availability`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              [HTTP_HEADERS.X_QH_USER_ID]: 'user-123',
            },
            query: {
              checkIn: TEST_DATA.DATES.CHECK_IN,
              checkOut: TEST_DATA.DATES.CHECK_OUT,
              adults: '2',
              children: '0',
              infants: '0',
              page: '1',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              location: TEST_DATA.LOCATIONS.INVALID,
              checkIn: TEST_DATA.DATES.CHECK_IN,
              checkOut: TEST_DATA.DATES.CHECK_OUT,
              guests: TEST_DATA.GUESTS.COUPLE,
              results: [],
              pagination: {
                page: 1,
                totalPages: 0,
                totalResults: 0,
                resultsPerPage: 20,
              },
              filters: {
                priceRange: {
                  min: 0,
                  max: 0,
                  currency: TEST_DATA.CURRENCIES.AUD,
                },
                starRating: {
                  available: [],
                  selected: [],
                },
                amenities: {
                  available: [],
                  selected: [],
                },
                distance: {
                  available: [],
                  selected: [],
                  unit: 'km',
                },
              },
              message: 'No properties found for the specified location and dates',
            },
          },
        });
      });

      it('should return empty results for invalid location', async () => {
        const result = await searchLocationAvailability({
          location: TEST_DATA.LOCATIONS.INVALID,
          checkIn: TEST_DATA.DATES.CHECK_IN,
          checkOut: TEST_DATA.DATES.CHECK_OUT,
          adults: 2,
          children: 0,
          infants: 0,
          accessToken: 'valid-token',
          qhUserId: 'user-123',
        });

        expect(result.results).toEqual([]);
        expect(result.pagination.totalResults).toBe(0);
        expect(result.message).toBeDefined();
      });
    });
  });

  describe('GET /properties/{id}/related - Related Property Availability', () => {
    describe('successful related property availability search', () => {
      const expectedRelatedResults = {
        results: [
          {
            property: {
              id: 'related-property-1',
              name: 'Similar Hotel Sydney',
              location: {
                city: 'Sydney',
                state: 'NSW',
                country: 'Australia',
                coordinates: {
                  latitude: -33.87,
                  longitude: 151.21,
                },
                distance: {
                  value: 0.8,
                  unit: 'km',
                  description: '0.8km from Test Hotel Sydney',
                },
              },
              rating: {
                type: 'star',
                value: 4,
              },
              pricing: {
                currency: TEST_DATA.CURRENCIES.AUD,
                baseRate: 180.0,
                totalRate: 198.0,
              },
              availability: {
                available: true,
                roomsLeft: 2,
                lastBookedMinutesAgo: 10,
              },
              amenities: ['wifi', 'pool', 'gym'],
              images: [
                {
                  url: 'https://example.com/similar-hotel.jpg',
                  alt: 'Similar hotel',
                },
              ],
              similarity: {
                score: 0.85,
                reasons: ['Similar star rating', 'Similar amenities', 'Close proximity'],
              },
            },
          },
          {
            property: {
              id: 'related-property-2',
              name: 'Alternative Hotel Sydney',
              location: {
                city: 'Sydney',
                state: 'NSW',
                country: 'Australia',
                coordinates: {
                  latitude: -33.865,
                  longitude: 151.205,
                },
                distance: {
                  value: 1.5,
                  unit: 'km',
                  description: '1.5km from Test Hotel Sydney',
                },
              },
              rating: {
                type: 'star',
                value: 4,
              },
              pricing: {
                currency: TEST_DATA.CURRENCIES.AUD,
                baseRate: 220.0,
                totalRate: 242.0,
              },
              availability: {
                available: true,
                roomsLeft: 4,
                lastBookedMinutesAgo: 25,
              },
              amenities: ['wifi', 'pool', 'restaurant'],
              images: [
                {
                  url: 'https://example.com/alternative-hotel.jpg',
                  alt: 'Alternative hotel',
                },
              ],
              similarity: {
                score: 0.78,
                reasons: ['Similar star rating', 'Similar price range'],
              },
            },
          },
        ],
        baseProperty: {
          id: TEST_DATA.PROPERTY_IDS.VALID,
          name: 'Test Hotel Sydney',
        },
        searchCriteria: {
          checkIn: TEST_DATA.DATES.CHECK_IN,
          checkOut: TEST_DATA.DATES.CHECK_OUT,
          guests: TEST_DATA.GUESTS.COUPLE,
        },
      };

      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.PROPERTIES.PROPERTY_EXISTS,
          uponReceiving: 'a request to search related property availability',
          withRequest: {
            method: 'GET',
            path: `/properties/${TEST_DATA.PROPERTY_IDS.VALID}/related`,
            query: {
              checkIn: TEST_DATA.DATES.CHECK_IN,
              checkOut: TEST_DATA.DATES.CHECK_OUT,
              adults: '2',
              children: '0',
              infants: '0',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedRelatedResults,
          },
        });
      });

      it('should search related property availability successfully', async () => {
        const result = await searchRelatedPropertyAvailability({
          propertyId: TEST_DATA.PROPERTY_IDS.VALID,
          checkIn: TEST_DATA.DATES.CHECK_IN,
          checkOut: TEST_DATA.DATES.CHECK_OUT,
          adults: 2,
          children: 0,
          infants: 0,
        });

        expect(result).toEqual(expectedRelatedResults);
        expect(result.results).toHaveLength(2);
        expect(result.results[0].property).toHaveProperty('id');
        expect(result.results[0].property).toHaveProperty('name');
        expect(result.results[0].property).toHaveProperty('location');
        expect(result.results[0].property).toHaveProperty('rating');
        expect(result.results[0].property).toHaveProperty('pricing');
        expect(result.results[0].property).toHaveProperty('availability');
        expect(result.results[0].property).toHaveProperty('similarity');
        expect(result.results[0].property.location).toHaveProperty('distance');
        expect(result.results[0].property.similarity).toHaveProperty('score');
        expect(result.results[0].property.similarity).toHaveProperty('reasons');
        expect(result.baseProperty.id).toBe(TEST_DATA.PROPERTY_IDS.VALID);
      });
    });

    describe('related property availability search with no results', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.PROPERTIES.PROPERTY_EXISTS,
          uponReceiving: 'a request to search related property availability with no results',
          withRequest: {
            method: 'GET',
            path: `/properties/${TEST_DATA.PROPERTY_IDS.VALID}/related`,
            query: {
              checkIn: TEST_DATA.DATES.CHECK_IN,
              checkOut: TEST_DATA.DATES.CHECK_OUT,
              adults: '10',
              children: '5',
              infants: '2',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              results: [],
              baseProperty: {
                id: TEST_DATA.PROPERTY_IDS.VALID,
                name: 'Test Hotel Sydney',
              },
              searchCriteria: {
                checkIn: TEST_DATA.DATES.CHECK_IN,
                checkOut: TEST_DATA.DATES.CHECK_OUT,
                guests: {
                  adults: 10,
                  children: 5,
                  infants: 2,
                },
              },
              message: 'No related properties found for the specified criteria',
            },
          },
        });
      });

      it('should return empty results when no related properties available', async () => {
        const result = await searchRelatedPropertyAvailability({
          propertyId: TEST_DATA.PROPERTY_IDS.VALID,
          checkIn: TEST_DATA.DATES.CHECK_IN,
          checkOut: TEST_DATA.DATES.CHECK_OUT,
          adults: 10,
          children: 5,
          infants: 2,
        });

        expect(result.results).toEqual([]);
        expect(result.message).toBeDefined();
        expect(result.baseProperty.id).toBe(TEST_DATA.PROPERTY_IDS.VALID);
      });
    });

    describe('related property availability search for non-existent property', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.PROPERTIES.PROPERTY_NOT_FOUND,
          uponReceiving: 'a request to search related availability for non-existent property',
          withRequest: {
            method: 'GET',
            path: `/properties/${TEST_DATA.PROPERTY_IDS.NOT_FOUND}/related`,
            query: {
              checkIn: TEST_DATA.DATES.CHECK_IN,
              checkOut: TEST_DATA.DATES.CHECK_OUT,
              adults: '2',
              children: '0',
              infants: '0',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.NOT_FOUND,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'PROPERTY_NOT_FOUND',
                message: 'Base property not found',
                details: {
                  propertyId: TEST_DATA.PROPERTY_IDS.NOT_FOUND,
                },
              },
              timestamp: dateUtils.getCurrentDateTime(),
              requestId: dataUtils.randomUUID(),
            },
          },
        });
      });

      it('should return 404 for non-existent base property', async () => {
        await expect(
          searchRelatedPropertyAvailability({
            propertyId: TEST_DATA.PROPERTY_IDS.NOT_FOUND,
            checkIn: TEST_DATA.DATES.CHECK_IN,
            checkOut: TEST_DATA.DATES.CHECK_OUT,
            adults: 2,
            children: 0,
            infants: 0,
          }),
        ).rejects.toThrow();
      });
    });
  });
});
