/**
 * Contract tests for Geolocation Services endpoints
 * Tests the consumer contract for location search, mapping services, and geolocation
 */

const { Matchers } = require('@pact-foundation/pact');
const { createContractTestSuite, createMockServerUrlGetter, addInteractionSafely } = require('../helpers/testRunner');

const axios = require('axios');
const { HTTP_STATUS, HTTP_HEADERS, CONTENT_TYPES } = require('../shared/constants');

// Define fail function for test assertions
const fail = (message) => {
  throw new Error(message);
};

createContractTestSuite('Geolocation Services Contract Tests', 'geolocation-services', (contractTest) => {
  const getMockServerUrl = createMockServerUrlGetter(contractTest);

  describe('GET /locations - Location Search', () => {
    describe('successful location search', () => {
      const expectedLocationResponse = [
        {
          id: Matchers.somethingLike('location-1'),
          name: 'Sydney',
          displayName: 'Sydney, NSW, Australia',
          type: 'city',
          country: 'Australia',
          countryCode: 'AU',
          state: 'NSW',
          stateCode: 'NSW',
          coordinates: {
            latitude: Matchers.decimal(-33.8688),
            longitude: Matchers.decimal(151.2093),
          },
          timezone: 'Australia/Sydney',
          popularity: Matchers.integer(95),
          isActive: true,
        },
        {
          id: Matchers.somethingLike('location-2'),
          name: 'Sydney CBD',
          displayName: 'Sydney CBD, Sydney, NSW, Australia',
          type: 'district',
          country: 'Australia',
          countryCode: 'AU',
          state: 'NSW',
          stateCode: 'NSW',
          parentLocation: {
            id: Matchers.somethingLike('location-1'),
            name: 'Sydney',
          },
          coordinates: {
            latitude: Matchers.decimal(-33.8651),
            longitude: Matchers.decimal(151.2099),
          },
          timezone: 'Australia/Sydney',
          popularity: Matchers.integer(88),
          isActive: true,
        },
      ];

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: 'locations are available for search',
          uponReceiving: 'a request to search locations by name',
          withRequest: {
            method: 'GET',
            path: '/locations',
            query: {
              name: 'Sydney',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedLocationResponse,
          },
        });
      });

      it('should search locations by name successfully', async () => {
        const response = await axios({
          method: 'GET',
          url: `${getMockServerUrl()}/locations`,
          params: {
            name: 'Sydney',
          },
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              id: expect.any(String),
              name: expect.any(String),
              displayName: expect.any(String),
              type: expect.any(String),
              country: expect.any(String),
              countryCode: expect.any(String),
              coordinates: expect.objectContaining({
                latitude: expect.any(Number),
                longitude: expect.any(Number),
              }),
              timezone: expect.any(String),
              popularity: expect.any(Number),
              isActive: true,
            }),
          ]),
        );
        expect(response.data).toHaveLength(2);
        expect(response.data[0].name).toBe('Sydney');
        expect(response.data[1].name).toBe('Sydney CBD');
      });
    });

    describe('location search with partial match', () => {
      const expectedPartialMatchResponse = [
        {
          id: Matchers.somethingLike('location-3'),
          name: 'Melbourne',
          displayName: 'Melbourne, VIC, Australia',
          type: 'city',
          country: 'Australia',
          countryCode: 'AU',
          state: 'Victoria',
          stateCode: 'VIC',
          coordinates: {
            latitude: Matchers.decimal(-37.8136),
            longitude: Matchers.decimal(144.9631),
          },
          timezone: 'Australia/Melbourne',
          popularity: Matchers.integer(92),
          isActive: true,
        },
      ];

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: 'locations are available for search',
          uponReceiving: 'a request to search locations with partial name match',
          withRequest: {
            method: 'GET',
            path: '/locations',
            query: {
              name: 'Melb',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedPartialMatchResponse,
          },
        });
      });

      it('should return locations matching partial name', async () => {
        const response = await axios({
          method: 'GET',
          url: `${getMockServerUrl()}/locations`,
          params: {
            name: 'Melb',
          },
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toHaveLength(1);
        expect(response.data[0]).toMatchObject({
          name: 'Melbourne',
          displayName: 'Melbourne, VIC, Australia',
          type: 'city',
          country: 'Australia',
        });
      });
    });

    describe('no locations found', () => {
      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: 'no locations match search criteria',
          uponReceiving: 'a request to search locations with no matches',
          withRequest: {
            method: 'GET',
            path: '/locations',
            query: {
              name: 'NonExistentLocation',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: [],
          },
        });
      });

      it('should return empty array when no locations match', async () => {
        const response = await axios({
          method: 'GET',
          url: `${getMockServerUrl()}/locations`,
          params: {
            name: 'NonExistentLocation',
          },
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toEqual([]);
      });
    });

    describe('invalid search parameters', () => {
      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: 'invalid search parameters provided',
          uponReceiving: 'a request to search locations with invalid parameters',
          withRequest: {
            method: 'GET',
            path: '/locations',
            query: {
              name: '',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.BAD_REQUEST,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'INVALID_SEARCH_PARAMETERS',
                message: 'Search name parameter cannot be empty',
                details: {
                  parameter: 'name',
                  value: '',
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 400 for invalid search parameters', async () => {
        try {
          await axios({
            method: 'GET',
            url: `${getMockServerUrl()}/locations`,
            params: {
              name: '',
            },
          });
          fail('Expected request to fail with 400');
        } catch (error) {
          expect(error.response?.status).toBe(HTTP_STATUS.BAD_REQUEST);
          if (error.response) {
            expect(error.response.data).toMatchObject({
              error: {
                code: 'INVALID_SEARCH_PARAMETERS',
                message: 'Search name parameter cannot be empty',
                details: {
                  parameter: 'name',
                  value: '',
                },
              },
            });
          }
        }
      });
    });
  });

  describe('GET /geolocation - User Geolocation Detection', () => {
    describe('successful geolocation detection', () => {
      const expectedGeolocationResponse = {
        geolocation: 'NSW',
        country: 'Australia',
        countryCode: 'AU',
        state: 'New South Wales',
        stateCode: 'NSW',
        city: 'Sydney',
        coordinates: {
          latitude: Matchers.decimal(-33.8688),
          longitude: Matchers.decimal(151.2093),
        },
        timezone: 'Australia/Sydney',
        accuracy: Matchers.somethingLike('city'),
        ipAddress: Matchers.somethingLike('*************'),
        detectionMethod: 'ip_geolocation',
        confidence: Matchers.decimal(0.95),
      };

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: 'user geolocation can be detected',
          uponReceiving: 'a request to detect user geolocation',
          withRequest: {
            method: 'GET',
            path: '/geolocation',
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedGeolocationResponse,
          },
        });
      });

      it('should detect user geolocation successfully', async () => {
        const response = await axios({
          method: 'GET',
          url: `${getMockServerUrl()}/geolocation`,
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toMatchObject({
          geolocation: expect.any(String),
          country: expect.any(String),
          countryCode: expect.any(String),
          state: expect.any(String),
          stateCode: expect.any(String),
          city: expect.any(String),
          coordinates: expect.objectContaining({
            latitude: expect.any(Number),
            longitude: expect.any(Number),
          }),
          timezone: expect.any(String),
          accuracy: expect.any(String),
          detectionMethod: expect.any(String),
          confidence: expect.any(Number),
        });
        expect(response.data.geolocation).toBe('NSW');
        expect(response.data.country).toBe('Australia');
      });
    });

    describe('geolocation detection with low accuracy', () => {
      const expectedLowAccuracyResponse = {
        geolocation: 'Australia',
        country: 'Australia',
        countryCode: 'AU',
        state: null,
        stateCode: null,
        city: null,
        coordinates: {
          latitude: Matchers.decimal(-25.2744),
          longitude: Matchers.decimal(133.7751),
        },
        timezone: 'Australia/Sydney',
        accuracy: 'country',
        ipAddress: Matchers.somethingLike('*************'),
        detectionMethod: 'ip_geolocation',
        confidence: Matchers.decimal(0.65),
      };

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: 'user geolocation has low accuracy',
          uponReceiving: 'a request to detect user geolocation with low accuracy',
          withRequest: {
            method: 'GET',
            path: '/geolocation',
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedLowAccuracyResponse,
          },
        });
      });

      it('should return low accuracy geolocation data', async () => {
        const response = await axios({
          method: 'GET',
          url: `${getMockServerUrl()}/geolocation`,
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toMatchObject({
          geolocation: 'Australia',
          country: 'Australia',
          countryCode: 'AU',
          state: null,
          stateCode: null,
          city: null,
          accuracy: 'country',
          confidence: expect.any(Number),
        });
        expect(response.data.confidence).toBeLessThan(0.8);
      });
    });

    describe('geolocation detection failure', () => {
      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: 'user geolocation cannot be detected',
          uponReceiving: 'a request to detect user geolocation when detection fails',
          withRequest: {
            method: 'GET',
            path: '/geolocation',
          },
          willRespondWith: {
            status: HTTP_STATUS.SERVICE_UNAVAILABLE,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'GEOLOCATION_UNAVAILABLE',
                message: 'Unable to determine user location',
                details: {
                  reason: 'ip_geolocation_service_unavailable',
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 503 when geolocation detection fails', async () => {
        try {
          await axios({
            method: 'GET',
            url: `${getMockServerUrl()}/geolocation`,
          });
          fail('Expected request to fail with 503');
        } catch (error) {
          expect(error.response?.status).toBe(HTTP_STATUS.SERVICE_UNAVAILABLE);
          if (error.response) {
            expect(error.response.data).toMatchObject({
              error: {
                code: 'GEOLOCATION_UNAVAILABLE',
                message: 'Unable to determine user location',
                details: {
                  reason: 'ip_geolocation_service_unavailable',
                },
              },
            });
          }
        }
      });
    });
  });

  describe('GET /locations/{id}/boundaries - Location Boundaries', () => {
    describe('successful location boundaries retrieval', () => {
      const locationId = 'location-sydney-1';
      const expectedBoundariesResponse = {
        location: {
          id: locationId,
          name: 'Sydney',
          displayName: 'Sydney, NSW, Australia',
        },
        boundaries: {
          type: 'Polygon',
          coordinates: [
            [
              [Matchers.decimal(151.0), Matchers.decimal(-34.0)],
              [Matchers.decimal(151.5), Matchers.decimal(-34.0)],
              [Matchers.decimal(151.5), Matchers.decimal(-33.5)],
              [Matchers.decimal(151.0), Matchers.decimal(-33.5)],
              [Matchers.decimal(151.0), Matchers.decimal(-34.0)],
            ],
          ],
        },
        center: {
          latitude: Matchers.decimal(-33.8688),
          longitude: Matchers.decimal(151.2093),
        },
        radius: Matchers.decimal(25.5),
        area: Matchers.decimal(2058.7),
        accuracy: 'high',
      };

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: 'location boundaries are available',
          uponReceiving: 'a request to fetch location boundaries',
          withRequest: {
            method: 'GET',
            path: `/locations/${locationId}/boundaries`,
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedBoundariesResponse,
          },
        });
      });

      it('should fetch location boundaries successfully', async () => {
        const response = await axios({
          method: 'GET',
          url: `${getMockServerUrl()}/locations/${locationId}/boundaries`,
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toMatchObject({
          location: {
            id: locationId,
            name: 'Sydney',
            displayName: 'Sydney, NSW, Australia',
          },
          boundaries: {
            type: 'Polygon',
            coordinates: expect.any(Array),
          },
          center: {
            latitude: expect.any(Number),
            longitude: expect.any(Number),
          },
          radius: expect.any(Number),
          area: expect.any(Number),
          accuracy: expect.any(String),
        });
        expect(response.data.boundaries.coordinates[0]).toHaveLength(5); // Polygon should be closed
      });
    });

    describe('location boundaries not found', () => {
      const nonExistentLocationId = 'location-nonexistent';

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: 'location boundaries do not exist',
          uponReceiving: 'a request to fetch boundaries for non-existent location',
          withRequest: {
            method: 'GET',
            path: `/locations/${nonExistentLocationId}/boundaries`,
          },
          willRespondWith: {
            status: HTTP_STATUS.NOT_FOUND,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'LOCATION_NOT_FOUND',
                message: 'Location not found',
                details: {
                  locationId: nonExistentLocationId,
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 404 for non-existent location boundaries', async () => {
        try {
          await axios({
            method: 'GET',
            url: `${getMockServerUrl()}/locations/${nonExistentLocationId}/boundaries`,
          });
          fail('Expected request to fail with 404');
        } catch (error) {
          expect(error.response?.status).toBe(HTTP_STATUS.NOT_FOUND);
          if (error.response) {
            expect(error.response.data).toMatchObject({
              error: {
                code: 'LOCATION_NOT_FOUND',
                message: 'Location not found',
                details: {
                  locationId: nonExistentLocationId,
                },
              },
            });
          }
        }
      });
    });
  });
});
