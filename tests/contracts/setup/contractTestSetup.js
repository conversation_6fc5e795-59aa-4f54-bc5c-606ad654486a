/* eslint-disable no-console */

/**
 * Contract test setup file
 * This file configures the test environment for Pact contract tests
 */

// Load environment variables for testing
// Extend Jest matchers for better assertions
import '@testing-library/jest-dom/jest-globals';

require('dotenv').config({
  path: './.env.test',
});

// Set up process-level error suppression for tracing subscriber errors
// This needs to be done early, before any Pact instances are created
const originalStderrWrite = process.stderr.write;
process.stderr.write = function (chunk, encoding, callback) {
  const message = chunk.toString();
  if (
    message.includes('Failed to initialise global tracing subscriber') ||
    message.includes('a global default trace dispatcher has already been set') ||
    message.includes('tracing subscriber') ||
    message.includes('trace dispatcher')
  ) {
    // Suppress these specific error messages
    if (typeof callback === 'function') {
      callback();
    }
    return true;
  }
  return originalStderrWrite.call(this, chunk, encoding, callback);
};

// Import test registry for cleanup
const { TestRegistry } = require('../helpers/testRunner');

// Global test timeout for contract tests (Pact operations can be slow)
jest.setTimeout(60000);

// Suppress tracing subscriber warnings that occur when running multiple tests
const originalWarn = console.warn;
const originalError = console.error;

// Suppress specific Pact-related warnings and errors that are expected during parallel test execution
console.warn = (...args) => {
  const message = args.join(' ');
  if (
    message.includes('Failed to initialise global tracing subscriber') ||
    message.includes('a global default trace dispatcher has already been set') ||
    message.includes('tracing subscriber') ||
    message.includes('trace dispatcher')
  ) {
    // Suppress these specific warnings as they're expected when running multiple Pact tests in parallel
    return;
  }
  originalWarn.apply(console, args);
};

console.error = (...args) => {
  const message = args.join(' ');
  if (
    message.includes('Failed to initialise global tracing subscriber') ||
    message.includes('a global default trace dispatcher has already been set') ||
    message.includes('tracing subscriber') ||
    message.includes('trace dispatcher')
  ) {
    // Suppress these specific errors as they're expected when running multiple Pact tests in parallel
    return;
  }
  originalError.apply(console, args);
};

// Mock console methods to reduce noise in test output
global.console = {
  ...console,
  // Suppress info logs during tests
  info: jest.fn(),
  // Keep error and warn for debugging (but with our custom handlers)
  error: console.error,
  warn: console.warn,
  log: console.log,
};

// Global cleanup function for Pact providers
global.afterEach(() => {
  // Reset any global state between tests
  jest.clearAllMocks();
});

// Ensure clean exit for Pact mock servers
process.on('exit', async () => {
  try {
    await TestRegistry.cleanupAll();
  } catch (error) {
    console.error('Error during final cleanup:', error);
  }
});

// Handle process termination signals
process.on('SIGINT', async () => {
  try {
    await TestRegistry.cleanupAll();
    process.exit(0);
  } catch (error) {
    console.error('Error during SIGINT cleanup:', error);
    process.exit(1);
  }
});

process.on('SIGTERM', async () => {
  try {
    await TestRegistry.cleanupAll();
    process.exit(0);
  } catch (error) {
    console.error('Error during SIGTERM cleanup:', error);
    process.exit(1);
  }
});

// Handle uncaught exceptions and unhandled rejections
process.on('uncaughtException', async (error) => {
  console.error('Uncaught Exception:', error);
  try {
    await TestRegistry.cleanupAll();
  } catch (cleanupError) {
    console.error('Error during exception cleanup:', cleanupError);
  }
  process.exit(1);
});

process.on('unhandledRejection', async (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  try {
    await TestRegistry.cleanupAll();
  } catch (cleanupError) {
    console.error('Error during rejection cleanup:', cleanupError);
  }
  process.exit(1);
});
