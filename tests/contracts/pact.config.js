/**
 * Pact configuration for contract testing
 * This file contains the base configuration for Pact consumer tests
 */

const path = require('path');

const PACT_CONFIG = {
  // Consumer name - identifies this application in Pact contracts
  consumer: 'qantas-hotels-ui',

  // Directory where Pact contracts will be written
  dir: path.resolve(process.cwd(), 'tests/contracts/pacts'),

  // Log level for Pact operations
  logLevel: process.env.NODE_ENV === 'test' ? 'ERROR' : 'INFO',

  // Specification version
  spec: 2,

  // CORS settings for mock server
  cors: true,

  // Host and port for mock server (will be dynamically assigned)
  host: '127.0.0.1',

  // Providers configuration
  providers: {
    'hotels-api': {
      name: 'hotels-api',
      baseUrl: process.env.NEXT_PUBLIC_HOTELS_API_HOST || 'https://api.hotels.qantas.com',
    },
    'auth-api': {
      name: 'auth-api',
      baseUrl: process.env.AUTH_API_URL || 'https://auth.qantas.com',
    },
    'sanity-cms': {
      name: 'sanity-cms',
      baseUrl: process.env.SANITY_PROJECT_URL || 'https://api.sanity.io',
    },
    'payment-services': {
      name: 'payment-services',
      baseUrl: process.env.PAYMENT_API_URL || 'https://payments.qantas.com',
    },
  },
};

module.exports = PACT_CONFIG;
