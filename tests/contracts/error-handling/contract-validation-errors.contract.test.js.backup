/**
 * Contract validation error tests
 * Tests for schema mismatches, missing required fields, data type validation, and status code mismatches
 */

const { Pact, Matchers } = require('@pact-foundation/pact');
const path = require('path');
const axios = require('axios');
const { API_PATHS, HTTP_STATUS, HTTP_HEADERS, CONTENT_TYPES, TEST_DATA, PROVIDER_STATES } = require('../shared/constants');

describe('Contract Validation Error Tests', () => {
  let provider;
  const mockServerBaseUrl = 'http://localhost:1236';

  beforeAll(() => {
    provider = new Pact({
      consumer: 'qantas-hotels-ui',
      provider: 'hotels-api',
      port: 1236,
      log: path.resolve(process.cwd(), 'logs', 'pact-validation-errors.log'),
      dir: path.resolve(process.cwd(), 'pacts'),
      logLevel: 'INFO',
    });

    return provider.setup();
  });

  afterAll(() => {
    return provider.finalize();
  });

  afterEach(() => {
    return provider.verify();
  });

  describe('Schema Mismatch Detection Tests', () => {
    describe('Response schema validation', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.BOOKINGS.BOOKING_EXISTS,
          uponReceiving: 'a request expecting booking response with correct schema',
          withRequest: {
            method: 'GET',
            path: `/bookings/${TEST_DATA.BOOKING_IDS.VALID}`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              booking: {
                id: Matchers.somethingLike(TEST_DATA.BOOKING_IDS.VALID),
                status: Matchers.term({
                  matcher: '^(confirmed|pending|cancelled)$',
                  generate: 'confirmed',
                }),
                propertyId: Matchers.somethingLike(TEST_DATA.PROPERTY_IDS.VALID),
                propertyName: Matchers.somethingLike('Test Hotel Sydney'),
                checkIn: Matchers.iso8601Date(),
                checkOut: Matchers.iso8601Date(),
                guests: {
                  adults: Matchers.integer(2),
                  children: Matchers.integer(0),
                  infants: Matchers.integer(0),
                },
                totalAmount: {
                  currency: Matchers.term({
                    matcher: '^[A-Z]{3}$',
                    generate: 'AUD',
                  }),
                  amount: Matchers.decimal(450.0),
                },
                confirmationNumber: Matchers.somethingLike('QH123456789'),
                createdAt: Matchers.iso8601DateTime(),
                updatedAt: Matchers.iso8601DateTime(),
              },
            },
          },
        });
      });

      it('should validate booking response schema structure', async () => {
        const response = await axios({
          method: 'GET',
          url: `${mockServerBaseUrl}/bookings/${TEST_DATA.BOOKING_IDS.VALID}`,
          headers: {
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
        });

        expect(response.status).toBe(HTTP_STATUS.OK);

        // Validate response structure matches expected schema
        expect(response.data).toHaveProperty('booking');
        expect(response.data.booking).toHaveProperty('id');
        expect(response.data.booking).toHaveProperty('status');
        expect(response.data.booking).toHaveProperty('propertyId');
        expect(response.data.booking).toHaveProperty('propertyName');
        expect(response.data.booking).toHaveProperty('checkIn');
        expect(response.data.booking).toHaveProperty('checkOut');
        expect(response.data.booking).toHaveProperty('guests');
        expect(response.data.booking).toHaveProperty('totalAmount');
        expect(response.data.booking).toHaveProperty('confirmationNumber');
        expect(response.data.booking).toHaveProperty('createdAt');
        expect(response.data.booking).toHaveProperty('updatedAt');

        // Validate nested object structure
        expect(response.data.booking.guests).toHaveProperty('adults');
        expect(response.data.booking.guests).toHaveProperty('children');
        expect(response.data.booking.guests).toHaveProperty('infants');
        expect(response.data.booking.totalAmount).toHaveProperty('currency');
        expect(response.data.booking.totalAmount).toHaveProperty('amount');

        // Validate data types
        expect(typeof response.data.booking.id).toBe('string');
        expect(typeof response.data.booking.status).toBe('string');
        expect(typeof response.data.booking.propertyId).toBe('string');
        expect(typeof response.data.booking.propertyName).toBe('string');
        expect(typeof response.data.booking.checkIn).toBe('string');
        expect(typeof response.data.booking.checkOut).toBe('string');
        expect(typeof response.data.booking.guests.adults).toBe('number');
        expect(typeof response.data.booking.guests.children).toBe('number');
        expect(typeof response.data.booking.guests.infants).toBe('number');
        expect(typeof response.data.booking.totalAmount.currency).toBe('string');
        expect(typeof response.data.booking.totalAmount.amount).toBe('number');
        expect(typeof response.data.booking.confirmationNumber).toBe('string');
        expect(typeof response.data.booking.createdAt).toBe('string');
        expect(typeof response.data.booking.updatedAt).toBe('string');

        // Validate specific format constraints
        expect(response.data.booking.status).toMatch(/^(confirmed|pending|cancelled)$/);
        expect(response.data.booking.totalAmount.currency).toMatch(/^[A-Z]{3}$/);
        expect(response.data.booking.checkIn).toMatch(/^\d{4}-\d{2}-\d{2}$/);
        expect(response.data.booking.checkOut).toMatch(/^\d{4}-\d{2}-\d{2}$/);
        expect(response.data.booking.createdAt).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/);
        expect(response.data.booking.updatedAt).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/);
      });
    });

    describe('Property search response schema validation', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.PROPERTIES.PROPERTIES_AVAILABLE,
          uponReceiving: 'a request for property search with expected schema',
          withRequest: {
            method: 'GET',
            path: API_PATHS.HOTELS.PROPERTIES,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
            query: {
              location: TEST_DATA.LOCATIONS.SYDNEY,
              checkIn: TEST_DATA.DATES.CHECK_IN,
              checkOut: TEST_DATA.DATES.CHECK_OUT,
              adults: '2',
              children: '0',
              infants: '0',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              properties: Matchers.eachLike(
                {
                  id: Matchers.somethingLike(TEST_DATA.PROPERTY_IDS.VALID),
                  name: Matchers.somethingLike('Test Hotel Sydney'),
                  location: {
                    city: Matchers.somethingLike('Sydney'),
                    country: Matchers.somethingLike('Australia'),
                    coordinates: {
                      latitude: Matchers.decimal(-33.8688),
                      longitude: Matchers.decimal(151.2093),
                    },
                  },
                  rating: {
                    type: Matchers.term({
                      matcher: '^(star|circle)$',
                      generate: 'star',
                    }),
                    value: Matchers.integer(4),
                  },
                  pricing: {
                    currency: Matchers.term({
                      matcher: '^[A-Z]{3}$',
                      generate: 'AUD',
                    }),
                    baseRate: Matchers.decimal(200.0),
                    totalRate: Matchers.decimal(450.0),
                  },
                  availability: {
                    available: Matchers.boolean(true),
                    roomsLeft: Matchers.integer(5),
                  },
                },
                { min: 1 },
              ),
              pagination: {
                page: Matchers.integer(1),
                totalPages: Matchers.integer(10),
                totalResults: Matchers.integer(95),
                limit: Matchers.integer(10),
              },
            },
          },
        });
      });

      it('should validate property search response schema', async () => {
        const response = await axios({
          method: 'GET',
          url: `${mockServerBaseUrl}${API_PATHS.HOTELS.PROPERTIES}`,
          headers: {
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          params: {
            location: TEST_DATA.LOCATIONS.SYDNEY,
            checkIn: TEST_DATA.DATES.CHECK_IN,
            checkOut: TEST_DATA.DATES.CHECK_OUT,
            adults: '2',
            children: '0',
            infants: '0',
          },
        });

        expect(response.status).toBe(HTTP_STATUS.OK);

        // Validate top-level structure
        expect(response.data).toHaveProperty('properties');
        expect(response.data).toHaveProperty('pagination');
        expect(Array.isArray(response.data.properties)).toBe(true);
        expect(response.data.properties.length).toBeGreaterThan(0);

        // Validate property structure
        const property = response.data.properties[0];
        expect(property).toHaveProperty('id');
        expect(property).toHaveProperty('name');
        expect(property).toHaveProperty('location');
        expect(property).toHaveProperty('rating');
        expect(property).toHaveProperty('pricing');
        expect(property).toHaveProperty('availability');

        // Validate nested structures
        expect(property.location).toHaveProperty('city');
        expect(property.location).toHaveProperty('country');
        expect(property.location).toHaveProperty('coordinates');
        expect(property.location.coordinates).toHaveProperty('latitude');
        expect(property.location.coordinates).toHaveProperty('longitude');

        expect(property.rating).toHaveProperty('type');
        expect(property.rating).toHaveProperty('value');

        expect(property.pricing).toHaveProperty('currency');
        expect(property.pricing).toHaveProperty('baseRate');
        expect(property.pricing).toHaveProperty('totalRate');

        expect(property.availability).toHaveProperty('available');
        expect(property.availability).toHaveProperty('roomsLeft');

        // Validate pagination structure
        expect(response.data.pagination).toHaveProperty('page');
        expect(response.data.pagination).toHaveProperty('totalPages');
        expect(response.data.pagination).toHaveProperty('totalResults');
        expect(response.data.pagination).toHaveProperty('limit');

        // Validate data types
        expect(typeof property.id).toBe('string');
        expect(typeof property.name).toBe('string');
        expect(typeof property.location.city).toBe('string');
        expect(typeof property.location.country).toBe('string');
        expect(typeof property.location.coordinates.latitude).toBe('number');
        expect(typeof property.location.coordinates.longitude).toBe('number');
        expect(typeof property.rating.type).toBe('string');
        expect(typeof property.rating.value).toBe('number');
        expect(typeof property.pricing.currency).toBe('string');
        expect(typeof property.pricing.baseRate).toBe('number');
        expect(typeof property.pricing.totalRate).toBe('number');
        expect(typeof property.availability.available).toBe('boolean');
        expect(typeof property.availability.roomsLeft).toBe('number');

        // Validate format constraints
        expect(property.rating.type).toMatch(/^(star|circle)$/);
        expect(property.pricing.currency).toMatch(/^[A-Z]{3}$/);
      });
    });
  });

  describe('Missing Required Fields Validation Tests', () => {
    describe('Booking creation with missing required fields', () => {
      const incompleteBookingPayload = {
        // Missing propertyId (required field)
        checkIn: TEST_DATA.DATES.CHECK_IN,
        checkOut: TEST_DATA.DATES.CHECK_OUT,
        adults: 2,
        // Missing children and infants fields
      };

      beforeEach(() => {
        return provider.addInteraction({
          state: 'request has missing required fields',
          uponReceiving: 'a booking request with missing required fields',
          withRequest: {
            method: 'POST',
            path: API_PATHS.HOTELS.BOOKINGS,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
            body: incompleteBookingPayload,
          },
          willRespondWith: {
            status: HTTP_STATUS.BAD_REQUEST,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'MISSING_REQUIRED_FIELDS',
                message: 'Request is missing required fields',
                details: {
                  missingFields: Matchers.eachLike('propertyId', { min: 1 }),
                  providedFields: Matchers.eachLike('checkIn', { min: 1 }),
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should detect and report missing required fields', async () => {
        const response = await axios({
          method: 'POST',
          url: `${mockServerBaseUrl}${API_PATHS.HOTELS.BOOKINGS}`,
          headers: {
            [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          data: incompleteBookingPayload,
          validateStatus: () => true, // Don't throw on error status codes
        });

        expect(response.status).toBe(HTTP_STATUS.BAD_REQUEST);
        expect(response.data).toMatchObject({
          error: {
            code: 'MISSING_REQUIRED_FIELDS',
            message: 'Request is missing required fields',
            details: {
              missingFields: expect.arrayContaining([expect.any(String)]),
              providedFields: expect.arrayContaining([expect.any(String)]),
            },
          },
        });
        expect(response.data.timestamp).toBeDefined();
        expect(response.data.requestId).toBeDefined();
      });
    });

    describe('Quote request with missing guest information', () => {
      const incompleteQuotePayload = {
        propertyId: TEST_DATA.PROPERTY_IDS.VALID,
        checkIn: TEST_DATA.DATES.CHECK_IN,
        checkOut: TEST_DATA.DATES.CHECK_OUT,
        // Missing adults, children, infants (required for quote calculation)
      };

      beforeEach(() => {
        return provider.addInteraction({
          state: 'quote request missing guest information',
          uponReceiving: 'a quote request without guest details',
          withRequest: {
            method: 'POST',
            path: API_PATHS.HOTELS.QUOTES,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
            body: incompleteQuotePayload,
          },
          willRespondWith: {
            status: HTTP_STATUS.BAD_REQUEST,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'MISSING_GUEST_INFORMATION',
                message: 'Guest information is required for quote calculation',
                details: {
                  requiredFields: ['adults', 'children', 'infants'],
                  missingFields: ['adults', 'children', 'infants'],
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should require guest information for quote requests', async () => {
        const response = await axios({
          method: 'POST',
          url: `${mockServerBaseUrl}${API_PATHS.HOTELS.QUOTES}`,
          headers: {
            [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          data: incompleteQuotePayload,
          validateStatus: () => true, // Don't throw on error status codes
        });

        expect(response.status).toBe(HTTP_STATUS.BAD_REQUEST);
        expect(response.data).toMatchObject({
          error: {
            code: 'MISSING_GUEST_INFORMATION',
            message: 'Guest information is required for quote calculation',
            details: {
              requiredFields: expect.arrayContaining(['adults', 'children', 'infants']),
              missingFields: expect.arrayContaining(['adults', 'children', 'infants']),
            },
          },
        });
        expect(response.data.timestamp).toBeDefined();
        expect(response.data.requestId).toBeDefined();
      });
    });
  });

  describe('Data Type Validation Tests', () => {
    describe('Invalid data types in booking request', () => {
      const invalidTypeBookingPayload = {
        propertyId: TEST_DATA.PROPERTY_IDS.VALID,
        checkIn: TEST_DATA.DATES.CHECK_IN,
        checkOut: TEST_DATA.DATES.CHECK_OUT,
        adults: 'two', // Should be number, not string
        children: true, // Should be number, not boolean
        infants: [], // Should be number, not array
        totalAmount: 'expensive', // Should be number, not string
      };

      beforeEach(() => {
        return provider.addInteraction({
          state: 'request has invalid data types',
          uponReceiving: 'a booking request with invalid data types',
          withRequest: {
            method: 'POST',
            path: API_PATHS.HOTELS.BOOKINGS,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
            body: invalidTypeBookingPayload,
          },
          willRespondWith: {
            status: HTTP_STATUS.BAD_REQUEST,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'INVALID_DATA_TYPES',
                message: 'Request contains fields with invalid data types',
                details: {
                  typeErrors: Matchers.eachLike(
                    {
                      field: Matchers.somethingLike('adults'),
                      expectedType: Matchers.somethingLike('number'),
                      actualType: Matchers.somethingLike('string'),
                      actualValue: Matchers.somethingLike('two'),
                    },
                    { min: 1 },
                  ),
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should validate data types and report type errors', async () => {
        const response = await axios({
          method: 'POST',
          url: `${mockServerBaseUrl}${API_PATHS.HOTELS.BOOKINGS}`,
          headers: {
            [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          data: invalidTypeBookingPayload,
          validateStatus: () => true, // Don't throw on error status codes
        });

        expect(response.status).toBe(HTTP_STATUS.BAD_REQUEST);
        expect(response.data).toMatchObject({
          error: {
            code: 'INVALID_DATA_TYPES',
            message: 'Request contains fields with invalid data types',
            details: {
              typeErrors: expect.arrayContaining([
                expect.objectContaining({
                  field: expect.any(String),
                  expectedType: expect.any(String),
                  actualType: expect.any(String),
                  actualValue: expect.anything(),
                }),
              ]),
            },
          },
        });
        expect(response.data.timestamp).toBeDefined();
        expect(response.data.requestId).toBeDefined();
      });
    });

    describe('Invalid date format validation', () => {
      const invalidDatePayload = {
        propertyId: TEST_DATA.PROPERTY_IDS.VALID,
        checkIn: '2024/12/01', // Invalid format, should be YYYY-MM-DD
        checkOut: '01-12-2024', // Invalid format, should be YYYY-MM-DD
        adults: 2,
        children: 0,
        infants: 0,
      };

      beforeEach(() => {
        return provider.addInteraction({
          state: 'request has invalid date formats',
          uponReceiving: 'a booking request with invalid date formats',
          withRequest: {
            method: 'POST',
            path: API_PATHS.HOTELS.BOOKINGS,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
            body: invalidDatePayload,
          },
          willRespondWith: {
            status: HTTP_STATUS.BAD_REQUEST,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'INVALID_DATE_FORMAT',
                message: 'Date fields must be in YYYY-MM-DD format',
                details: {
                  invalidDates: Matchers.eachLike(
                    {
                      field: Matchers.somethingLike('checkIn'),
                      value: Matchers.somethingLike('2024/12/01'),
                      expectedFormat: 'YYYY-MM-DD',
                    },
                    { min: 1 },
                  ),
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should validate date formats and report format errors', async () => {
        const response = await axios({
          method: 'POST',
          url: `${mockServerBaseUrl}${API_PATHS.HOTELS.BOOKINGS}`,
          headers: {
            [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          data: invalidDatePayload,
          validateStatus: () => true, // Don't throw on error status codes
        });

        expect(response.status).toBe(HTTP_STATUS.BAD_REQUEST);
        expect(response.data).toMatchObject({
          error: {
            code: 'INVALID_DATE_FORMAT',
            message: 'Date fields must be in YYYY-MM-DD format',
            details: {
              invalidDates: expect.arrayContaining([
                expect.objectContaining({
                  field: expect.any(String),
                  value: expect.any(String),
                  expectedFormat: 'YYYY-MM-DD',
                }),
              ]),
            },
          },
        });
        expect(response.data.timestamp).toBeDefined();
        expect(response.data.requestId).toBeDefined();
      });
    });
  });

  describe('Status Code Mismatch Detection Tests', () => {
    describe('Unexpected success status code', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.BOOKINGS.BOOKING_EXISTS,
          uponReceiving: 'a request expecting 200 OK status',
          withRequest: {
            method: 'GET',
            path: `/bookings/${TEST_DATA.BOOKING_IDS.VALID}`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK, // Expected status code
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              booking: {
                id: Matchers.somethingLike(TEST_DATA.BOOKING_IDS.VALID),
                status: 'confirmed',
                propertyId: TEST_DATA.PROPERTY_IDS.VALID,
                checkIn: TEST_DATA.DATES.CHECK_IN,
                checkOut: TEST_DATA.DATES.CHECK_OUT,
                guests: TEST_DATA.GUESTS.COUPLE,
                totalAmount: {
                  currency: TEST_DATA.CURRENCIES.AUD,
                  amount: Matchers.decimal(450.0),
                },
                confirmationNumber: Matchers.somethingLike('QH123456789'),
                createdAt: Matchers.iso8601DateTime(),
              },
            },
          },
        });
      });

      it('should receive expected 200 OK status code', async () => {
        const response = await axios({
          method: 'GET',
          url: `${mockServerBaseUrl}/bookings/${TEST_DATA.BOOKING_IDS.VALID}`,
          headers: {
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
        });

        // Verify we receive the expected status code
        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.status).not.toBe(HTTP_STATUS.CREATED);
        expect(response.status).not.toBe(HTTP_STATUS.ACCEPTED);

        // Verify response contains expected data
        expect(response.data).toHaveProperty('booking');
        expect(response.data.booking.id).toBe(TEST_DATA.BOOKING_IDS.VALID);
      });
    });

    describe('Unexpected error status code validation', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: 'service returns unexpected error status',
          uponReceiving: 'a request that should succeed but returns error',
          withRequest: {
            method: 'GET',
            path: `/bookings/${TEST_DATA.BOOKING_IDS.VALID}`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.SERVICE_UNAVAILABLE, // Unexpected error status
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'SERVICE_UNAVAILABLE',
                message: 'Service is temporarily unavailable',
                details: {
                  expectedStatus: HTTP_STATUS.OK,
                  actualStatus: HTTP_STATUS.SERVICE_UNAVAILABLE,
                  retryAfter: Matchers.integer(60),
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should handle unexpected error status codes', async () => {
        const response = await axios({
          method: 'GET',
          url: `${mockServerBaseUrl}/bookings/${TEST_DATA.BOOKING_IDS.VALID}`,
          headers: {
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          validateStatus: () => true, // Don't throw on error status codes
        });

        expect(response.status).toBe(HTTP_STATUS.SERVICE_UNAVAILABLE);
        expect(response.status).not.toBe(HTTP_STATUS.OK);
        expect(response.status).not.toBe(HTTP_STATUS.NOT_FOUND);

        expect(response.data).toMatchObject({
          error: {
            code: 'SERVICE_UNAVAILABLE',
            message: 'Service is temporarily unavailable',
            details: {
              expectedStatus: HTTP_STATUS.OK,
              actualStatus: HTTP_STATUS.SERVICE_UNAVAILABLE,
              retryAfter: expect.any(Number),
            },
          },
        });
        expect(response.data.timestamp).toBeDefined();
        expect(response.data.requestId).toBeDefined();
      });
    });

    describe('Status code consistency validation', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.BOOKINGS.BOOKING_CAN_BE_CREATED,
          uponReceiving: 'a booking creation request expecting 201 Created',
          withRequest: {
            method: 'POST',
            path: API_PATHS.HOTELS.BOOKINGS,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
            body: {
              propertyId: TEST_DATA.PROPERTY_IDS.VALID,
              checkIn: TEST_DATA.DATES.CHECK_IN,
              checkOut: TEST_DATA.DATES.CHECK_OUT,
              adults: 2,
              children: 0,
              infants: 0,
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.CREATED, // Correct status for resource creation
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              booking: {
                id: Matchers.somethingLike('booking-new-123'),
                status: 'confirmed',
                propertyId: TEST_DATA.PROPERTY_IDS.VALID,
                checkIn: TEST_DATA.DATES.CHECK_IN,
                checkOut: TEST_DATA.DATES.CHECK_OUT,
                guests: TEST_DATA.GUESTS.COUPLE,
                totalAmount: {
                  currency: TEST_DATA.CURRENCIES.AUD,
                  amount: Matchers.decimal(450.0),
                },
                confirmationNumber: Matchers.somethingLike('QH123456789'),
                createdAt: Matchers.iso8601DateTime(),
              },
            },
          },
        });
      });

      it('should return 201 Created for successful resource creation', async () => {
        const response = await axios({
          method: 'POST',
          url: `${mockServerBaseUrl}${API_PATHS.HOTELS.BOOKINGS}`,
          headers: {
            [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          data: {
            propertyId: TEST_DATA.PROPERTY_IDS.VALID,
            checkIn: TEST_DATA.DATES.CHECK_IN,
            checkOut: TEST_DATA.DATES.CHECK_OUT,
            adults: 2,
            children: 0,
            infants: 0,
          },
        });

        // Verify correct status code for resource creation
        expect(response.status).toBe(HTTP_STATUS.CREATED);
        expect(response.status).not.toBe(HTTP_STATUS.OK); // Should not be 200 for creation
        expect(response.status).not.toBe(HTTP_STATUS.ACCEPTED); // Should not be 202 for immediate creation

        // Verify response contains created resource
        expect(response.data).toHaveProperty('booking');
        expect(response.data.booking).toHaveProperty('id');
        expect(response.data.booking).toHaveProperty('createdAt');
        expect(response.data.booking.status).toBe('confirmed');
      });
    });
  });
});
