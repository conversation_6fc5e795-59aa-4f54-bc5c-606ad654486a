/**
 * Core HTTP error response contract tests
 * Tests essential HTTP error scenarios and contract validation
 */

const { Matchers } = require('@pact-foundation/pact');
const { createContractTestSuite, createMockServerUrlGetter, addInteractionSafely } = require('../helpers/testRunner');
const axios = require('axios');
const { API_PATHS, HTTP_STATUS, HTTP_HEADERS, CONTENT_TYPES, TEST_DATA, PROVIDER_STATES } = require('../shared/constants');

createContractTestSuite('Core Error Handling Contract Tests', 'unknown-provider', (contractTest) => {
  const getMockServerUrl = createMockServerUrlGetter(contractTest);

  describe('HTTP 400 Bad Request Error Handling', () => {
    describe('Invalid request payload validation', () => {
      const invalidBookingPayload = {
        propertyId: '', // Invalid empty property ID
        checkIn: 'invalid-date', // Invalid date format
        checkOut: TEST_DATA.DATES.CHECK_OUT,
        adults: -1, // Invalid negative number
      };

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: 'request has invalid payload data',
          uponReceiving: 'a request with invalid booking payload',
          withRequest: {
            method: 'POST',
            path: API_PATHS.HOTELS.BOOKINGS,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
            body: invalidBookingPayload,
          },
          willRespondWith: {
            status: HTTP_STATUS.BAD_REQUEST,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'INVALID_REQUEST',
                message: 'Request contains invalid data',
                details: {
                  validationErrors: Matchers.eachLike(
                    {
                      field: Matchers.somethingLike('propertyId'),
                      message: Matchers.somethingLike('Property ID cannot be empty'),
                      code: Matchers.somethingLike('REQUIRED_FIELD'),
                    },
                    { min: 1 },
                  ),
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 400 for invalid request payload with detailed validation errors', async () => {
        const response = await axios({
          method: 'POST',
          url: `${getMockServerUrl()}${API_PATHS.HOTELS.BOOKINGS}`,
          headers: {
            [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          data: invalidBookingPayload,
          validateStatus: () => true, // Don't throw on error status codes
        });

        // Verify HTTP status code
        expect(response.status).toBe(HTTP_STATUS.BAD_REQUEST);

        // Verify error response structure
        expect(response.data).toMatchObject({
          error: {
            code: 'INVALID_REQUEST',
            message: 'Request contains invalid data',
            details: {
              validationErrors: expect.arrayContaining([
                expect.objectContaining({
                  field: expect.any(String),
                  message: expect.any(String),
                  code: expect.any(String),
                }),
              ]),
            },
          },
        });

        // Verify required metadata fields
        expect(response.data.timestamp).toBeDefined();
        expect(response.data.requestId).toBeDefined();

        // Verify timestamp format
        expect(response.data.timestamp).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/);

        // Verify request ID format (UUID)
        expect(response.data.requestId).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i);
      });
    });
  });

  describe('HTTP 404 Not Found Error Handling', () => {
    describe('Non-existent resource handling', () => {
      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.BOOKINGS.BOOKING_NOT_FOUND,
          uponReceiving: 'a request for non-existent booking',
          withRequest: {
            method: 'GET',
            path: `/bookings/${TEST_DATA.BOOKING_IDS.NOT_FOUND}`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.NOT_FOUND,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'BOOKING_NOT_FOUND',
                message: 'The requested booking could not be found',
                details: {
                  bookingId: TEST_DATA.BOOKING_IDS.NOT_FOUND,
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 404 for non-existent booking with proper error structure', async () => {
        const response = await axios({
          method: 'GET',
          url: `${getMockServerUrl()}/bookings/${TEST_DATA.BOOKING_IDS.NOT_FOUND}`,
          headers: {
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          validateStatus: () => true, // Don't throw on error status codes
        });

        // Verify HTTP status code
        expect(response.status).toBe(HTTP_STATUS.NOT_FOUND);

        // Verify error response structure
        expect(response.data).toMatchObject({
          error: {
            code: 'BOOKING_NOT_FOUND',
            message: 'The requested booking could not be found',
            details: {
              bookingId: TEST_DATA.BOOKING_IDS.NOT_FOUND,
            },
          },
        });

        // Verify required metadata fields
        expect(response.data.timestamp).toBeDefined();
        expect(response.data.requestId).toBeDefined();
      });
    });
  });

  describe('Contract Schema Validation', () => {
    describe('Response schema structure validation', () => {
      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.BOOKINGS.BOOKING_EXISTS,
          uponReceiving: 'a request expecting booking response with correct schema',
          withRequest: {
            method: 'GET',
            path: `/bookings/${TEST_DATA.BOOKING_IDS.VALID}`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              booking: {
                id: Matchers.somethingLike(TEST_DATA.BOOKING_IDS.VALID),
                status: Matchers.term({
                  matcher: '^(confirmed|pending|cancelled)$',
                  generate: 'confirmed',
                }),
                propertyId: Matchers.somethingLike(TEST_DATA.PROPERTY_IDS.VALID),
                propertyName: Matchers.somethingLike('Test Hotel Sydney'),
                checkIn: Matchers.iso8601Date(),
                checkOut: Matchers.iso8601Date(),
                guests: {
                  adults: Matchers.integer(2),
                  children: Matchers.integer(0),
                  infants: Matchers.integer(0),
                },
                totalAmount: {
                  currency: Matchers.term({
                    matcher: '^[A-Z]{3}$',
                    generate: 'AUD',
                  }),
                  amount: Matchers.decimal(450.0),
                },
                confirmationNumber: Matchers.somethingLike('QH123456789'),
                createdAt: Matchers.iso8601DateTime(),
                updatedAt: Matchers.iso8601DateTime(),
              },
            },
          },
        });
      });

      it('should validate booking response schema and data types', async () => {
        const response = await axios({
          method: 'GET',
          url: `${getMockServerUrl()}/bookings/${TEST_DATA.BOOKING_IDS.VALID}`,
          headers: {
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          validateStatus: () => true,
        });

        // Verify HTTP status code
        expect(response.status).toBe(HTTP_STATUS.OK);

        // Verify top-level response structure
        expect(response.data).toHaveProperty('booking');

        const booking = response.data.booking;

        // Verify all required fields are present
        const requiredFields = [
          'id',
          'status',
          'propertyId',
          'propertyName',
          'checkIn',
          'checkOut',
          'guests',
          'totalAmount',
          'confirmationNumber',
          'createdAt',
          'updatedAt',
        ];

        requiredFields.forEach((field) => {
          expect(booking).toHaveProperty(field);
        });

        // Verify nested object structures
        expect(booking.guests).toHaveProperty('adults');
        expect(booking.guests).toHaveProperty('children');
        expect(booking.guests).toHaveProperty('infants');
        expect(booking.totalAmount).toHaveProperty('currency');
        expect(booking.totalAmount).toHaveProperty('amount');

        // Verify data types
        expect(typeof booking.id).toBe('string');
        expect(typeof booking.status).toBe('string');
        expect(typeof booking.propertyId).toBe('string');
        expect(typeof booking.propertyName).toBe('string');
        expect(typeof booking.checkIn).toBe('string');
        expect(typeof booking.checkOut).toBe('string');
        expect(typeof booking.guests.adults).toBe('number');
        expect(typeof booking.guests.children).toBe('number');
        expect(typeof booking.guests.infants).toBe('number');
        expect(typeof booking.totalAmount.currency).toBe('string');
        expect(typeof booking.totalAmount.amount).toBe('number');
        expect(typeof booking.confirmationNumber).toBe('string');
        expect(typeof booking.createdAt).toBe('string');
        expect(typeof booking.updatedAt).toBe('string');

        // Verify format constraints
        expect(booking.status).toMatch(/^(confirmed|pending|cancelled)$/);
        expect(booking.totalAmount.currency).toMatch(/^[A-Z]{3}$/);
        expect(booking.checkIn).toMatch(/^\d{4}-\d{2}-\d{2}$/);
        expect(booking.checkOut).toMatch(/^\d{4}-\d{2}-\d{2}$/);
        expect(booking.createdAt).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/);
        expect(booking.updatedAt).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/);

        // Verify numeric constraints
        expect(booking.guests.adults).toBeGreaterThanOrEqual(0);
        expect(booking.guests.children).toBeGreaterThanOrEqual(0);
        expect(booking.guests.infants).toBeGreaterThanOrEqual(0);
        expect(booking.totalAmount.amount).toBeGreaterThan(0);
      });
    });
  });

  describe('HTTP Status Code Validation', () => {
    describe('Resource creation status code validation', () => {
      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.BOOKINGS.BOOKING_CAN_BE_CREATED,
          uponReceiving: 'a booking creation request expecting 201 Created',
          withRequest: {
            method: 'POST',
            path: API_PATHS.HOTELS.BOOKINGS,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
            body: {
              propertyId: TEST_DATA.PROPERTY_IDS.VALID,
              checkIn: TEST_DATA.DATES.CHECK_IN,
              checkOut: TEST_DATA.DATES.CHECK_OUT,
              adults: 2,
              children: 0,
              infants: 0,
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.CREATED, // Correct status for resource creation
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              booking: {
                id: Matchers.somethingLike('booking-new-123'),
                status: 'confirmed',
                propertyId: TEST_DATA.PROPERTY_IDS.VALID,
                checkIn: TEST_DATA.DATES.CHECK_IN,
                checkOut: TEST_DATA.DATES.CHECK_OUT,
                guests: TEST_DATA.GUESTS.COUPLE,
                totalAmount: {
                  currency: TEST_DATA.CURRENCIES.AUD,
                  amount: Matchers.decimal(450.0),
                },
                confirmationNumber: Matchers.somethingLike('QH123456789'),
                createdAt: Matchers.iso8601DateTime(),
              },
            },
          },
        });
      });

      it('should return 201 Created for successful resource creation', async () => {
        const response = await axios({
          method: 'POST',
          url: `${getMockServerUrl()}${API_PATHS.HOTELS.BOOKINGS}`,
          headers: {
            [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          data: {
            propertyId: TEST_DATA.PROPERTY_IDS.VALID,
            checkIn: TEST_DATA.DATES.CHECK_IN,
            checkOut: TEST_DATA.DATES.CHECK_OUT,
            adults: 2,
            children: 0,
            infants: 0,
          },
          validateStatus: () => true,
        });

        // Verify correct status code for resource creation
        expect(response.status).toBe(HTTP_STATUS.CREATED);

        // Verify it's not other common status codes
        expect(response.status).not.toBe(HTTP_STATUS.OK); // Should not be 200 for creation
        expect(response.status).not.toBe(HTTP_STATUS.ACCEPTED); // Should not be 202 for immediate creation

        // Verify response contains created resource
        expect(response.data).toHaveProperty('booking');
        expect(response.data.booking).toHaveProperty('id');
        expect(response.data.booking).toHaveProperty('createdAt');
        expect(response.data.booking.status).toBe('confirmed');

        // Verify the created resource has expected structure
        expect(response.data.booking.propertyId).toBe(TEST_DATA.PROPERTY_IDS.VALID);
        expect(response.data.booking.checkIn).toBe(TEST_DATA.DATES.CHECK_IN);
        expect(response.data.booking.checkOut).toBe(TEST_DATA.DATES.CHECK_OUT);
        expect(response.data.booking.guests).toEqual(TEST_DATA.GUESTS.COUPLE);
      });
    });
  });
});
