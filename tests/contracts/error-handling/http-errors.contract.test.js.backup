/**
 * Contract tests for HTTP error response scenarios
 * Tests various HTTP error status codes and their response formats
 */

const { Pact, Matchers } = require('@pact-foundation/pact');
const path = require('path');
const axios = require('axios');
const { API_PATHS, HTTP_STATUS, HTTP_HEADERS, CONTENT_TYPES, TEST_DATA, PROVIDER_STATES } = require('../shared/constants');

describe('HTTP Error Response Contract Tests', () => {
  let provider;
  const mockServerBaseUrl = 'http://localhost:1235';

  beforeAll(() => {
    provider = new Pact({
      consumer: 'qantas-hotels-ui',
      provider: 'hotels-api',
      port: 1235,
      log: path.resolve(process.cwd(), 'logs', 'pact-error-handling.log'),
      dir: path.resolve(process.cwd(), 'pacts'),
      logLevel: 'INFO',
    });

    return provider.setup();
  });

  afterAll(() => {
    return provider.finalize();
  });

  afterEach(() => {
    return provider.verify();
  });

  describe('400 Bad Request Error Scenarios', () => {
    describe('Invalid request payload', () => {
      const invalidBookingPayload = {
        propertyId: '', // Invalid empty property ID
        checkIn: 'invalid-date', // Invalid date format
        checkOut: TEST_DATA.DATES.CHECK_OUT,
        adults: -1, // Invalid negative number
        children: 'not-a-number', // Invalid data type
      };

      beforeEach(() => {
        return provider.addInteraction({
          state: 'request has invalid payload data',
          uponReceiving: 'a request with invalid booking payload',
          withRequest: {
            method: 'POST',
            path: API_PATHS.HOTELS.BOOKINGS,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
            body: invalidBookingPayload,
          },
          willRespondWith: {
            status: HTTP_STATUS.BAD_REQUEST,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'INVALID_REQUEST',
                message: 'Request contains invalid data',
                details: {
                  validationErrors: Matchers.eachLike(
                    {
                      field: Matchers.somethingLike('propertyId'),
                      message: Matchers.somethingLike('Property ID cannot be empty'),
                      code: Matchers.somethingLike('REQUIRED_FIELD'),
                    },
                    { min: 1 },
                  ),
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 400 for invalid request payload', async () => {
        const response = await axios({
          method: 'POST',
          url: `${mockServerBaseUrl}${API_PATHS.HOTELS.BOOKINGS}`,
          headers: {
            [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          data: invalidBookingPayload,
          validateStatus: () => true, // Don't throw on error status codes
        });

        expect(response.status).toBe(HTTP_STATUS.BAD_REQUEST);
        expect(response.data).toMatchObject({
          error: {
            code: 'INVALID_REQUEST',
            message: 'Request contains invalid data',
            details: {
              validationErrors: expect.arrayContaining([
                expect.objectContaining({
                  field: expect.any(String),
                  message: expect.any(String),
                  code: expect.any(String),
                }),
              ]),
            },
          },
        });
        expect(response.data.timestamp).toBeDefined();
        expect(response.data.requestId).toBeDefined();
      });
    });

    describe('Missing required headers', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: 'request is missing required headers',
          uponReceiving: 'a request without Content-Type header',
          withRequest: {
            method: 'POST',
            path: API_PATHS.HOTELS.BOOKINGS,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              // Missing Content-Type header
            },
            body: {
              propertyId: TEST_DATA.PROPERTY_IDS.VALID,
              checkIn: TEST_DATA.DATES.CHECK_IN,
              checkOut: TEST_DATA.DATES.CHECK_OUT,
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.BAD_REQUEST,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'MISSING_HEADER',
                message: 'Required header is missing',
                details: {
                  missingHeader: 'Content-Type',
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 400 for missing required headers', async () => {
        const response = await axios({
          method: 'POST',
          url: `${mockServerBaseUrl}${API_PATHS.HOTELS.BOOKINGS}`,
          headers: {
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            // Intentionally omitting Content-Type
          },
          data: {
            propertyId: TEST_DATA.PROPERTY_IDS.VALID,
            checkIn: TEST_DATA.DATES.CHECK_IN,
            checkOut: TEST_DATA.DATES.CHECK_OUT,
          },
          validateStatus: () => true, // Don't throw on error status codes
        });

        expect(response.status).toBe(HTTP_STATUS.BAD_REQUEST);
        expect(response.data).toMatchObject({
          error: {
            code: 'MISSING_HEADER',
            message: 'Required header is missing',
            details: {
              missingHeader: 'Content-Type',
            },
          },
        });
        expect(response.data.timestamp).toBeDefined();
        expect(response.data.requestId).toBeDefined();
      });
    });
  });

  describe('401 Unauthorized Error Scenarios', () => {
    describe('Missing authentication token', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: 'request has no authentication token',
          uponReceiving: 'a request without Authorization header',
          withRequest: {
            method: 'GET',
            path: `/bookings/${TEST_DATA.BOOKING_IDS.VALID}`,
            headers: {
              // Missing Authorization header
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.UNAUTHORIZED,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'MISSING_AUTHENTICATION',
                message: 'Authentication token is required',
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 401 for missing authentication token', async () => {
        const response = await axios({
          method: 'GET',
          url: `${mockServerBaseUrl}/bookings/${TEST_DATA.BOOKING_IDS.VALID}`,
          // Intentionally omitting Authorization header
          validateStatus: () => true, // Don't throw on error status codes
        });

        expect(response.status).toBe(HTTP_STATUS.UNAUTHORIZED);
        expect(response.data).toMatchObject({
          error: {
            code: 'MISSING_AUTHENTICATION',
            message: 'Authentication token is required',
          },
        });
        expect(response.data.timestamp).toBeDefined();
        expect(response.data.requestId).toBeDefined();
      });
    });

    describe('Invalid authentication token', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.AUTH.TOKEN_INVALID,
          uponReceiving: 'a request with invalid authentication token',
          withRequest: {
            method: 'GET',
            path: `/bookings/${TEST_DATA.BOOKING_IDS.VALID}`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer invalid-token',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.UNAUTHORIZED,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'INVALID_TOKEN',
                message: 'Authentication token is invalid or malformed',
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 401 for invalid authentication token', async () => {
        const response = await axios({
          method: 'GET',
          url: `${mockServerBaseUrl}/bookings/${TEST_DATA.BOOKING_IDS.VALID}`,
          headers: {
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer invalid-token',
          },
          validateStatus: () => true, // Don't throw on error status codes
        });

        expect(response.status).toBe(HTTP_STATUS.UNAUTHORIZED);
        expect(response.data).toMatchObject({
          error: {
            code: 'INVALID_TOKEN',
            message: 'Authentication token is invalid or malformed',
          },
        });
        expect(response.data.timestamp).toBeDefined();
        expect(response.data.requestId).toBeDefined();
      });
    });

    describe('Expired authentication token', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.AUTH.TOKEN_EXPIRED,
          uponReceiving: 'a request with expired authentication token',
          withRequest: {
            method: 'GET',
            path: `/bookings/${TEST_DATA.BOOKING_IDS.VALID}`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer expired-token',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.UNAUTHORIZED,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'TOKEN_EXPIRED',
                message: 'Authentication token has expired',
                details: {
                  expiredAt: Matchers.iso8601DateTime(),
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 401 for expired authentication token', async () => {
        const response = await axios({
          method: 'GET',
          url: `${mockServerBaseUrl}/bookings/${TEST_DATA.BOOKING_IDS.VALID}`,
          headers: {
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer expired-token',
          },
          validateStatus: () => true, // Don't throw on error status codes
        });

        expect(response.status).toBe(HTTP_STATUS.UNAUTHORIZED);
        expect(response.data).toMatchObject({
          error: {
            code: 'TOKEN_EXPIRED',
            message: 'Authentication token has expired',
            details: {
              expiredAt: expect.any(String),
            },
          },
        });
        expect(response.data.timestamp).toBeDefined();
        expect(response.data.requestId).toBeDefined();
      });
    });
  });

  describe('404 Not Found Error Scenarios', () => {
    describe('Non-existent booking', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.BOOKINGS.BOOKING_NOT_FOUND,
          uponReceiving: 'a request for non-existent booking',
          withRequest: {
            method: 'GET',
            path: `/bookings/${TEST_DATA.BOOKING_IDS.NOT_FOUND}`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.NOT_FOUND,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'BOOKING_NOT_FOUND',
                message: 'The requested booking could not be found',
                details: {
                  bookingId: TEST_DATA.BOOKING_IDS.NOT_FOUND,
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 404 for non-existent booking', async () => {
        const response = await axios({
          method: 'GET',
          url: `${mockServerBaseUrl}/bookings/${TEST_DATA.BOOKING_IDS.NOT_FOUND}`,
          headers: {
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          validateStatus: () => true, // Don't throw on error status codes
        });

        expect(response.status).toBe(HTTP_STATUS.NOT_FOUND);
        expect(response.data).toMatchObject({
          error: {
            code: 'BOOKING_NOT_FOUND',
            message: 'The requested booking could not be found',
            details: {
              bookingId: TEST_DATA.BOOKING_IDS.NOT_FOUND,
            },
          },
        });
        expect(response.data.timestamp).toBeDefined();
        expect(response.data.requestId).toBeDefined();
      });
    });

    describe('Non-existent property', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.PROPERTIES.PROPERTY_NOT_FOUND,
          uponReceiving: 'a request for non-existent property',
          withRequest: {
            method: 'GET',
            path: `/properties/${TEST_DATA.PROPERTY_IDS.NOT_FOUND}`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.NOT_FOUND,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'PROPERTY_NOT_FOUND',
                message: 'The requested property could not be found',
                details: {
                  propertyId: TEST_DATA.PROPERTY_IDS.NOT_FOUND,
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 404 for non-existent property', async () => {
        const response = await axios({
          method: 'GET',
          url: `${mockServerBaseUrl}/properties/${TEST_DATA.PROPERTY_IDS.NOT_FOUND}`,
          headers: {
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          validateStatus: () => true, // Don't throw on error status codes
        });

        expect(response.status).toBe(HTTP_STATUS.NOT_FOUND);
        expect(response.data).toMatchObject({
          error: {
            code: 'PROPERTY_NOT_FOUND',
            message: 'The requested property could not be found',
            details: {
              propertyId: TEST_DATA.PROPERTY_IDS.NOT_FOUND,
            },
          },
        });
        expect(response.data.timestamp).toBeDefined();
        expect(response.data.requestId).toBeDefined();
      });
    });

    describe('Invalid API endpoint', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: 'API endpoint does not exist',
          uponReceiving: 'a request to non-existent API endpoint',
          withRequest: {
            method: 'GET',
            path: '/non-existent-endpoint',
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.NOT_FOUND,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'ENDPOINT_NOT_FOUND',
                message: 'The requested API endpoint does not exist',
                details: {
                  path: '/non-existent-endpoint',
                  method: 'GET',
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 404 for invalid API endpoint', async () => {
        const response = await axios({
          method: 'GET',
          url: `${mockServerBaseUrl}/non-existent-endpoint`,
          headers: {
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          validateStatus: () => true, // Don't throw on error status codes
        });

        expect(response.status).toBe(HTTP_STATUS.NOT_FOUND);
        expect(response.data).toMatchObject({
          error: {
            code: 'ENDPOINT_NOT_FOUND',
            message: 'The requested API endpoint does not exist',
            details: {
              path: '/non-existent-endpoint',
              method: 'GET',
            },
          },
        });
        expect(response.data.timestamp).toBeDefined();
        expect(response.data.requestId).toBeDefined();
      });
    });
  });

  describe('500 Internal Server Error Scenarios', () => {
    describe('Database connection failure', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: 'database is unavailable',
          uponReceiving: 'a request when database is down',
          withRequest: {
            method: 'GET',
            path: `/bookings/${TEST_DATA.BOOKING_IDS.VALID}`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.INTERNAL_SERVER_ERROR,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'DATABASE_ERROR',
                message: 'An internal server error occurred',
                details: {
                  type: 'database_connection_failure',
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 500 for database connection failure', async () => {
        const response = await axios({
          method: 'GET',
          url: `${mockServerBaseUrl}/bookings/${TEST_DATA.BOOKING_IDS.VALID}`,
          headers: {
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          validateStatus: () => true, // Don't throw on error status codes
        });

        expect(response.status).toBe(HTTP_STATUS.INTERNAL_SERVER_ERROR);
        expect(response.data).toMatchObject({
          error: {
            code: 'DATABASE_ERROR',
            message: 'An internal server error occurred',
            details: {
              type: 'database_connection_failure',
            },
          },
        });
        expect(response.data.timestamp).toBeDefined();
        expect(response.data.requestId).toBeDefined();
      });
    });

    describe('Unexpected server error', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: 'server encounters unexpected error',
          uponReceiving: 'a request that causes server error',
          withRequest: {
            method: 'POST',
            path: API_PATHS.HOTELS.BOOKINGS,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
            body: {
              propertyId: TEST_DATA.PROPERTY_IDS.VALID,
              checkIn: TEST_DATA.DATES.CHECK_IN,
              checkOut: TEST_DATA.DATES.CHECK_OUT,
              adults: 2,
              children: 0,
              infants: 0,
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.INTERNAL_SERVER_ERROR,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'INTERNAL_ERROR',
                message: 'An unexpected error occurred while processing the request',
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 500 for unexpected server error', async () => {
        const response = await axios({
          method: 'POST',
          url: `${mockServerBaseUrl}${API_PATHS.HOTELS.BOOKINGS}`,
          headers: {
            [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          data: {
            propertyId: TEST_DATA.PROPERTY_IDS.VALID,
            checkIn: TEST_DATA.DATES.CHECK_IN,
            checkOut: TEST_DATA.DATES.CHECK_OUT,
            adults: 2,
            children: 0,
            infants: 0,
          },
          validateStatus: () => true, // Don't throw on error status codes
        });

        expect(response.status).toBe(HTTP_STATUS.INTERNAL_SERVER_ERROR);
        expect(response.data).toMatchObject({
          error: {
            code: 'INTERNAL_ERROR',
            message: 'An unexpected error occurred while processing the request',
          },
        });
        expect(response.data.timestamp).toBeDefined();
        expect(response.data.requestId).toBeDefined();
      });
    });

    describe('Third-party service failure', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: 'third-party payment service is unavailable',
          uponReceiving: 'a booking request when payment service is down',
          withRequest: {
            method: 'POST',
            path: API_PATHS.HOTELS.BOOKINGS,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
            body: {
              propertyId: TEST_DATA.PROPERTY_IDS.VALID,
              checkIn: TEST_DATA.DATES.CHECK_IN,
              checkOut: TEST_DATA.DATES.CHECK_OUT,
              adults: 2,
              children: 0,
              infants: 0,
              paymentDetails: {
                method: 'credit_card',
                cardToken: 'card-token-123',
              },
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.INTERNAL_SERVER_ERROR,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'EXTERNAL_SERVICE_ERROR',
                message: 'A required external service is currently unavailable',
                details: {
                  service: 'payment_processor',
                  retryAfter: Matchers.integer(300),
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 500 for third-party service failure', async () => {
        const response = await axios({
          method: 'POST',
          url: `${mockServerBaseUrl}${API_PATHS.HOTELS.BOOKINGS}`,
          headers: {
            [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          data: {
            propertyId: TEST_DATA.PROPERTY_IDS.VALID,
            checkIn: TEST_DATA.DATES.CHECK_IN,
            checkOut: TEST_DATA.DATES.CHECK_OUT,
            adults: 2,
            children: 0,
            infants: 0,
            paymentDetails: {
              method: 'credit_card',
              cardToken: 'card-token-123',
            },
          },
          validateStatus: () => true, // Don't throw on error status codes
        });

        expect(response.status).toBe(HTTP_STATUS.INTERNAL_SERVER_ERROR);
        expect(response.data).toMatchObject({
          error: {
            code: 'EXTERNAL_SERVICE_ERROR',
            message: 'A required external service is currently unavailable',
            details: {
              service: 'payment_processor',
              retryAfter: expect.any(Number),
            },
          },
        });
        expect(response.data.timestamp).toBeDefined();
        expect(response.data.requestId).toBeDefined();
      });
    });
  });
});
