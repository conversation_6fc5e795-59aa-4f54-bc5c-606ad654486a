/**
 * Simplified HTTP error response contract tests
 * Tests core HTTP error scenarios with reliable request patterns
 */

const { Matchers } = require('@pact-foundation/pact');
const { createContractTestSuite, createMockServerUrlGetter, addInteractionSafely } = require('../helpers/testRunner');
const axios = require('axios');
const { API_PATHS, HTTP_STATUS, HTTP_HEADERS, CONTENT_TYPES, TEST_DATA, PROVIDER_STATES } = require('../shared/constants');

createContractTestSuite('Simplified HTTP Error Response Contract Tests', 'unknown-provider', (contractTest) => {
  const getMockServerUrl = createMockServerUrlGetter(contractTest);

  describe('400 Bad Request Error Scenarios', () => {
    describe('Invalid request payload', () => {
      const invalidBookingPayload = {
        propertyId: '', // Invalid empty property ID
        checkIn: 'invalid-date', // Invalid date format
        checkOut: TEST_DATA.DATES.CHECK_OUT,
        adults: -1, // Invalid negative number
      };

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: 'request has invalid payload data',
          uponReceiving: 'a request with invalid booking payload',
          withRequest: {
            method: 'POST',
            path: API_PATHS.HOTELS.BOOKINGS,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
            body: invalidBookingPayload,
          },
          willRespondWith: {
            status: HTTP_STATUS.BAD_REQUEST,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'INVALID_REQUEST',
                message: 'Request contains invalid data',
                details: {
                  validationErrors: Matchers.eachLike(
                    {
                      field: Matchers.somethingLike('propertyId'),
                      message: Matchers.somethingLike('Property ID cannot be empty'),
                      code: Matchers.somethingLike('REQUIRED_FIELD'),
                    },
                    { min: 1 },
                  ),
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 400 for invalid request payload', async () => {
        const response = await axios({
          method: 'POST',
          url: `${getMockServerUrl()}${API_PATHS.HOTELS.BOOKINGS}`,
          headers: {
            [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          data: invalidBookingPayload,
          validateStatus: () => true, // Don't throw on error status codes
        });

        expect(response.status).toBe(HTTP_STATUS.BAD_REQUEST);
        expect(response.data).toMatchObject({
          error: {
            code: 'INVALID_REQUEST',
            message: 'Request contains invalid data',
            details: {
              validationErrors: expect.arrayContaining([
                expect.objectContaining({
                  field: expect.any(String),
                  message: expect.any(String),
                  code: expect.any(String),
                }),
              ]),
            },
          },
        });
        expect(response.data.timestamp).toBeDefined();
        expect(response.data.requestId).toBeDefined();
      });
    });
  });

  describe('401 Unauthorized Error Scenarios', () => {
    describe('Invalid authentication token', () => {
      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.AUTH.TOKEN_INVALID,
          uponReceiving: 'a request with invalid authentication token',
          withRequest: {
            method: 'GET',
            path: `/bookings/${TEST_DATA.BOOKING_IDS.VALID}`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer invalid-token',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.UNAUTHORIZED,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'INVALID_TOKEN',
                message: 'Authentication token is invalid or malformed',
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 401 for invalid authentication token', async () => {
        const response = await axios({
          method: 'GET',
          url: `${getMockServerUrl()}/bookings/${TEST_DATA.BOOKING_IDS.VALID}`,
          headers: {
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer invalid-token',
          },
          validateStatus: () => true, // Don't throw on error status codes
        });

        expect(response.status).toBe(HTTP_STATUS.UNAUTHORIZED);
        expect(response.data).toMatchObject({
          error: {
            code: 'INVALID_TOKEN',
            message: 'Authentication token is invalid or malformed',
          },
        });
        expect(response.data.timestamp).toBeDefined();
        expect(response.data.requestId).toBeDefined();
      });
    });
  });

  describe('404 Not Found Error Scenarios', () => {
    describe('Non-existent booking', () => {
      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.BOOKINGS.BOOKING_NOT_FOUND,
          uponReceiving: 'a request for non-existent booking',
          withRequest: {
            method: 'GET',
            path: `/bookings/${TEST_DATA.BOOKING_IDS.NOT_FOUND}`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.NOT_FOUND,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'BOOKING_NOT_FOUND',
                message: 'The requested booking could not be found',
                details: {
                  bookingId: TEST_DATA.BOOKING_IDS.NOT_FOUND,
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 404 for non-existent booking', async () => {
        const response = await axios({
          method: 'GET',
          url: `${getMockServerUrl()}/bookings/${TEST_DATA.BOOKING_IDS.NOT_FOUND}`,
          headers: {
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          validateStatus: () => true, // Don't throw on error status codes
        });

        expect(response.status).toBe(HTTP_STATUS.NOT_FOUND);
        expect(response.data).toMatchObject({
          error: {
            code: 'BOOKING_NOT_FOUND',
            message: 'The requested booking could not be found',
            details: {
              bookingId: TEST_DATA.BOOKING_IDS.NOT_FOUND,
            },
          },
        });
        expect(response.data.timestamp).toBeDefined();
        expect(response.data.requestId).toBeDefined();
      });
    });
  });

  describe('500 Internal Server Error Scenarios', () => {
    describe('Database connection failure', () => {
      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: 'database is unavailable',
          uponReceiving: 'a request when database is down',
          withRequest: {
            method: 'GET',
            path: `/bookings/${TEST_DATA.BOOKING_IDS.VALID}`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.INTERNAL_SERVER_ERROR,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'DATABASE_ERROR',
                message: 'An internal server error occurred',
                details: {
                  type: 'database_connection_failure',
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 500 for database connection failure', async () => {
        const response = await axios({
          method: 'GET',
          url: `${getMockServerUrl()}/bookings/${TEST_DATA.BOOKING_IDS.VALID}`,
          headers: {
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          validateStatus: () => true, // Don't throw on error status codes
        });

        expect(response.status).toBe(HTTP_STATUS.INTERNAL_SERVER_ERROR);
        expect(response.data).toMatchObject({
          error: {
            code: 'DATABASE_ERROR',
            message: 'An internal server error occurred',
            details: {
              type: 'database_connection_failure',
            },
          },
        });
        expect(response.data.timestamp).toBeDefined();
        expect(response.data.requestId).toBeDefined();
      });
    });
  });

  describe('Contract Validation Error Scenarios', () => {
    describe('Schema validation', () => {
      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.BOOKINGS.BOOKING_EXISTS,
          uponReceiving: 'a request expecting booking response with correct schema',
          withRequest: {
            method: 'GET',
            path: `/bookings/${TEST_DATA.BOOKING_IDS.VALID}`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              booking: {
                id: Matchers.somethingLike(TEST_DATA.BOOKING_IDS.VALID),
                status: Matchers.term({
                  matcher: '^(confirmed|pending|cancelled)$',
                  generate: 'confirmed',
                }),
                propertyId: Matchers.somethingLike(TEST_DATA.PROPERTY_IDS.VALID),
                propertyName: Matchers.somethingLike('Test Hotel Sydney'),
                checkIn: Matchers.iso8601Date(),
                checkOut: Matchers.iso8601Date(),
                guests: {
                  adults: Matchers.integer(2),
                  children: Matchers.integer(0),
                  infants: Matchers.integer(0),
                },
                totalAmount: {
                  currency: Matchers.term({
                    matcher: '^[A-Z]{3}$',
                    generate: 'AUD',
                  }),
                  amount: Matchers.decimal(450.0),
                },
                confirmationNumber: Matchers.somethingLike('QH123456789'),
                createdAt: Matchers.iso8601DateTime(),
                updatedAt: Matchers.iso8601DateTime(),
              },
            },
          },
        });
      });

      it('should validate booking response schema structure', async () => {
        const response = await axios({
          method: 'GET',
          url: `${getMockServerUrl()}/bookings/${TEST_DATA.BOOKING_IDS.VALID}`,
          headers: {
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          validateStatus: () => true,
        });

        expect(response.status).toBe(HTTP_STATUS.OK);

        // Validate response structure matches expected schema
        expect(response.data).toHaveProperty('booking');
        expect(response.data.booking).toHaveProperty('id');
        expect(response.data.booking).toHaveProperty('status');
        expect(response.data.booking).toHaveProperty('propertyId');
        expect(response.data.booking).toHaveProperty('propertyName');
        expect(response.data.booking).toHaveProperty('checkIn');
        expect(response.data.booking).toHaveProperty('checkOut');
        expect(response.data.booking).toHaveProperty('guests');
        expect(response.data.booking).toHaveProperty('totalAmount');
        expect(response.data.booking).toHaveProperty('confirmationNumber');
        expect(response.data.booking).toHaveProperty('createdAt');
        expect(response.data.booking).toHaveProperty('updatedAt');

        // Validate nested object structure
        expect(response.data.booking.guests).toHaveProperty('adults');
        expect(response.data.booking.guests).toHaveProperty('children');
        expect(response.data.booking.guests).toHaveProperty('infants');
        expect(response.data.booking.totalAmount).toHaveProperty('currency');
        expect(response.data.booking.totalAmount).toHaveProperty('amount');

        // Validate data types
        expect(typeof response.data.booking.id).toBe('string');
        expect(typeof response.data.booking.status).toBe('string');
        expect(typeof response.data.booking.propertyId).toBe('string');
        expect(typeof response.data.booking.propertyName).toBe('string');
        expect(typeof response.data.booking.checkIn).toBe('string');
        expect(typeof response.data.booking.checkOut).toBe('string');
        expect(typeof response.data.booking.guests.adults).toBe('number');
        expect(typeof response.data.booking.guests.children).toBe('number');
        expect(typeof response.data.booking.guests.infants).toBe('number');
        expect(typeof response.data.booking.totalAmount.currency).toBe('string');
        expect(typeof response.data.booking.totalAmount.amount).toBe('number');
        expect(typeof response.data.booking.confirmationNumber).toBe('string');
        expect(typeof response.data.booking.createdAt).toBe('string');
        expect(typeof response.data.booking.updatedAt).toBe('string');

        // Validate specific format constraints
        expect(response.data.booking.status).toMatch(/^(confirmed|pending|cancelled)$/);
        expect(response.data.booking.totalAmount.currency).toMatch(/^[A-Z]{3}$/);
        expect(response.data.booking.checkIn).toMatch(/^\d{4}-\d{2}-\d{2}$/);
        expect(response.data.booking.checkOut).toMatch(/^\d{4}-\d{2}-\d{2}$/);
        expect(response.data.booking.createdAt).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/);
        expect(response.data.booking.updatedAt).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/);
      });
    });

    describe('Missing required fields validation', () => {
      const incompleteBookingPayload = {
        // Missing propertyId (required field)
        checkIn: TEST_DATA.DATES.CHECK_IN,
        checkOut: TEST_DATA.DATES.CHECK_OUT,
        adults: 2,
        // Missing children and infants fields
      };

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: 'request has missing required fields',
          uponReceiving: 'a booking request with missing required fields',
          withRequest: {
            method: 'POST',
            path: API_PATHS.HOTELS.BOOKINGS,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
            body: incompleteBookingPayload,
          },
          willRespondWith: {
            status: HTTP_STATUS.BAD_REQUEST,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'MISSING_REQUIRED_FIELDS',
                message: 'Request is missing required fields',
                details: {
                  missingFields: Matchers.eachLike('propertyId', { min: 1 }),
                  providedFields: Matchers.eachLike('checkIn', { min: 1 }),
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should detect and report missing required fields', async () => {
        const response = await axios({
          method: 'POST',
          url: `${getMockServerUrl()}${API_PATHS.HOTELS.BOOKINGS}`,
          headers: {
            [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          data: incompleteBookingPayload,
          validateStatus: () => true, // Don't throw on error status codes
        });

        expect(response.status).toBe(HTTP_STATUS.BAD_REQUEST);
        expect(response.data).toMatchObject({
          error: {
            code: 'MISSING_REQUIRED_FIELDS',
            message: 'Request is missing required fields',
            details: {
              missingFields: expect.arrayContaining([expect.any(String)]),
              providedFields: expect.arrayContaining([expect.any(String)]),
            },
          },
        });
        expect(response.data.timestamp).toBeDefined();
        expect(response.data.requestId).toBeDefined();
      });
    });

    describe('Status code consistency validation', () => {
      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.BOOKINGS.BOOKING_CAN_BE_CREATED,
          uponReceiving: 'a booking creation request expecting 201 Created',
          withRequest: {
            method: 'POST',
            path: API_PATHS.HOTELS.BOOKINGS,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
            body: {
              propertyId: TEST_DATA.PROPERTY_IDS.VALID,
              checkIn: TEST_DATA.DATES.CHECK_IN,
              checkOut: TEST_DATA.DATES.CHECK_OUT,
              adults: 2,
              children: 0,
              infants: 0,
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.CREATED, // Correct status for resource creation
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              booking: {
                id: Matchers.somethingLike('booking-new-123'),
                status: 'confirmed',
                propertyId: TEST_DATA.PROPERTY_IDS.VALID,
                checkIn: TEST_DATA.DATES.CHECK_IN,
                checkOut: TEST_DATA.DATES.CHECK_OUT,
                guests: TEST_DATA.GUESTS.COUPLE,
                totalAmount: {
                  currency: TEST_DATA.CURRENCIES.AUD,
                  amount: Matchers.decimal(450.0),
                },
                confirmationNumber: Matchers.somethingLike('QH123456789'),
                createdAt: Matchers.iso8601DateTime(),
              },
            },
          },
        });
      });

      it('should return 201 Created for successful resource creation', async () => {
        const response = await axios({
          method: 'POST',
          url: `${getMockServerUrl()}${API_PATHS.HOTELS.BOOKINGS}`,
          headers: {
            [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          data: {
            propertyId: TEST_DATA.PROPERTY_IDS.VALID,
            checkIn: TEST_DATA.DATES.CHECK_IN,
            checkOut: TEST_DATA.DATES.CHECK_OUT,
            adults: 2,
            children: 0,
            infants: 0,
          },
          validateStatus: () => true,
        });

        // Verify correct status code for resource creation
        expect(response.status).toBe(HTTP_STATUS.CREATED);
        expect(response.status).not.toBe(HTTP_STATUS.OK); // Should not be 200 for creation
        expect(response.status).not.toBe(HTTP_STATUS.ACCEPTED); // Should not be 202 for immediate creation

        // Verify response contains created resource
        expect(response.data).toHaveProperty('booking');
        expect(response.data.booking).toHaveProperty('id');
        expect(response.data.booking).toHaveProperty('createdAt');
        expect(response.data.booking.status).toBe('confirmed');
      });
    });
  });
});
