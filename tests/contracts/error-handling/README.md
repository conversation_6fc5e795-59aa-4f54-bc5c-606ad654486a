# Error Handling Contract Tests

This directory contains comprehensive contract tests for HTTP error response scenarios and contract validation error detection.

## Overview

The error handling contract tests validate that the API properly handles various error scenarios and returns consistent error response formats. These tests ensure that the front-end application can reliably handle and display appropriate error messages to users.

## Test Coverage

### HTTP Error Response Tests

#### 400 Bad Request Scenarios
- **Invalid request payload validation**: Tests that the API returns proper validation errors for malformed request data
- **Missing required fields**: Validates error responses when required fields are omitted
- **Invalid data types**: Tests error handling for incorrect data types in request payloads
- **Invalid date formats**: Validates proper error responses for malformed date fields

#### 401 Unauthorized Scenarios
- **Missing authentication token**: Tests error responses when no authentication is provided
- **Invalid authentication token**: Validates handling of malformed or invalid tokens
- **Expired authentication token**: Tests proper error responses for expired tokens

#### 404 Not Found Scenarios
- **Non-existent booking**: Tests error responses for requests to non-existent bookings
- **Non-existent property**: Validates error handling for invalid property IDs
- **Invalid API endpoints**: Tests responses for requests to non-existent API endpoints

#### 500 Internal Server Error Scenarios
- **Database connection failure**: Tests error responses when database is unavailable
- **Unexpected server errors**: Validates handling of unexpected internal errors
- **Third-party service failures**: Tests error responses when external services are down

### Contract Validation Tests

#### Schema Mismatch Detection
- **Response schema validation**: Ensures API responses match expected data structures
- **Property search response validation**: Tests complex nested object schema validation
- **Data type validation**: Validates that response fields have correct data types

#### Missing Required Fields Validation
- **Booking creation validation**: Tests detection of missing required fields in requests
- **Quote request validation**: Validates proper error handling for incomplete quote requests

#### Status Code Validation
- **Resource creation status codes**: Ensures proper HTTP status codes for different operations
- **Error status code consistency**: Validates that error responses use appropriate status codes

## Test Structure

### Core Files

- `core-error-handling.contract.test.js`: Essential error handling tests that demonstrate the core functionality
- `http-errors.contract.test.js`: Comprehensive HTTP error response tests
- `contract-validation-errors.contract.test.js`: Contract validation and schema mismatch tests
- `simplified-error-tests.contract.test.js`: Streamlined version focusing on key scenarios

### Test Implementation

Each test follows the Pact contract testing pattern:

1. **Setup**: Configure Pact provider with mock server
2. **Interaction Definition**: Define expected request/response pairs
3. **Test Execution**: Make HTTP requests and validate responses
4. **Verification**: Ensure all expected interactions occurred

### Error Response Format

All error responses follow a consistent structure:

```json
{
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": {
      // Additional error-specific details
    }
  },
  "timestamp": "2024-12-01T10:30:00.000Z",
  "requestId": "uuid-v4-request-id"
}
```

## Key Validation Points

### HTTP Status Codes
- **400**: Bad Request - Invalid request data
- **401**: Unauthorized - Authentication issues
- **404**: Not Found - Resource not found
- **500**: Internal Server Error - Server-side errors

### Error Response Validation
- Consistent error response structure
- Proper error codes and messages
- Required metadata fields (timestamp, requestId)
- Detailed error information in the `details` field

### Schema Validation
- Required fields presence
- Correct data types
- Format constraints (dates, currencies, etc.)
- Nested object structure validation

## Running the Tests

```bash
# Run all error handling tests
npm run test:contracts -- tests/contracts/error-handling

# Run specific test file
npm run test:contracts -- tests/contracts/error-handling/core-error-handling.contract.test.js

# Run with watch mode
npm run test:contracts:watch -- tests/contracts/error-handling
```

## Benefits

1. **Fast Feedback**: Contract tests run much faster than E2E tests
2. **Isolated Testing**: Tests focus on API contracts without UI dependencies
3. **Comprehensive Coverage**: Tests both happy path and error scenarios
4. **Schema Validation**: Ensures API responses match expected data structures
5. **Error Handling**: Validates proper error response formats and status codes

## Requirements Satisfied

This implementation satisfies the following requirements from the specification:

- **Requirement 1.2**: Validates HTTP status codes for both success and error scenarios
- **Requirement 3.2**: Includes both happy path scenarios and error handling scenarios
- **Requirement 6.1**: Focuses exclusively on API request/response validation
- **Requirement 6.5**: Validates error response formats and status codes
- **Requirement 6.6**: Provides clear indication of which specific contract expectations were not met

## Future Enhancements

- Add more comprehensive validation for all API endpoints
- Implement provider verification tests
- Add performance benchmarking against E2E tests
- Integrate with CI/CD pipeline for automated contract verification