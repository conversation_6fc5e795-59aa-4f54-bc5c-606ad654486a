/**
 * Contract tests for Points payment integration endpoints
 * Tests the consumer contract for points burn, earn, and combination payment functionality
 */

const { Pact, Matchers } = require('@pact-foundation/pact');
const path = require('path');
const axios = require('axios');
const { HTTP_STATUS, HTTP_HEADERS, CONTENT_TYPES, TEST_DATA, PROVIDER_STATES } = require('../shared/constants');

// Define fail function for test assertions
const fail = (message) => {
  throw new Error(message);
};

describe('Points Payment Integration Contract Tests', () => {
  let provider;
  const mockServerBaseUrl = 'http://localhost:1237';

  beforeAll(() => {
    provider = new Pact({
      consumer: 'qantas-hotels-ui',
      provider: 'hotels-api',
      port: 1237,
      log: path.resolve(process.cwd(), 'logs', 'pact.log'),
      dir: path.resolve(process.cwd(), 'pacts'),
      logLevel: 'INFO',
    });

    return provider.setup();
  });

  afterAll(() => {
    return provider.finalize();
  });

  afterEach(() => {
    return provider.verify();
  });

  describe('GET /points-burn-v2 - Fetch Points Burn Tiers', () => {
    describe('successful points burn tiers retrieval', () => {
      const expectedPointsBurnTiers = {
        pointsTierInstance: {
          name: 'points-burn-v2',
          family: 'points-burn',
          levels: [
            {
              tier: 'bronze',
              minPoints: 0,
              maxPoints: 49999,
              burnRate: {
                pointsPerDollar: 2.5,
                dollarPerPoint: 0.004,
              },
              benefits: ['Standard burn rate'],
            },
            {
              tier: 'silver',
              minPoints: 50000,
              maxPoints: 99999,
              burnRate: {
                pointsPerDollar: 2.0,
                dollarPerPoint: 0.005,
              },
              benefits: ['Enhanced burn rate', 'Priority support'],
            },
            {
              tier: 'gold',
              minPoints: 100000,
              maxPoints: 199999,
              burnRate: {
                pointsPerDollar: 1.8,
                dollarPerPoint: 0.0056,
              },
              benefits: ['Premium burn rate', 'Priority support', 'Bonus points'],
            },
            {
              tier: 'platinum',
              minPoints: 200000,
              maxPoints: null,
              burnRate: {
                pointsPerDollar: 1.5,
                dollarPerPoint: 0.0067,
              },
              benefits: ['Best burn rate', 'VIP support', 'Bonus points', 'Exclusive offers'],
            },
          ],
          metadata: {
            currency: TEST_DATA.CURRENCIES.AUD,
            lastUpdated: Matchers.iso8601DateTime(),
            version: '2.1',
          },
        },
      };

      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.CONTENT.CONTENT_EXISTS,
          uponReceiving: 'a request to fetch points burn tiers',
          withRequest: {
            method: 'GET',
            path: '/points-burn-v2',
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedPointsBurnTiers,
          },
        });
      });

      it('should fetch points burn tiers successfully', async () => {
        const response = await axios({
          method: 'GET',
          url: `${mockServerBaseUrl}/points-burn-v2`,
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data.pointsTierInstance).toEqual(expectedPointsBurnTiers.pointsTierInstance);
        expect(response.data.pointsTierInstance.name).toBe('points-burn-v2');
        expect(response.data.pointsTierInstance.family).toBe('points-burn');
        expect(response.data.pointsTierInstance.levels).toHaveLength(4);
        expect(response.data.pointsTierInstance.levels[0]).toMatchObject({
          tier: 'bronze',
          minPoints: 0,
          maxPoints: 49999,
          burnRate: {
            pointsPerDollar: 2.5,
            dollarPerPoint: 0.004,
          },
        });
        expect(response.data.pointsTierInstance.levels[3]).toMatchObject({
          tier: 'platinum',
          minPoints: 200000,
          maxPoints: null,
          burnRate: {
            pointsPerDollar: 1.5,
            dollarPerPoint: 0.0067,
          },
        });
        expect(response.data.pointsTierInstance.metadata).toBeDefined();
        expect(response.data.pointsTierInstance.metadata.currency).toBe(TEST_DATA.CURRENCIES.AUD);
      });
    });

    describe('points burn tiers service unavailable', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.CONTENT.CONTENT_NOT_FOUND,
          uponReceiving: 'a request to fetch points burn tiers when service unavailable',
          withRequest: {
            method: 'GET',
            path: '/points-burn-v2',
          },
          willRespondWith: {
            status: HTTP_STATUS.SERVICE_UNAVAILABLE,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'SERVICE_UNAVAILABLE',
                message: 'Points burn service is temporarily unavailable',
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should handle service unavailable error', async () => {
        try {
          await axios({
            method: 'GET',
            url: `${mockServerBaseUrl}/points-burn-v2`,
          });
          fail('Expected request to fail with 503');
        } catch (error) {
          expect(error.response?.status).toBe(HTTP_STATUS.SERVICE_UNAVAILABLE);
          if (error.response) {
            expect(error.response.data).toMatchObject({
              error: {
                code: 'SERVICE_UNAVAILABLE',
                message: 'Points burn service is temporarily unavailable',
              },
            });
          }
        }
      });
    });
  });

  describe('GET /points-burn-luxe - Fetch Luxury Points Burn Tiers', () => {
    describe('successful luxury points burn tiers retrieval', () => {
      const expectedLuxePointsBurnTiers = {
        pointsTierInstance: {
          name: 'points-burn-luxe',
          family: 'points-burn-luxe',
          levels: [
            {
              tier: 'luxe-bronze',
              minPoints: 0,
              maxPoints: 99999,
              burnRate: {
                pointsPerDollar: 2.0,
                dollarPerPoint: 0.005,
              },
              benefits: ['Luxury property access', 'Enhanced burn rate'],
              propertyTypes: ['luxury', 'boutique'],
            },
            {
              tier: 'luxe-silver',
              minPoints: 100000,
              maxPoints: 249999,
              burnRate: {
                pointsPerDollar: 1.7,
                dollarPerPoint: 0.0059,
              },
              benefits: ['Premium luxury access', 'Better burn rate', 'Concierge service'],
              propertyTypes: ['luxury', 'boutique', 'premium'],
            },
            {
              tier: 'luxe-gold',
              minPoints: 250000,
              maxPoints: null,
              burnRate: {
                pointsPerDollar: 1.4,
                dollarPerPoint: 0.0071,
              },
              benefits: ['Exclusive luxury access', 'Best burn rate', 'VIP concierge', 'Room upgrades'],
              propertyTypes: ['luxury', 'boutique', 'premium', 'exclusive'],
            },
          ],
          metadata: {
            currency: TEST_DATA.CURRENCIES.AUD,
            lastUpdated: Matchers.iso8601DateTime(),
            version: '1.3',
            category: 'luxury',
          },
        },
      };

      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.CONTENT.CONTENT_EXISTS,
          uponReceiving: 'a request to fetch luxury points burn tiers',
          withRequest: {
            method: 'GET',
            path: '/points-burn-luxe',
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedLuxePointsBurnTiers,
          },
        });
      });

      it('should fetch luxury points burn tiers successfully', async () => {
        const response = await axios({
          method: 'GET',
          url: `${mockServerBaseUrl}/points-burn-luxe`,
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data.pointsTierInstance).toEqual(expectedLuxePointsBurnTiers.pointsTierInstance);
        expect(response.data.pointsTierInstance.name).toBe('points-burn-luxe');
        expect(response.data.pointsTierInstance.family).toBe('points-burn-luxe');
        expect(response.data.pointsTierInstance.levels).toHaveLength(3);
        expect(response.data.pointsTierInstance.levels[0]).toMatchObject({
          tier: 'luxe-bronze',
          minPoints: 0,
          maxPoints: 99999,
          burnRate: {
            pointsPerDollar: 2.0,
            dollarPerPoint: 0.005,
          },
          propertyTypes: ['luxury', 'boutique'],
        });
        expect(response.data.pointsTierInstance.levels[2]).toMatchObject({
          tier: 'luxe-gold',
          minPoints: 250000,
          maxPoints: null,
          burnRate: {
            pointsPerDollar: 1.4,
            dollarPerPoint: 0.0071,
          },
          propertyTypes: ['luxury', 'boutique', 'premium', 'exclusive'],
        });
        expect(response.data.pointsTierInstance.metadata.category).toBe('luxury');
      });
    });
  });

  describe('POST /points/calculate-burn - Calculate Points Burn Amount', () => {
    describe('successful points burn calculation', () => {
      const pointsBurnRequest = {
        memberId: TEST_DATA.MEMBER_IDS.VALID,
        memberTier: 'gold',
        cashAmount: {
          value: 50000, // 500.00 in cents
          currency: TEST_DATA.CURRENCIES.AUD,
        },
        pointsAmount: 90000, // 90,000 points
        propertyType: 'standard',
      };

      const expectedPointsBurnCalculation = {
        calculation: {
          requestedPointsAmount: 90000,
          actualPointsAmount: 90000,
          cashEquivalent: {
            value: 50400, // 504.00 in cents (90000 * 0.0056)
            currency: TEST_DATA.CURRENCIES.AUD,
          },
          burnRate: {
            pointsPerDollar: 1.8,
            dollarPerPoint: 0.0056,
          },
          memberTier: 'gold',
          savings: {
            value: 400, // 4.00 in cents
            currency: TEST_DATA.CURRENCIES.AUD,
            percentage: 0.8,
          },
        },
        memberBalance: {
          availablePoints: 150000,
          remainingPoints: 60000, // 150000 - 90000
          tierStatus: 'gold',
        },
        validation: {
          isValid: true,
          sufficientBalance: true,
          withinLimits: true,
        },
      };

      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.AUTH.USER_AUTHENTICATED,
          uponReceiving: 'a request to calculate points burn amount',
          withRequest: {
            method: 'POST',
            path: '/points/calculate-burn',
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
            body: pointsBurnRequest,
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedPointsBurnCalculation,
          },
        });
      });

      it('should calculate points burn amount successfully', async () => {
        const response = await axios({
          method: 'POST',
          url: `${mockServerBaseUrl}/points/calculate-burn`,
          headers: {
            [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          data: pointsBurnRequest,
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toMatchObject({
          calculation: {
            requestedPointsAmount: 90000,
            actualPointsAmount: 90000,
            cashEquivalent: {
              value: 50400,
              currency: TEST_DATA.CURRENCIES.AUD,
            },
            burnRate: {
              pointsPerDollar: 1.8,
              dollarPerPoint: 0.0056,
            },
            memberTier: 'gold',
          },
          memberBalance: {
            availablePoints: 150000,
            remainingPoints: 60000,
            tierStatus: 'gold',
          },
          validation: {
            isValid: true,
            sufficientBalance: true,
            withinLimits: true,
          },
        });
      });
    });

    describe('points burn calculation with insufficient balance', () => {
      const insufficientPointsRequest = {
        memberId: TEST_DATA.MEMBER_IDS.VALID,
        memberTier: 'bronze',
        cashAmount: {
          value: 100000, // 1000.00 in cents
          currency: TEST_DATA.CURRENCIES.AUD,
        },
        pointsAmount: 300000, // 300,000 points (more than available)
        propertyType: 'standard',
      };

      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.AUTH.USER_AUTHENTICATED,
          uponReceiving: 'a request to calculate points burn with insufficient balance',
          withRequest: {
            method: 'POST',
            path: '/points/calculate-burn',
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
            body: insufficientPointsRequest,
          },
          willRespondWith: {
            status: HTTP_STATUS.UNPROCESSABLE_ENTITY,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'INSUFFICIENT_POINTS_BALANCE',
                message: 'Insufficient points balance for requested burn amount',
                details: {
                  requestedPoints: 300000,
                  availablePoints: 150000,
                  shortfall: 150000,
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 422 for insufficient points balance', async () => {
        try {
          await axios({
            method: 'POST',
            url: `${mockServerBaseUrl}/points/calculate-burn`,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
            data: insufficientPointsRequest,
          });
          fail('Expected request to fail with 422');
        } catch (error) {
          expect(error.response?.status).toBe(HTTP_STATUS.UNPROCESSABLE_ENTITY);
          if (error.response) {
            expect(error.response.data).toMatchObject({
              error: {
                code: 'INSUFFICIENT_POINTS_BALANCE',
                message: 'Insufficient points balance for requested burn amount',
                details: {
                  requestedPoints: 300000,
                  availablePoints: 150000,
                  shortfall: 150000,
                },
              },
            });
          }
        }
      });
    });
  });

  describe('POST /points/calculate-earn - Calculate Points Earn Amount', () => {
    describe('successful points earn calculation', () => {
      const pointsEarnRequest = {
        memberId: TEST_DATA.MEMBER_IDS.VALID,
        memberTier: 'silver',
        bookingAmount: {
          value: 75000, // 750.00 in cents
          currency: TEST_DATA.CURRENCIES.AUD,
        },
        propertyId: TEST_DATA.PROPERTY_IDS.VALID,
        propertyType: 'standard',
        stayDates: {
          checkIn: TEST_DATA.DATES.CHECK_IN,
          checkOut: TEST_DATA.DATES.CHECK_OUT,
        },
      };

      const expectedPointsEarnCalculation = {
        calculation: {
          basePoints: 2250, // 750 * 3 base points per dollar
          bonusPoints: 750, // 750 * 1 bonus point for silver tier
          totalPoints: 3000,
          earnRate: {
            basePointsPerDollar: 3,
            bonusPointsPerDollar: 1,
            totalPointsPerDollar: 4,
          },
          memberTier: 'silver',
        },
        breakdown: {
          baseEarning: {
            amount: {
              value: 75000,
              currency: TEST_DATA.CURRENCIES.AUD,
            },
            points: 2250,
            rate: 3,
          },
          tierBonus: {
            tier: 'silver',
            points: 750,
            rate: 1,
            multiplier: 1.33,
          },
          promotionalBonus: {
            points: 0,
            campaigns: [],
          },
        },
        memberStatus: {
          currentTier: 'silver',
          pointsToNextTier: 47000, // Points needed to reach gold
          nextTier: 'gold',
          tierProgress: 0.53,
        },
      };

      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.AUTH.USER_AUTHENTICATED,
          uponReceiving: 'a request to calculate points earn amount',
          withRequest: {
            method: 'POST',
            path: '/points/calculate-earn',
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
            body: pointsEarnRequest,
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedPointsEarnCalculation,
          },
        });
      });

      it('should calculate points earn amount successfully', async () => {
        const response = await axios({
          method: 'POST',
          url: `${mockServerBaseUrl}/points/calculate-earn`,
          headers: {
            [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          data: pointsEarnRequest,
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toMatchObject({
          calculation: {
            basePoints: 2250,
            bonusPoints: 750,
            totalPoints: 3000,
            earnRate: {
              basePointsPerDollar: 3,
              bonusPointsPerDollar: 1,
              totalPointsPerDollar: 4,
            },
            memberTier: 'silver',
          },
          breakdown: {
            baseEarning: {
              amount: {
                value: 75000,
                currency: TEST_DATA.CURRENCIES.AUD,
              },
              points: 2250,
              rate: 3,
            },
            tierBonus: {
              tier: 'silver',
              points: 750,
              rate: 1,
              multiplier: 1.33,
            },
          },
          memberStatus: {
            currentTier: 'silver',
            pointsToNextTier: 47000,
            nextTier: 'gold',
          },
        });
        expect(response.data.memberStatus.tierProgress).toBeCloseTo(0.53, 2);
      });
    });

    describe('points earn calculation with promotional campaign', () => {
      const promotionalEarnRequest = {
        memberId: TEST_DATA.MEMBER_IDS.VALID,
        memberTier: 'gold',
        bookingAmount: {
          value: 100000, // 1000.00 in cents
          currency: TEST_DATA.CURRENCIES.AUD,
        },
        propertyId: TEST_DATA.PROPERTY_IDS.VALID,
        propertyType: 'luxury',
        stayDates: {
          checkIn: TEST_DATA.DATES.CHECK_IN,
          checkOut: TEST_DATA.DATES.CHECK_OUT,
        },
        campaignCode: 'DOUBLE-POINTS-2024',
      };

      const expectedPromotionalEarnCalculation = {
        calculation: {
          basePoints: 3000, // 1000 * 3 base points per dollar
          bonusPoints: 2000, // 1000 * 2 bonus points for gold tier
          promotionalPoints: 5000, // Double points campaign
          totalPoints: 10000,
          earnRate: {
            basePointsPerDollar: 3,
            bonusPointsPerDollar: 2,
            promotionalPointsPerDollar: 5,
            totalPointsPerDollar: 10,
          },
          memberTier: 'gold',
        },
        breakdown: {
          baseEarning: {
            amount: {
              value: 100000,
              currency: TEST_DATA.CURRENCIES.AUD,
            },
            points: 3000,
            rate: 3,
          },
          tierBonus: {
            tier: 'gold',
            points: 2000,
            rate: 2,
            multiplier: 1.67,
          },
          promotionalBonus: {
            points: 5000,
            campaigns: [
              {
                code: 'DOUBLE-POINTS-2024',
                name: 'Double Points Campaign',
                multiplier: 2.0,
                bonusPoints: 5000,
              },
            ],
          },
        },
        memberStatus: {
          currentTier: 'gold',
          pointsToNextTier: 10000, // Points needed to reach platinum
          nextTier: 'platinum',
          tierProgress: 0.9,
        },
      };

      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.CAMPAIGNS_ACTIVE,
          uponReceiving: 'a request to calculate points earn with promotional campaign',
          withRequest: {
            method: 'POST',
            path: '/points/calculate-earn',
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
            body: promotionalEarnRequest,
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedPromotionalEarnCalculation,
          },
        });
      });

      it('should calculate points earn with promotional campaign', async () => {
        const response = await axios({
          method: 'POST',
          url: `${mockServerBaseUrl}/points/calculate-earn`,
          headers: {
            [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          data: promotionalEarnRequest,
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toMatchObject({
          calculation: {
            basePoints: 3000,
            bonusPoints: 2000,
            promotionalPoints: 5000,
            totalPoints: 10000,
            earnRate: {
              totalPointsPerDollar: 10,
            },
          },
          breakdown: {
            promotionalBonus: {
              points: 5000,
              campaigns: expect.arrayContaining([
                expect.objectContaining({
                  code: 'DOUBLE-POINTS-2024',
                  name: 'Double Points Campaign',
                  multiplier: 2.0,
                  bonusPoints: 5000,
                }),
              ]),
            },
          },
        });
      });
    });
  });

  describe('POST /points/process-combination-payment - Process Points and Cash Payment', () => {
    describe('successful combination payment processing', () => {
      const combinationPaymentRequest = {
        bookingReference: 'booking-combo-123',
        memberId: TEST_DATA.MEMBER_IDS.VALID,
        pointsPayment: {
          amount: 50000, // 50,000 points
          cashEquivalent: {
            value: 28000, // 280.00 in cents
            currency: TEST_DATA.CURRENCIES.AUD,
          },
        },
        cashPayment: {
          amount: {
            value: 17000, // 170.00 in cents
            currency: TEST_DATA.CURRENCIES.AUD,
          },
          paymentMethod: {
            type: 'scheme',
            encryptedCardNumber: 'adyenjs_0_1_25$encrypted-card-number',
            encryptedExpiryMonth: 'adyenjs_0_1_25$encrypted-expiry-month',
            encryptedExpiryYear: 'adyenjs_0_1_25$encrypted-expiry-year',
            encryptedSecurityCode: 'adyenjs_0_1_25$encrypted-security-code',
            holderName: 'John Doe',
          },
        },
        totalAmount: {
          value: 45000, // 450.00 in cents
          currency: TEST_DATA.CURRENCIES.AUD,
        },
      };

      const expectedCombinationPaymentResponse = {
        paymentResult: {
          status: 'completed',
          bookingReference: 'booking-combo-123',
          totalAmount: {
            value: 45000,
            currency: TEST_DATA.CURRENCIES.AUD,
          },
          pointsTransaction: {
            transactionId: Matchers.somethingLike('pts-txn-789'),
            pointsDeducted: 50000,
            cashEquivalent: {
              value: 28000,
              currency: TEST_DATA.CURRENCIES.AUD,
            },
            memberBalance: {
              previousBalance: 150000,
              newBalance: 100000,
            },
            status: 'completed',
          },
          cashTransaction: {
            pspReference: Matchers.somethingLike('8816178914218518'),
            resultCode: 'Authorised',
            amount: {
              value: 17000,
              currency: TEST_DATA.CURRENCIES.AUD,
            },
            paymentMethod: {
              type: 'scheme',
              brand: 'visa',
            },
            status: 'completed',
          },
          breakdown: {
            pointsContribution: {
              value: 28000,
              currency: TEST_DATA.CURRENCIES.AUD,
              percentage: 62.2,
            },
            cashContribution: {
              value: 17000,
              currency: TEST_DATA.CURRENCIES.AUD,
              percentage: 37.8,
            },
          },
        },
        confirmationDetails: {
          confirmationNumber: Matchers.somethingLike('QH-COMBO-456789'),
          processedAt: Matchers.iso8601DateTime(),
        },
      };

      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.PAYMENTS.PAYMENT_CAN_BE_PROCESSED,
          uponReceiving: 'a request to process combination points and cash payment',
          withRequest: {
            method: 'POST',
            path: '/points/process-combination-payment',
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
            body: combinationPaymentRequest,
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedCombinationPaymentResponse,
          },
        });
      });

      it('should process combination payment successfully', async () => {
        const response = await axios({
          method: 'POST',
          url: `${mockServerBaseUrl}/points/process-combination-payment`,
          headers: {
            [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          data: combinationPaymentRequest,
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toMatchObject({
          paymentResult: {
            status: 'completed',
            bookingReference: 'booking-combo-123',
            totalAmount: {
              value: 45000,
              currency: TEST_DATA.CURRENCIES.AUD,
            },
            pointsTransaction: {
              pointsDeducted: 50000,
              cashEquivalent: {
                value: 28000,
                currency: TEST_DATA.CURRENCIES.AUD,
              },
              memberBalance: {
                previousBalance: 150000,
                newBalance: 100000,
              },
              status: 'completed',
            },
            cashTransaction: {
              resultCode: 'Authorised',
              amount: {
                value: 17000,
                currency: TEST_DATA.CURRENCIES.AUD,
              },
              paymentMethod: {
                type: 'scheme',
                brand: 'visa',
              },
              status: 'completed',
            },
            breakdown: {
              pointsContribution: {
                value: 28000,
                currency: TEST_DATA.CURRENCIES.AUD,
              },
              cashContribution: {
                value: 17000,
                currency: TEST_DATA.CURRENCIES.AUD,
              },
            },
          },
        });
        expect(response.data.paymentResult.pointsTransaction.transactionId).toBeDefined();
        expect(response.data.paymentResult.cashTransaction.pspReference).toBeDefined();
        expect(response.data.confirmationDetails.confirmationNumber).toBeDefined();
        expect(response.data.confirmationDetails.processedAt).toBeDefined();
      });
    });

    describe('combination payment with points transaction failure', () => {
      const failedPointsPaymentRequest = {
        bookingReference: 'booking-failed-points',
        memberId: TEST_DATA.MEMBER_IDS.VALID,
        pointsPayment: {
          amount: 200000, // 200,000 points (insufficient balance)
          cashEquivalent: {
            value: 112000, // 1120.00 in cents
            currency: TEST_DATA.CURRENCIES.AUD,
          },
        },
        cashPayment: {
          amount: {
            value: 33000, // 330.00 in cents
            currency: TEST_DATA.CURRENCIES.AUD,
          },
          paymentMethod: {
            type: 'scheme',
            encryptedCardNumber: 'adyenjs_0_1_25$encrypted-card-number',
            encryptedExpiryMonth: 'adyenjs_0_1_25$encrypted-expiry-month',
            encryptedExpiryYear: 'adyenjs_0_1_25$encrypted-expiry-year',
            encryptedSecurityCode: 'adyenjs_0_1_25$encrypted-security-code',
            holderName: 'John Doe',
          },
        },
        totalAmount: {
          value: 145000, // 1450.00 in cents
          currency: TEST_DATA.CURRENCIES.AUD,
        },
      };

      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.PAYMENTS.PAYMENT_WILL_FAIL,
          uponReceiving: 'a request to process combination payment with insufficient points',
          withRequest: {
            method: 'POST',
            path: '/points/process-combination-payment',
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
            body: failedPointsPaymentRequest,
          },
          willRespondWith: {
            status: HTTP_STATUS.UNPROCESSABLE_ENTITY,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'POINTS_TRANSACTION_FAILED',
                message: 'Points transaction failed due to insufficient balance',
                details: {
                  pointsRequested: 200000,
                  pointsAvailable: 150000,
                  shortfall: 50000,
                  transactionId: null,
                  cashTransactionStatus: 'not_attempted',
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 422 for points transaction failure', async () => {
        try {
          await axios({
            method: 'POST',
            url: `${mockServerBaseUrl}/points/process-combination-payment`,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
            data: failedPointsPaymentRequest,
          });
          fail('Expected request to fail with 422');
        } catch (error) {
          expect(error.response?.status).toBe(HTTP_STATUS.UNPROCESSABLE_ENTITY);
          if (error.response) {
            expect(error.response.data).toMatchObject({
              error: {
                code: 'POINTS_TRANSACTION_FAILED',
                message: 'Points transaction failed due to insufficient balance',
                details: {
                  pointsRequested: 200000,
                  pointsAvailable: 150000,
                  shortfall: 50000,
                  cashTransactionStatus: 'not_attempted',
                },
              },
            });
          }
        }
      });
    });
  });
});
