/**
 * Contract tests for Adyen payment integration endpoints
 * Tests the consumer contract for payment methods retrieval and payment processing
 */

const { Matchers } = require('@pact-foundation/pact');
const { createContractTestSuite, createMockServerUrlGetter, addInteractionSafely } = require('../helpers/testRunner');

const axios = require('axios');
const { API_PATHS, HTTP_STATUS, HTTP_HEADERS, CONTENT_TYPES, TEST_DATA, PROVIDER_STATES } = require('../shared/constants');

// Define fail function for test assertions
const fail = (message) => {
  throw new Error(message);
};

createContractTestSuite('Adyen Payment Integration Contract Tests', 'adyen-payment-service', (contractTest) => {
  const getMockServerUrl = createMockServerUrlGetter(contractTest);

  describe('POST /qepg/get-payment-methods - Retrieve Payment Methods', () => {
    describe('successful payment methods retrieval', () => {
      const paymentMethodsRequest = {
        memberId: TEST_DATA.MEMBER_IDS.VALID,
        amount: {
          value: 100,
          currency: TEST_DATA.CURRENCIES.AUD,
        },
      };

      const expectedPaymentMethods = {
        paymentMethods: [
          {
            type: 'scheme',
            name: 'Credit Card',
            brands: ['visa', 'mc', 'amex', 'uatp'],
          },
          {
            type: 'applepay',
            name: 'Apple Pay',
            configuration: {
              merchantId: Matchers.somethingLike('***************'),
              merchantName: 'Qantas Loyalty',
            },
          },
          {
            type: 'googlepay',
            name: 'Google Pay',
            configuration: {
              merchantId: Matchers.somethingLike('***************'),
              merchantName: 'Qantas Loyalty',
            },
          },
        ],
        storedPaymentMethods: null,
        qepgVault: {
          memberId: TEST_DATA.MEMBER_IDS.VALID,
          accountType: 'qff',
        },
      };

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.PAYMENTS.PAYMENT_METHODS_AVAILABLE,
          uponReceiving: 'a request to get payment methods',
          withRequest: {
            method: 'POST',
            path: '/qepg/get-payment-methods',
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: paymentMethodsRequest,
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedPaymentMethods,
          },
        });
      });

      it('should retrieve payment methods successfully', async () => {
        const response = await axios({
          method: 'POST',
          url: `${getMockServerUrl()}/qepg/get-payment-methods`,
          headers: {
            [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
          },
          data: paymentMethodsRequest,
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toMatchObject({
          paymentMethods: expect.arrayContaining([
            expect.objectContaining({
              type: 'scheme',
              name: 'Credit Card',
              brands: expect.arrayContaining(['visa', 'mc', 'amex']),
            }),
            expect.objectContaining({
              type: 'applepay',
              name: 'Apple Pay',
              configuration: expect.objectContaining({
                merchantId: expect.any(String),
                merchantName: 'Qantas Loyalty',
              }),
            }),
          ]),
          qepgVault: {
            memberId: TEST_DATA.MEMBER_IDS.VALID,
            accountType: 'qff',
          },
        });
        expect(response.data.storedPaymentMethods).toBeNull();
      });
    });

    describe('payment methods with stored cards', () => {
      const paymentMethodsRequest = {
        memberId: TEST_DATA.MEMBER_IDS.VALID,
        amount: {
          value: 250,
          currency: TEST_DATA.CURRENCIES.AUD,
        },
      };

      const expectedPaymentMethodsWithStored = {
        paymentMethods: [
          {
            type: 'scheme',
            name: 'Credit Card',
            brands: ['visa', 'mc', 'amex', 'uatp'],
          },
        ],
        storedPaymentMethods: [
          {
            id: 'stored-card-123',
            type: 'scheme',
            name: 'VISA ending in 1234',
            brand: 'visa',
            lastFour: '1234',
            expiryMonth: '12',
            expiryYear: '2025',
            holderName: 'John Doe',
            supportedShopperInteractions: ['Ecommerce'],
            storedPaymentMethodId: 'stored-card-123',
          },
          {
            id: 'stored-card-456',
            type: 'scheme',
            name: 'MASTERCARD ending in 5678',
            brand: 'mc',
            lastFour: '5678',
            expiryMonth: '06',
            expiryYear: '2026',
            holderName: 'John Doe',
            supportedShopperInteractions: ['Ecommerce'],
            storedPaymentMethodId: 'stored-card-456',
          },
        ],
        qepgVault: {
          memberId: TEST_DATA.MEMBER_IDS.VALID,
          accountType: 'qff',
        },
      };

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.PAYMENTS.PAYMENT_METHODS_AVAILABLE,
          uponReceiving: 'a request to get payment methods with stored cards',
          withRequest: {
            method: 'POST',
            path: '/qepg/get-payment-methods',
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: paymentMethodsRequest,
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedPaymentMethodsWithStored,
          },
        });
      });

      it('should retrieve payment methods with stored cards', async () => {
        const response = await axios({
          method: 'POST',
          url: `${getMockServerUrl()}/qepg/get-payment-methods`,
          headers: {
            [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
          },
          data: paymentMethodsRequest,
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data.storedPaymentMethods).toHaveLength(2);
        expect(response.data.storedPaymentMethods[0]).toMatchObject({
          id: 'stored-card-123',
          type: 'scheme',
          brand: 'visa',
          lastFour: '1234',
          expiryMonth: '12',
          expiryYear: '2025',
          holderName: 'John Doe',
          storedPaymentMethodId: 'stored-card-123',
        });
      });
    });

    describe('payment methods retrieval with invalid member', () => {
      const invalidRequest = {
        memberId: TEST_DATA.MEMBER_IDS.INVALID,
        amount: {
          value: 100,
          currency: TEST_DATA.CURRENCIES.AUD,
        },
      };

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.AUTH.TOKEN_INVALID,
          uponReceiving: 'a request to get payment methods with invalid member',
          withRequest: {
            method: 'POST',
            path: '/qepg/get-payment-methods',
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: invalidRequest,
          },
          willRespondWith: {
            status: HTTP_STATUS.BAD_REQUEST,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'INVALID_MEMBER_ID',
                message: 'Invalid member ID provided',
                details: {
                  memberId: TEST_DATA.MEMBER_IDS.INVALID,
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 400 for invalid member ID', async () => {
        try {
          await axios({
            method: 'POST',
            url: `${getMockServerUrl()}/qepg/get-payment-methods`,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            data: invalidRequest,
          });
          fail('Expected request to fail with 400');
        } catch (error) {
          expect(error.response?.status).toBe(HTTP_STATUS.BAD_REQUEST);
          if (error.response) {
            expect(error.response.data).toMatchObject({
              error: {
                code: 'INVALID_MEMBER_ID',
                message: 'Invalid member ID provided',
                details: {
                  memberId: TEST_DATA.MEMBER_IDS.INVALID,
                },
              },
            });
            expect(error.response.data.timestamp).toBeDefined();
            expect(error.response.data.requestId).toBeDefined();
          }
        }
      });
    });
  });

  describe('POST /payments - Process Payment', () => {
    describe('successful payment processing', () => {
      const paymentRequest = {
        amount: {
          value: 45000, // 450.00 in cents
          currency: TEST_DATA.CURRENCIES.AUD,
        },
        paymentMethod: {
          type: 'scheme',
          encryptedCardNumber: 'adyenjs_0_1_25$encrypted-card-number',
          encryptedExpiryMonth: 'adyenjs_0_1_25$encrypted-expiry-month',
          encryptedExpiryYear: 'adyenjs_0_1_25$encrypted-expiry-year',
          encryptedSecurityCode: 'adyenjs_0_1_25$encrypted-security-code',
          holderName: 'John Doe',
        },
        reference: 'booking-789',
        merchantAccount: 'QantasLoyaltyAU',
        returnUrl: 'https://hotels.qantas.com/checkout/payment-result',
        shopperReference: TEST_DATA.MEMBER_IDS.VALID,
        storePaymentMethod: false,
      };

      const expectedPaymentResponse = {
        pspReference: Matchers.somethingLike('****************'),
        resultCode: 'Authorised',
        amount: {
          value: 45000,
          currency: TEST_DATA.CURRENCIES.AUD,
        },
        merchantReference: 'booking-789',
        paymentMethod: {
          type: 'scheme',
          brand: 'visa',
        },
        additionalData: {
          cardSummary: '1234',
          expiryDate: '12/2025',
          authCode: Matchers.somethingLike('123456'),
        },
      };

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.PAYMENTS.PAYMENT_CAN_BE_PROCESSED,
          uponReceiving: 'a request to process payment',
          withRequest: {
            method: 'POST',
            path: API_PATHS.PAYMENTS.PROCESS,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
            body: paymentRequest,
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedPaymentResponse,
          },
        });
      });

      it('should process payment successfully', async () => {
        const response = await axios({
          method: 'POST',
          url: `${getMockServerUrl()}${API_PATHS.PAYMENTS.PROCESS}`,
          headers: {
            [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          data: paymentRequest,
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toMatchObject({
          resultCode: 'Authorised',
          amount: {
            value: 45000,
            currency: TEST_DATA.CURRENCIES.AUD,
          },
          merchantReference: 'booking-789',
          paymentMethod: {
            type: 'scheme',
            brand: 'visa',
          },
          additionalData: {
            cardSummary: '1234',
            expiryDate: '12/2025',
          },
        });
        expect(response.data.pspReference).toBeDefined();
        expect(response.data.additionalData.authCode).toBeDefined();
      });
    });

    describe('payment processing with stored card', () => {
      const storedCardPaymentRequest = {
        amount: {
          value: 25000, // 250.00 in cents
          currency: TEST_DATA.CURRENCIES.AUD,
        },
        paymentMethod: {
          type: 'scheme',
          storedPaymentMethodId: 'stored-card-123',
          encryptedSecurityCode: 'adyenjs_0_1_25$encrypted-security-code',
        },
        reference: 'booking-456',
        merchantAccount: 'QantasLoyaltyAU',
        returnUrl: 'https://hotels.qantas.com/checkout/payment-result',
        shopperReference: TEST_DATA.MEMBER_IDS.VALID,
        shopperInteraction: 'Ecommerce',
        recurringProcessingModel: 'Subscription',
      };

      const expectedStoredCardResponse = {
        pspReference: Matchers.somethingLike('****************'),
        resultCode: 'Authorised',
        amount: {
          value: 25000,
          currency: TEST_DATA.CURRENCIES.AUD,
        },
        merchantReference: 'booking-456',
        paymentMethod: {
          type: 'scheme',
          brand: 'visa',
        },
        additionalData: {
          cardSummary: '1234',
          expiryDate: '12/2025',
          authCode: Matchers.somethingLike('654321'),
          recurring: 'true',
        },
      };

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.PAYMENTS.PAYMENT_CAN_BE_PROCESSED,
          uponReceiving: 'a request to process payment with stored card',
          withRequest: {
            method: 'POST',
            path: API_PATHS.PAYMENTS.PROCESS,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
            body: storedCardPaymentRequest,
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedStoredCardResponse,
          },
        });
      });

      it('should process stored card payment successfully', async () => {
        const response = await axios({
          method: 'POST',
          url: `${getMockServerUrl()}${API_PATHS.PAYMENTS.PROCESS}`,
          headers: {
            [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
          data: storedCardPaymentRequest,
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toMatchObject({
          resultCode: 'Authorised',
          amount: {
            value: 25000,
            currency: TEST_DATA.CURRENCIES.AUD,
          },
          merchantReference: 'booking-456',
          paymentMethod: {
            type: 'scheme',
            brand: 'visa',
          },
          additionalData: {
            cardSummary: '1234',
            expiryDate: '12/2025',
            recurring: 'true',
          },
        });
        expect(response.data.pspReference).toBeDefined();
        expect(response.data.additionalData.authCode).toBeDefined();
      });
    });

    describe('payment processing failure scenarios', () => {
      describe('insufficient funds', () => {
        const insufficientFundsRequest = {
          amount: {
            value: 100000, // 1000.00 in cents
            currency: TEST_DATA.CURRENCIES.AUD,
          },
          paymentMethod: {
            type: 'scheme',
            encryptedCardNumber: 'adyenjs_0_1_25$encrypted-card-number',
            encryptedExpiryMonth: 'adyenjs_0_1_25$encrypted-expiry-month',
            encryptedExpiryYear: 'adyenjs_0_1_25$encrypted-expiry-year',
            encryptedSecurityCode: 'adyenjs_0_1_25$encrypted-security-code',
            holderName: 'John Doe',
          },
          reference: 'booking-failed',
          merchantAccount: 'QantasLoyaltyAU',
          returnUrl: 'https://hotels.qantas.com/checkout/payment-result',
          shopperReference: TEST_DATA.MEMBER_IDS.VALID,
        };

        beforeEach(async () => {
          await addInteractionSafely(contractTest, {
            state: PROVIDER_STATES.PAYMENTS.PAYMENT_WILL_FAIL,
            uponReceiving: 'a request to process payment with insufficient funds',
            withRequest: {
              method: 'POST',
              path: API_PATHS.PAYMENTS.PROCESS,
              headers: {
                [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
                [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              },
              body: insufficientFundsRequest,
            },
            willRespondWith: {
              status: HTTP_STATUS.OK,
              headers: {
                [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              },
              body: {
                pspReference: Matchers.somethingLike('8816178914218517'),
                resultCode: 'Refused',
                refusalReason: 'Insufficient funds',
                amount: {
                  value: 100000,
                  currency: TEST_DATA.CURRENCIES.AUD,
                },
                merchantReference: 'booking-failed',
                paymentMethod: {
                  type: 'scheme',
                  brand: 'visa',
                },
                additionalData: {
                  cardSummary: '1234',
                  refusalReasonRaw: '05 : Do not honor',
                },
              },
            },
          });
        });

        it('should handle insufficient funds payment failure', async () => {
          const response = await axios({
            method: 'POST',
            url: `${getMockServerUrl()}${API_PATHS.PAYMENTS.PROCESS}`,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
            data: insufficientFundsRequest,
          });

          expect(response.status).toBe(HTTP_STATUS.OK);
          expect(response.data).toMatchObject({
            resultCode: 'Refused',
            refusalReason: 'Insufficient funds',
            amount: {
              value: 100000,
              currency: TEST_DATA.CURRENCIES.AUD,
            },
            merchantReference: 'booking-failed',
            paymentMethod: {
              type: 'scheme',
              brand: 'visa',
            },
            additionalData: {
              cardSummary: '1234',
              refusalReasonRaw: '05 : Do not honor',
            },
          });
          expect(response.data.pspReference).toBeDefined();
        });
      });

      describe('invalid card details', () => {
        const invalidCardRequest = {
          amount: {
            value: 30000, // 300.00 in cents
            currency: TEST_DATA.CURRENCIES.AUD,
          },
          paymentMethod: {
            type: 'scheme',
            encryptedCardNumber: 'adyenjs_0_1_25$invalid-card-number',
            encryptedExpiryMonth: 'adyenjs_0_1_25$invalid-expiry-month',
            encryptedExpiryYear: 'adyenjs_0_1_25$invalid-expiry-year',
            encryptedSecurityCode: 'adyenjs_0_1_25$invalid-security-code',
            holderName: 'John Doe',
          },
          reference: 'booking-invalid',
          merchantAccount: 'QantasLoyaltyAU',
          returnUrl: 'https://hotels.qantas.com/checkout/payment-result',
          shopperReference: TEST_DATA.MEMBER_IDS.VALID,
        };

        beforeEach(async () => {
          await addInteractionSafely(contractTest, {
            state: PROVIDER_STATES.PAYMENTS.PAYMENT_WILL_FAIL,
            uponReceiving: 'a request to process payment with invalid card',
            withRequest: {
              method: 'POST',
              path: API_PATHS.PAYMENTS.PROCESS,
              headers: {
                [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
                [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              },
              body: invalidCardRequest,
            },
            willRespondWith: {
              status: HTTP_STATUS.UNPROCESSABLE_ENTITY,
              headers: {
                [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
              },
              body: {
                error: {
                  code: 'INVALID_CARD_DATA',
                  message: 'Invalid card data provided',
                  details: {
                    fieldErrors: [
                      {
                        field: 'encryptedCardNumber',
                        message: 'Invalid card number format',
                      },
                      {
                        field: 'encryptedExpiryMonth',
                        message: 'Invalid expiry month',
                      },
                    ],
                  },
                },
                timestamp: Matchers.iso8601DateTime(),
                requestId: Matchers.uuid(),
              },
            },
          });
        });

        it('should return 422 for invalid card details', async () => {
          try {
            await axios({
              method: 'POST',
              url: `${getMockServerUrl()}${API_PATHS.PAYMENTS.PROCESS}`,
              headers: {
                [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
                [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
              },
              data: invalidCardRequest,
            });
            fail('Expected request to fail with 422');
          } catch (error) {
            expect(error.response?.status).toBe(HTTP_STATUS.UNPROCESSABLE_ENTITY);
            if (error.response) {
              expect(error.response.data).toMatchObject({
                error: {
                  code: 'INVALID_CARD_DATA',
                  message: 'Invalid card data provided',
                  details: {
                    fieldErrors: expect.arrayContaining([
                      expect.objectContaining({
                        field: 'encryptedCardNumber',
                        message: 'Invalid card number format',
                      }),
                    ]),
                  },
                },
              });
              expect(error.response.data.timestamp).toBeDefined();
              expect(error.response.data.requestId).toBeDefined();
            }
          }
        });
      });
    });
  });

  describe('GET /payment-status/{pspReference} - Check Payment Status', () => {
    describe('successful payment status check', () => {
      const pspReference = '****************';

      const expectedPaymentStatus = {
        pspReference: pspReference,
        status: 'Authorised',
        amount: {
          value: 45000,
          currency: TEST_DATA.CURRENCIES.AUD,
        },
        merchantReference: 'booking-789',
        paymentMethod: {
          type: 'scheme',
          brand: 'visa',
        },
        createdAt: Matchers.iso8601DateTime(),
        lastUpdated: Matchers.iso8601DateTime(),
      };

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.PAYMENTS.PAYMENT_CAN_BE_PROCESSED,
          uponReceiving: 'a request to check payment status',
          withRequest: {
            method: 'GET',
            path: `/payment-status/${pspReference}`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: expectedPaymentStatus,
          },
        });
      });

      it('should check payment status successfully', async () => {
        const response = await axios({
          method: 'GET',
          url: `${getMockServerUrl()}/payment-status/${pspReference}`,
          headers: {
            [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
          },
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toMatchObject({
          pspReference: pspReference,
          status: 'Authorised',
          amount: {
            value: 45000,
            currency: TEST_DATA.CURRENCIES.AUD,
          },
          merchantReference: 'booking-789',
          paymentMethod: {
            type: 'scheme',
            brand: 'visa',
          },
        });
        expect(response.data.createdAt).toBeDefined();
        expect(response.data.lastUpdated).toBeDefined();
      });
    });

    describe('payment status not found', () => {
      const invalidPspReference = 'invalid-psp-reference';

      beforeEach(async () => {
        await addInteractionSafely(contractTest, {
          state: PROVIDER_STATES.PAYMENTS.PAYMENT_WILL_FAIL,
          uponReceiving: 'a request to check non-existent payment status',
          withRequest: {
            method: 'GET',
            path: `/payment-status/${invalidPspReference}`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.NOT_FOUND,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'PAYMENT_NOT_FOUND',
                message: 'Payment not found',
                details: {
                  pspReference: invalidPspReference,
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 404 for non-existent payment', async () => {
        try {
          await axios({
            method: 'GET',
            url: `${getMockServerUrl()}/payment-status/${invalidPspReference}`,
            headers: {
              [HTTP_HEADERS.AUTHORIZATION]: 'Bearer valid-token',
            },
          });
          fail('Expected request to fail with 404');
        } catch (error) {
          expect(error.response?.status).toBe(HTTP_STATUS.NOT_FOUND);
          if (error.response) {
            expect(error.response.data).toMatchObject({
              error: {
                code: 'PAYMENT_NOT_FOUND',
                message: 'Payment not found',
                details: {
                  pspReference: invalidPspReference,
                },
              },
            });
            expect(error.response.data.timestamp).toBeDefined();
            expect(error.response.data.requestId).toBeDefined();
          }
        }
      });
    });
  });
});
