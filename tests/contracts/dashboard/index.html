<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contract Test Maintenance Dashboard</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .dashboard {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            color: white;
            font-weight: bold;
            font-size: 14px;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }
        .metric-label {
            color: #6c757d;
            font-size: 14px;
        }
        .alerts-section, .recommendations-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .alert {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .alert.critical { border-color: #dc3545; background-color: #f8d7da; }
        .alert.high { border-color: #fd7e14; background-color: #fff3cd; }
        .alert.medium { border-color: #ffc107; background-color: #fff3cd; }
        .alert.low { border-color: #17a2b8; background-color: #d1ecf1; }
        .recommendation {
            padding: 15px;
            margin: 10px 0;
            border: 1px solid #dee2e6;
            border-radius: 4px;
        }
        .last-updated {
            color: #6c757d;
            font-size: 12px;
        }
        .refresh-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
    <script>
        function refreshDashboard() {
            location.reload();
        }
        
        // Auto-refresh every 5 minutes
        setTimeout(refreshDashboard, 300000);
    </script>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <h1>🎛️ Contract Test Maintenance Dashboard</h1>
            <div>
                <span class="status-badge" style="background-color: #ffc107">
                    ⚠️ WARNING
                </span>
                <button class="refresh-btn" onclick="refreshDashboard()">🔄 Refresh</button>
            </div>
            <div class="last-updated">
                Last updated: 9/4/2025, 11:30:51 PM
            </div>
        </div>

        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-label">Success Rate</div>
                <div class="metric-value" style="color: #28a745">
                    95.8%
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Execution Time</div>
                <div class="metric-value">
                    24s
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Endpoint Coverage</div>
                <div class="metric-value" style="color: #ffc107">
                    89%
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Flakiness Rate</div>
                <div class="metric-value" style="color: #28a745">
                    2.1%
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Total Tests</div>
                <div class="metric-value">
                    111
                </div>
            </div>
            <div class="metric-card">
                <div class="metric-label">Current Version</div>
                <div class="metric-value" style="font-size: 1.5em;">
                    1.0.0
                </div>
            </div>
        </div>

        
        <div class="alerts-section">
            <h2>🚨 Active Alerts</h2>
            
                <div class="alert low">
                    <strong>LOW:</strong> Endpoint coverage (88.6%) below target
                    <div style="font-size: 12px; color: #6c757d; margin-top: 5px;">
                        Component: coverage | 9/4/2025, 11:30:51 PM
                    </div>
                </div>
            
        </div>
        

        
        <div class="recommendations-section">
            <h2>💡 Recommendations</h2>
            
                <div class="recommendation">
                    <h4>Reliability - MEDIUM Priority</h4>
                    <p>Improve test reliability</p>
                    <ul>
                        <li>Investigate failing tests</li><li>Improve test stability</li><li>Add retry logic</li>
                    </ul>
                </div>
            
                <div class="recommendation">
                    <h4>Coverage - MEDIUM Priority</h4>
                    <p>Increase API endpoint coverage</p>
                    <ul>
                        <li>Identify uncovered endpoints</li><li>Add missing contract tests</li><li>Review new API changes</li>
                    </ul>
                </div>
            
                <div class="recommendation">
                    <h4>Stability - MEDIUM Priority</h4>
                    <p>Reduce test flakiness</p>
                    <ul>
                        <li>Fix flaky tests</li><li>Improve test isolation</li><li>Optimize mock services</li>
                    </ul>
                </div>
            
        </div>
        

        <div class="metric-card">
            <h3>📊 System Information</h3>
            <p><strong>Health Status:</strong> healthy</p>
            <p><strong>Tests Running:</strong> Yes</p>
            <p><strong>Contracts Generated:</strong> 5</p>
            <p><strong>Compatibility:</strong> compatible</p>
            <p><strong>Total Versions:</strong> 1</p>
        </div>
    </div>
</body>
</html>