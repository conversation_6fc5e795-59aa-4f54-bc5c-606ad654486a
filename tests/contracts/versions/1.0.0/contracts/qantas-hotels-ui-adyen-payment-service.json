{"consumer": {"name": "qantas-hotels-ui"}, "interactions": [{"description": "a request to check non-existent payment status", "providerState": "payment will fail", "request": {"headers": {"Authorization": "Bearer valid-token"}, "method": "GET", "path": "/payment-status/invalid-psp-reference"}, "response": {"body": {"error": {"code": "PAYMENT_NOT_FOUND", "details": {"pspReference": "invalid-psp-reference"}, "message": "Payment not found"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 404}}, {"description": "a request to check payment status", "providerState": "payment can be processed", "request": {"headers": {"Authorization": "Bearer valid-token"}, "method": "GET", "path": "/payment-status/****************"}, "response": {"body": {"amount": {"currency": "AUD", "value": 45000}, "createdAt": "2015-08-06T16:53:10+01:00", "lastUpdated": "2015-08-06T16:53:10+01:00", "merchantReference": "booking-789", "paymentMethod": {"brand": "visa", "type": "scheme"}, "pspReference": "****************", "status": "Authorised"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.createdAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.lastUpdated": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request to get payment methods", "providerState": "payment methods are available", "request": {"body": {"amount": {"currency": "AUD", "value": 100}, "memberId": "********"}, "headers": {"Content-Type": "application/json"}, "method": "POST", "path": "/qepg/get-payment-methods"}, "response": {"body": {"paymentMethods": [{"brands": ["visa", "mc", "amex", "uatp"], "name": "Credit Card", "type": "scheme"}, {"configuration": {"merchantId": "***************", "merchantName": "Qantas Loyalty"}, "name": "Apple Pay", "type": "applepay"}, {"configuration": {"merchantId": "***************", "merchantName": "Qantas Loyalty"}, "name": "Google Pay", "type": "googlepay"}], "qepgVault": {"accountType": "qff", "memberId": "********"}, "storedPaymentMethods": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.paymentMethods[1].configuration.merchantId": {"match": "type"}, "$.body.paymentMethods[2].configuration.merchantId": {"match": "type"}}, "status": 200}}, {"description": "a request to get payment methods with invalid member", "providerState": "user has invalid authentication token", "request": {"body": {"amount": {"currency": "AUD", "value": 100}, "memberId": "invalid-member"}, "headers": {"Content-Type": "application/json"}, "method": "POST", "path": "/qepg/get-payment-methods"}, "response": {"body": {"error": {"code": "INVALID_MEMBER_ID", "details": {"memberId": "invalid-member"}, "message": "Invalid member ID provided"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 400}}, {"description": "a request to get payment methods with stored cards", "providerState": "payment methods are available", "request": {"body": {"amount": {"currency": "AUD", "value": 250}, "memberId": "********"}, "headers": {"Content-Type": "application/json"}, "method": "POST", "path": "/qepg/get-payment-methods"}, "response": {"body": {"paymentMethods": [{"brands": ["visa", "mc", "amex", "uatp"], "name": "Credit Card", "type": "scheme"}], "qepgVault": {"accountType": "qff", "memberId": "********"}, "storedPaymentMethods": [{"brand": "visa", "expiryMonth": "12", "expiryYear": "2025", "holderName": "<PERSON>", "id": "stored-card-123", "lastFour": "1234", "name": "VISA ending in 1234", "storedPaymentMethodId": "stored-card-123", "supportedShopperInteractions": ["Ecommerce"], "type": "scheme"}, {"brand": "mc", "expiryMonth": "06", "expiryYear": "2026", "holderName": "<PERSON>", "id": "stored-card-456", "lastFour": "5678", "name": "MASTERCARD ending in 5678", "storedPaymentMethodId": "stored-card-456", "supportedShopperInteractions": ["Ecommerce"], "type": "scheme"}]}, "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to process payment", "providerState": "payment can be processed", "request": {"body": {"amount": {"currency": "AUD", "value": 45000}, "merchantAccount": "QantasLoyaltyAU", "paymentMethod": {"encryptedCardNumber": "adyenjs_0_1_25$encrypted-card-number", "encryptedExpiryMonth": "adyenjs_0_1_25$encrypted-expiry-month", "encryptedExpiryYear": "adyenjs_0_1_25$encrypted-expiry-year", "encryptedSecurityCode": "adyenjs_0_1_25$encrypted-security-code", "holderName": "<PERSON>", "type": "scheme"}, "reference": "booking-789", "returnUrl": "https://hotels.qantas.com/checkout/payment-result", "shopperReference": "********", "storePaymentMethod": false}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/payments"}, "response": {"body": {"additionalData": {"authCode": "123456", "cardSummary": "1234", "expiryDate": "12/2025"}, "amount": {"currency": "AUD", "value": 45000}, "merchantReference": "booking-789", "paymentMethod": {"brand": "visa", "type": "scheme"}, "pspReference": "****************", "resultCode": "Authorised"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.additionalData.authCode": {"match": "type"}, "$.body.pspReference": {"match": "type"}}, "status": 200}}, {"description": "a request to process payment with insufficient funds", "providerState": "payment will fail", "request": {"body": {"amount": {"currency": "AUD", "value": 100000}, "merchantAccount": "QantasLoyaltyAU", "paymentMethod": {"encryptedCardNumber": "adyenjs_0_1_25$encrypted-card-number", "encryptedExpiryMonth": "adyenjs_0_1_25$encrypted-expiry-month", "encryptedExpiryYear": "adyenjs_0_1_25$encrypted-expiry-year", "encryptedSecurityCode": "adyenjs_0_1_25$encrypted-security-code", "holderName": "<PERSON>", "type": "scheme"}, "reference": "booking-failed", "returnUrl": "https://hotels.qantas.com/checkout/payment-result", "shopperReference": "********"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/payments"}, "response": {"body": {"additionalData": {"cardSummary": "1234", "refusalReasonRaw": "05 : Do not honor"}, "amount": {"currency": "AUD", "value": 100000}, "merchantReference": "booking-failed", "paymentMethod": {"brand": "visa", "type": "scheme"}, "pspReference": "****************", "refusalReason": "Insufficient funds", "resultCode": "Refused"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.pspReference": {"match": "type"}}, "status": 200}}, {"description": "a request to process payment with invalid card", "providerState": "payment will fail", "request": {"body": {"amount": {"currency": "AUD", "value": 30000}, "merchantAccount": "QantasLoyaltyAU", "paymentMethod": {"encryptedCardNumber": "adyenjs_0_1_25$invalid-card-number", "encryptedExpiryMonth": "adyenjs_0_1_25$invalid-expiry-month", "encryptedExpiryYear": "adyenjs_0_1_25$invalid-expiry-year", "encryptedSecurityCode": "adyenjs_0_1_25$invalid-security-code", "holderName": "<PERSON>", "type": "scheme"}, "reference": "booking-invalid", "returnUrl": "https://hotels.qantas.com/checkout/payment-result", "shopperReference": "********"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/payments"}, "response": {"body": {"error": {"code": "INVALID_CARD_DATA", "details": {"fieldErrors": [{"field": "encryptedCardNumber", "message": "Invalid card number format"}, {"field": "encryptedExpiryMonth", "message": "Invalid expiry month"}]}, "message": "Invalid card data provided"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 422}}, {"description": "a request to process payment with stored card", "providerState": "payment can be processed", "request": {"body": {"amount": {"currency": "AUD", "value": 25000}, "merchantAccount": "QantasLoyaltyAU", "paymentMethod": {"encryptedSecurityCode": "adyenjs_0_1_25$encrypted-security-code", "storedPaymentMethodId": "stored-card-123", "type": "scheme"}, "recurringProcessingModel": "Subscription", "reference": "booking-456", "returnUrl": "https://hotels.qantas.com/checkout/payment-result", "shopperInteraction": "Ecommerce", "shopperReference": "********"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/payments"}, "response": {"body": {"additionalData": {"authCode": "654321", "cardSummary": "1234", "expiryDate": "12/2025", "recurring": "true"}, "amount": {"currency": "AUD", "value": 25000}, "merchantReference": "booking-456", "paymentMethod": {"brand": "visa", "type": "scheme"}, "pspReference": "****************", "resultCode": "Authorised"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.additionalData.authCode": {"match": "type"}, "$.body.pspReference": {"match": "type"}}, "status": 200}}], "metadata": {"pact-js": {"version": "15.0.1"}, "pactRust": {"ffi": "0.4.22", "models": "1.2.3"}, "pactSpecification": {"version": "2.0.0"}}, "provider": {"name": "adyen-payment-service"}}