{"consumer": {"name": "qantas-hotels-ui"}, "interactions": [{"description": "a request to detect user geolocation", "providerState": "user geolocation can be detected", "request": {"method": "GET", "path": "/geolocation"}, "response": {"body": {"accuracy": "city", "city": "Sydney", "confidence": 0.95, "coordinates": {"latitude": -33.8688, "longitude": 151.2093}, "country": "Australia", "countryCode": "AU", "detectionMethod": "ip_geolocation", "geolocation": "NSW", "ipAddress": "*************", "state": "New South Wales", "stateCode": "NSW", "timezone": "Australia/Sydney"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.accuracy": {"match": "type"}, "$.body.confidence": {"match": "type"}, "$.body.coordinates.latitude": {"match": "type"}, "$.body.coordinates.longitude": {"match": "type"}, "$.body.ipAddress": {"match": "type"}}, "status": 200}}, {"description": "a request to detect user geolocation when detection fails", "providerState": "user geolocation cannot be detected", "request": {"method": "GET", "path": "/geolocation"}, "response": {"body": {"error": {"code": "GEOLOCATION_UNAVAILABLE", "details": {"reason": "ip_geolocation_service_unavailable"}, "message": "Unable to determine user location"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 503}}, {"description": "a request to detect user geolocation with low accuracy", "providerState": "user geolocation has low accuracy", "request": {"method": "GET", "path": "/geolocation"}, "response": {"body": {"accuracy": "country", "city": null, "confidence": 0.65, "coordinates": {"latitude": -25.2744, "longitude": 133.7751}, "country": "Australia", "countryCode": "AU", "detectionMethod": "ip_geolocation", "geolocation": "Australia", "ipAddress": "*************", "state": null, "stateCode": null, "timezone": "Australia/Sydney"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.confidence": {"match": "type"}, "$.body.coordinates.latitude": {"match": "type"}, "$.body.coordinates.longitude": {"match": "type"}, "$.body.ipAddress": {"match": "type"}}, "status": 200}}, {"description": "a request to fetch boundaries for non-existent location", "providerState": "location boundaries do not exist", "request": {"method": "GET", "path": "/locations/location-nonexistent/boundaries"}, "response": {"body": {"error": {"code": "LOCATION_NOT_FOUND", "details": {"locationId": "location-nonexistent"}, "message": "Location not found"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 404}}, {"description": "a request to fetch location boundaries", "providerState": "location boundaries are available", "request": {"method": "GET", "path": "/locations/location-sydney-1/boundaries"}, "response": {"body": {"accuracy": "high", "area": 2058.7, "boundaries": {"coordinates": [[[151, -34], [151.5, -34], [151.5, -33.5], [151, -33.5], [151, -34]]], "type": "Polygon"}, "center": {"latitude": -33.8688, "longitude": 151.2093}, "location": {"displayName": "Sydney, NSW, Australia", "id": "location-sydney-1", "name": "Sydney"}, "radius": 25.5}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.area": {"match": "type"}, "$.body.boundaries.coordinates[0][0][0]": {"match": "type"}, "$.body.boundaries.coordinates[0][0][1]": {"match": "type"}, "$.body.boundaries.coordinates[0][1][0]": {"match": "type"}, "$.body.boundaries.coordinates[0][1][1]": {"match": "type"}, "$.body.boundaries.coordinates[0][2][0]": {"match": "type"}, "$.body.boundaries.coordinates[0][2][1]": {"match": "type"}, "$.body.boundaries.coordinates[0][3][0]": {"match": "type"}, "$.body.boundaries.coordinates[0][3][1]": {"match": "type"}, "$.body.boundaries.coordinates[0][4][0]": {"match": "type"}, "$.body.boundaries.coordinates[0][4][1]": {"match": "type"}, "$.body.center.latitude": {"match": "type"}, "$.body.center.longitude": {"match": "type"}, "$.body.radius": {"match": "type"}}, "status": 200}}, {"description": "a request to search locations by name", "providerState": "locations are available for search", "request": {"method": "GET", "path": "/locations", "query": "name=Sydney"}, "response": {"body": [{"coordinates": {"latitude": -33.8688, "longitude": 151.2093}, "country": "Australia", "countryCode": "AU", "displayName": "Sydney, NSW, Australia", "id": "location-1", "isActive": true, "name": "Sydney", "popularity": 95, "state": "NSW", "stateCode": "NSW", "timezone": "Australia/Sydney", "type": "city"}, {"coordinates": {"latitude": -33.8651, "longitude": 151.2099}, "country": "Australia", "countryCode": "AU", "displayName": "Sydney CBD, Sydney, NSW, Australia", "id": "location-2", "isActive": true, "name": "Sydney CBD", "parentLocation": {"id": "location-1", "name": "Sydney"}, "popularity": 88, "state": "NSW", "stateCode": "NSW", "timezone": "Australia/Sydney", "type": "district"}], "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body[0].coordinates.latitude": {"match": "type"}, "$.body[0].coordinates.longitude": {"match": "type"}, "$.body[0].id": {"match": "type"}, "$.body[0].popularity": {"match": "type"}, "$.body[1].coordinates.latitude": {"match": "type"}, "$.body[1].coordinates.longitude": {"match": "type"}, "$.body[1].id": {"match": "type"}, "$.body[1].parentLocation.id": {"match": "type"}, "$.body[1].popularity": {"match": "type"}}, "status": 200}}, {"description": "a request to search locations with invalid parameters", "providerState": "invalid search parameters provided", "request": {"method": "GET", "path": "/locations", "query": "name="}, "response": {"body": {"error": {"code": "INVALID_SEARCH_PARAMETERS", "details": {"parameter": "name", "value": ""}, "message": "Search name parameter cannot be empty"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 400}}, {"description": "a request to search locations with no matches", "providerState": "no locations match search criteria", "request": {"method": "GET", "path": "/locations", "query": "name=NonExistentLocation"}, "response": {"body": [], "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to search locations with partial name match", "providerState": "locations are available for search", "request": {"method": "GET", "path": "/locations", "query": "name=<PERSON><PERSON>"}, "response": {"body": [{"coordinates": {"latitude": -37.8136, "longitude": 144.9631}, "country": "Australia", "countryCode": "AU", "displayName": "Melbourne, VIC, Australia", "id": "location-3", "isActive": true, "name": "Melbourne", "popularity": 92, "state": "Victoria", "stateCode": "VIC", "timezone": "Australia/Melbourne", "type": "city"}], "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body[0].coordinates.latitude": {"match": "type"}, "$.body[0].coordinates.longitude": {"match": "type"}, "$.body[0].id": {"match": "type"}, "$.body[0].popularity": {"match": "type"}}, "status": 200}}], "metadata": {"pact-js": {"version": "15.0.1"}, "pactRust": {"ffi": "0.4.22", "models": "1.2.3"}, "pactSpecification": {"version": "2.0.0"}}, "provider": {"name": "geolocation-services"}}