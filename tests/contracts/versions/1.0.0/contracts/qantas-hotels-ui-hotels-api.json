{"consumer": {"name": "qantas-hotels-ui"}, "interactions": [{"description": "a request to create a booking", "providerState": "booking can be created for property", "request": {"body": {"adults": 2, "checkIn": "2024-12-01", "checkOut": "2024-12-05", "children": 0, "guestDetails": {"email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "phone": "+61412345678"}, "infants": 0, "offerId": "offer-456", "paymentDetails": {"cardToken": "card-token-123", "method": "credit_card"}, "propertyId": "property-123", "roomTypeId": "room-type-123"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json", "qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}}, "method": "POST", "path": "/bookings"}, "response": {"body": {"booking": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "confirmationNumber": "QH123456789", "createdAt": "2015-08-06T16:53:10+01:00", "guests": {"adults": 2, "children": 0, "infants": 0}, "id": "booking-789", "propertyId": "property-123", "status": "confirmed", "totalAmount": {"amount": 450, "currency": "AUD"}}}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.booking.confirmationNumber": {"match": "type"}, "$.body.booking.createdAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.booking.id": {"match": "type"}, "$.body.booking.totalAmount.amount": {"match": "type"}}, "status": 201}}, {"description": "a request to create a booking for unavailable property", "providerState": "booking cannot be created for property", "request": {"body": {"adults": 2, "checkIn": "2024-12-01", "checkOut": "2024-12-05", "children": 0, "infants": 0, "offerId": "offer-456", "propertyId": "property-404", "roomTypeId": "room-type-123"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json", "qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}}, "method": "POST", "path": "/bookings"}, "response": {"body": {"error": {"code": "PROPERTY_UNAVAILABLE", "details": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "propertyId": "property-404"}, "message": "Property is not available for the selected dates"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 422}}, {"description": "a request to create a booking with invalid token", "providerState": "user has invalid authentication token", "request": {"body": {"adults": 2, "checkIn": "2024-12-01", "checkOut": "2024-12-05", "children": 0, "infants": 0, "offerId": "offer-456", "propertyId": "property-123", "roomTypeId": "room-type-123"}, "headers": {"Authorization": "Bearer invalid-token", "Content-Type": "application/json", "qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}}, "method": "POST", "path": "/bookings"}, "response": {"body": {"error": {"code": "UNAUTHORIZED", "message": "Invalid or expired authentication token"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 401}}, {"description": "a request to create a quote for invalid property", "providerState": "property does not exist", "request": {"body": {"adults": 2, "checkIn": "2024-12-01", "checkOut": "2024-12-05", "children": 0, "flightBookerToken": null, "infants": 0, "offerId": "offer-456", "paidByDeposit": "false", "propertyId": "property-404", "roomTypeId": "room-type-123"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json", "X-QH-User-Id": "user-123"}, "method": "POST", "path": "/quotes"}, "response": {"body": {"error": {"code": "PROPERTY_NOT_FOUND", "details": {"propertyId": "property-404"}, "message": "Property not found"}, "requestId": "df31f989-a7de-4970-a9da-ac05dca6156f", "timestamp": "2025-09-04T13:21:50.933Z"}, "headers": {"Content-Type": "application/json"}, "status": 404}}, {"description": "a request to create a quote with cash payment", "providerState": "property exists and is available", "request": {"body": {"adults": 2, "checkIn": "2024-12-01", "checkOut": "2024-12-05", "children": 0, "flightBookerToken": null, "infants": 0, "offerId": "offer-456", "paidByDeposit": "false", "propertyId": "property-123", "roomTypeId": "room-type-123"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json", "X-QH-User-Id": "user-123"}, "method": "POST", "path": "/quotes"}, "response": {"body": {"quote": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "createdAt": "2025-09-04T13:21:50.465Z", "guests": {"adults": 2, "children": 0, "infants": 0}, "id": "quote-789", "offerId": "offer-456", "paymentOptions": {"cash": {"amount": 920, "available": true, "currency": "AUD"}, "points": {"available": false, "reason": "Insufficient points balance"}, "pointsAndCash": {"available": false, "reason": "Insufficient points balance"}}, "policies": {"cancellation": "Free cancellation up to 24 hours before check-in", "modification": "Modifications allowed up to 48 hours before check-in"}, "pricing": {"baseRate": 200, "breakdown": [{"amount": 800, "description": "Room rate (4 nights)"}, {"amount": 120, "description": "Taxes and fees"}], "currency": "AUD", "fees": 10, "taxes": 20, "totalRate": 230}, "propertyId": "property-123", "roomTypeId": "room-type-123", "validUntil": "2025-09-05"}}, "headers": {"Content-Type": "application/json"}, "status": 201}}, {"description": "a request to create a quote with deposit payment", "providerState": "property exists and is available", "request": {"body": {"adults": 2, "checkIn": "2025-10-04", "checkOut": "2025-10-08", "children": 0, "flightBookerToken": null, "infants": 0, "offerId": "offer-456", "paidByDeposit": "true", "propertyId": "property-123", "roomTypeId": "room-type-123"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json", "X-QH-User-Id": "user-123"}, "method": "POST", "path": "/quotes"}, "response": {"body": {"quote": {"checkIn": "2025-10-04", "checkOut": "2025-10-08", "createdAt": "2025-09-04T13:21:50.466Z", "guests": {"adults": 2, "children": 0, "infants": 0}, "id": "quote-deposit-789", "offerId": "offer-456", "paymentOptions": {"cash": {"amount": 920, "available": true, "currency": "AUD"}, "deposit": {"available": true, "currency": "AUD", "depositAmount": 184, "dueDate": "2025-09-27", "remainingAmount": 736}}, "policies": {"cancellation": "Free cancellation up to 24 hours before check-in", "deposit": "Deposit payment available for bookings made 21+ days before check-in", "modification": "Modifications allowed up to 48 hours before check-in"}, "pricing": {"baseRate": 200, "breakdown": [{"amount": 800, "description": "Room rate (4 nights)"}, {"amount": 120, "description": "Taxes and fees"}], "currency": "AUD", "fees": 10, "taxes": 20, "totalRate": 230}, "propertyId": "property-123", "roomTypeId": "room-type-123", "validUntil": "2025-09-05"}}, "headers": {"Content-Type": "application/json"}, "status": 201}}, {"description": "a request to create a quote with invalid dates", "providerState": "property exists and is available", "request": {"body": {"adults": 2, "checkIn": "2023-01-01", "checkOut": "2024-12-01", "children": 0, "flightBookerToken": null, "infants": 0, "offerId": "offer-456", "paidByDeposit": "false", "propertyId": "property-123", "roomTypeId": "room-type-123"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json", "X-QH-User-Id": "user-123"}, "method": "POST", "path": "/quotes"}, "response": {"body": {"error": {"code": "INVALID_DATES", "details": {"checkIn": "2023-01-01", "checkOut": "2024-12-01", "issues": ["Check-in date is in the past", "Check-out date must be after check-in date"]}, "message": "Check-in date must be in the future and before check-out date"}, "requestId": "ac087d3e-cbcc-4cb9-8fd9-699bab1160b7", "timestamp": "2025-09-04T13:21:51.111Z"}, "headers": {"Content-Type": "application/json"}, "status": 400}}, {"description": "a request to create a quote with invalid token", "providerState": "user has invalid authentication token", "request": {"body": {"adults": 2, "checkIn": "2024-12-01", "checkOut": "2024-12-05", "children": 0, "flightBookerToken": null, "infants": 0, "offerId": "offer-456", "paidByDeposit": "false", "propertyId": "property-123", "roomTypeId": "room-type-123"}, "headers": {"Authorization": "Bearer invalid-token", "Content-Type": "application/json", "X-QH-User-Id": "user-123"}, "method": "POST", "path": "/quotes"}, "response": {"body": {"error": {"code": "UNAUTHORIZED", "message": "Invalid or expired authentication token"}, "requestId": "e586df9e-7d21-4012-96fa-353de8f6e2a3", "timestamp": "2025-09-04T13:21:51.226Z"}, "headers": {"Content-Type": "application/json"}, "status": 401}}, {"description": "a request to create a quote with points and cash payment", "providerState": "user is authenticated as QFF member", "request": {"body": {"adults": 2, "checkIn": "2024-12-01", "checkOut": "2024-12-05", "children": 0, "flightBookerToken": "flight-token-123", "infants": 0, "offerId": "offer-456", "paidByDeposit": "false", "propertyId": "property-123", "roomTypeId": "room-type-123"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json", "X-QH-User-Id": "user-123"}, "method": "POST", "path": "/quotes"}, "response": {"body": {"quote": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "createdAt": "2025-09-04T13:21:50.465Z", "guests": {"adults": 2, "children": 0, "infants": 0}, "id": "quote-points-789", "offerId": "offer-456", "paymentOptions": {"cash": {"amount": 920, "available": true, "currency": "AUD"}, "points": {"amount": 92000, "available": true, "conversionRate": 0.01, "currency": "QFF_POINTS"}, "pointsAndCash": {"available": true, "cashAmount": 460, "currency": "AUD", "pointsAmount": 46000, "pointsCurrency": "QFF_POINTS"}}, "pointsEarn": {"basePoints": 920, "bonusPoints": 184, "totalPoints": 1104}, "policies": {"cancellation": "Free cancellation up to 24 hours before check-in", "modification": "Modifications allowed up to 48 hours before check-in"}, "pricing": {"baseRate": 200, "breakdown": [{"amount": 800, "description": "Room rate (4 nights)"}, {"amount": 120, "description": "Taxes and fees"}], "currency": "AUD", "fees": 10, "taxes": 20, "totalRate": 230}, "propertyId": "property-123", "roomTypeId": "room-type-123", "validUntil": "2025-09-05"}}, "headers": {"Content-Type": "application/json"}, "status": 201}}, {"description": "a request to fetch booking by ID", "providerState": "booking exists for user", "request": {"headers": {"qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}, "$.query.cachebuster": {"match": "type"}}, "method": "GET", "path": "/bookings/booking-456", "query": "cachebuster=0%2e123456789"}, "response": {"body": {"booking": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "confirmationNumber": "QH123456789", "createdAt": "2015-08-06T16:53:10+01:00", "guestDetails": {"email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "phone": "+61412345678"}, "guests": {"adults": 2, "children": 0, "infants": 0}, "id": "booking-456", "propertyId": "property-123", "propertyName": "Test Hotel Sydney", "status": "confirmed", "totalAmount": {"amount": 450, "currency": "AUD"}, "updatedAt": "2015-08-06T16:53:10+01:00"}}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.booking.confirmationNumber": {"match": "type"}, "$.body.booking.createdAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.booking.totalAmount.amount": {"match": "type"}, "$.body.booking.updatedAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request to fetch exclusive offers", "providerState": "content exists", "request": {"method": "GET", "path": "/exclusive-offers"}, "response": {"body": [{"description": "Exclusive weekend package with premium amenities", "discount": {"type": "percentage", "value": 25}, "id": "exclusive-offer-123", "inclusions": ["Complimentary breakfast", "Late checkout", "Welcome drink"], "name": "VIP Weekend Package", "pricing": {"currency": "AUD", "discountedRate": 300, "originalRate": 400, "savings": 100}, "propertyId": "property-123", "propertyName": "Test Hotel Sydney", "terms": "Valid for weekend stays only. Subject to availability.", "validFrom": "2025-09-04", "validTo": "2025-10-04"}, {"description": "Special rates for stays of 7 nights or more", "discount": {"type": "fixed", "value": 150}, "id": "exclusive-offer-456", "inclusions": ["Free WiFi", "Daily housekeeping", "Access to fitness center"], "name": "Extended Stay Special", "pricing": {"currency": "AUD", "discountedRate": 350, "originalRate": 500, "savings": 150}, "propertyId": "property-456", "propertyName": "Luxury Resort Sydney", "terms": "Minimum 7 night stay required. Non-refundable.", "validFrom": "2025-09-04", "validTo": "2025-11-03"}], "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to fetch exclusive offers with no results", "providerState": "content exists", "request": {"method": "GET", "path": "/exclusive-offers"}, "response": {"body": [], "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to fetch non-existent booking", "providerState": "booking does not exist", "request": {"headers": {"qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}, "$.query.cachebuster[0]": {"match": "type"}}, "method": "GET", "path": "/bookings/booking-404", "query": "cachebuster=0%2e123456789"}, "response": {"body": {"error": {"code": "BOOKING_NOT_FOUND", "details": {"bookingId": "booking-404"}, "message": "Booking not found"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 404}}, {"description": "a request to fetch non-existent property details", "providerState": "property does not exist", "request": {"headers": {"X-QH-User-Id": "user-123"}, "method": "GET", "path": "/properties/property-404"}, "response": {"body": {"error": {"code": "PROPERTY_NOT_FOUND", "details": {"propertyId": "property-404"}, "message": "Property not found"}, "requestId": "0cd9367a-e7ee-429b-bf27-13d24c9a01c0", "timestamp": "2025-09-04T13:21:53.227Z"}, "headers": {"Content-Type": "application/json"}, "status": 404}}, {"description": "a request to fetch property details", "providerState": "property exists and is available", "request": {"headers": {"X-QH-User-Id": "user-123"}, "method": "GET", "path": "/properties/property-123"}, "response": {"body": {"amenities": [{"category": "connectivity", "id": "wifi", "name": "Free WiFi"}, {"category": "recreation", "id": "pool", "name": "Swimming Pool"}], "contact": {"email": "<EMAIL>", "phone": "+61 2 1234 5678"}, "description": "A beautiful hotel in the heart of Sydney", "id": "property-123", "images": [{"alt": "Hotel exterior", "type": "exterior", "url": "https://example.com/hotel1.jpg"}, {"alt": "Standard room", "type": "room", "url": "https://example.com/room1.jpg"}], "location": {"address": "123 George Street, Sydney NSW 2000", "city": "Sydney", "coordinates": {"latitude": -33.8688, "longitude": 151.2093}, "country": "Australia", "postcode": "2000", "state": "NSW"}, "name": "Test Hotel Sydney", "policies": {"cancellation": "Free cancellation up to 24 hours before check-in", "checkIn": "15:00", "checkOut": "11:00"}, "rating": {"type": "star", "value": 4}}, "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to search location availability", "providerState": "properties are available for search", "request": {"headers": {"Authorization": "Bearer valid-token", "X-QH-User-Id": "user-123"}, "method": "GET", "path": "/locations/Sydney/availability", "query": "adults=2&checkIn=2024-12-01&checkOut=2024-12-05&children=0&infants=0&page=1"}, "response": {"body": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "filters": {"amenities": {"available": ["wifi", "pool", "gym", "spa", "restaurant"], "selected": []}, "distance": {"available": [1, 2, 5, 10], "selected": [], "unit": "km"}, "priceRange": {"currency": "AUD", "max": 495, "min": 200}, "starRating": {"available": [3, 4, 5], "selected": []}}, "guests": {"adults": 2, "children": 0, "infants": 0}, "location": "Sydney", "pagination": {"page": 1, "resultsPerPage": 20, "totalPages": 1, "totalResults": 2}, "results": [{"property": {"amenities": ["wifi", "pool", "gym"], "availability": {"available": true, "lastBookedMinutesAgo": 15, "roomsLeft": 3}, "id": "property-123", "images": [{"alt": "Hotel exterior", "url": "https://example.com/hotel1.jpg"}], "location": {"city": "Sydney", "coordinates": {"latitude": -33.8688, "longitude": 151.2093}, "country": "Australia", "distance": {"description": "0.5km from city center", "unit": "km", "value": 0.5}, "state": "NSW"}, "name": "Test Hotel Sydney", "offers": [{"description": "10% off for early bookings", "id": "offer-123", "name": "Early Bird Special"}], "pricing": {"baseRate": 200, "currency": "AUD", "savings": 30, "strikethroughRate": 250, "totalRate": 220}, "rating": {"type": "star", "value": 4}}}, {"property": {"amenities": ["wifi", "pool", "gym", "spa", "restaurant"], "availability": {"available": true, "lastBookedMinutesAgo": 5, "roomsLeft": 1}, "id": "property-456", "images": [{"alt": "Resort view", "url": "https://example.com/resort1.jpg"}], "location": {"city": "Sydney", "coordinates": {"latitude": -33.865, "longitude": 151.2094}, "country": "Australia", "distance": {"description": "1.2km from city center", "unit": "km", "value": 1.2}, "state": "NSW"}, "name": "Luxury Resort Sydney", "offers": [], "pricing": {"baseRate": 450, "currency": "AUD", "totalRate": 495}, "rating": {"type": "star", "value": 5}}}]}, "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to search location availability with filters", "providerState": "properties are available for search", "request": {"headers": {"Authorization": "Bearer valid-token", "X-QH-User-Id": "user-123"}, "method": "GET", "path": "/locations/Sydney/availability", "query": "adults=2&amenities=spa&checkIn=2024-12-01&checkOut=2024-12-05&children=0&infants=0&page=1&sortBy=price&sortOrder=asc&starRating=5"}, "response": {"body": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "filters": {"amenities": {"available": ["wifi", "pool", "gym", "spa", "restaurant"], "selected": ["spa"]}, "distance": {"available": [1, 2, 5, 10], "selected": [], "unit": "km"}, "priceRange": {"currency": "AUD", "max": 495, "min": 450}, "starRating": {"available": [5], "selected": [5]}}, "guests": {"adults": 2, "children": 0, "infants": 0}, "location": "Sydney", "pagination": {"page": 1, "resultsPerPage": 20, "totalPages": 1, "totalResults": 1}, "results": [{"property": {"amenities": ["wifi", "pool", "gym", "spa", "restaurant"], "availability": {"available": true, "lastBookedMinutesAgo": 5, "roomsLeft": 1}, "id": "property-456", "images": [{"alt": "Resort view", "url": "https://example.com/resort1.jpg"}], "location": {"city": "Sydney", "coordinates": {"latitude": -33.865, "longitude": 151.2094}, "country": "Australia", "distance": {"description": "1.2km from city center", "unit": "km", "value": 1.2}, "state": "NSW"}, "name": "Luxury Resort Sydney", "offers": [], "pricing": {"baseRate": 450, "currency": "AUD", "totalRate": 495}, "rating": {"type": "star", "value": 5}}}]}, "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to search location availability with no results", "providerState": "properties are available for search", "request": {"headers": {"Authorization": "Bearer valid-token", "X-QH-User-Id": "user-123"}, "method": "GET", "path": "/locations/InvalidLocation/availability", "query": "adults=2&checkIn=2024-12-01&checkOut=2024-12-05&children=0&infants=0&page=1"}, "response": {"body": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "filters": {"amenities": {"available": [], "selected": []}, "distance": {"available": [], "selected": [], "unit": "km"}, "priceRange": {"currency": "AUD", "max": 0, "min": 0}, "starRating": {"available": [], "selected": []}}, "guests": {"adults": 2, "children": 0, "infants": 0}, "location": "InvalidLocation", "message": "No properties found for the specified location and dates", "pagination": {"page": 1, "resultsPerPage": 20, "totalPages": 0, "totalResults": 0}, "results": []}, "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to search location availability with pagination", "providerState": "properties are available for search", "request": {"headers": {"Authorization": "Bearer valid-token", "X-QH-User-Id": "user-123"}, "method": "GET", "path": "/locations/Sydney/availability", "query": "adults=2&checkIn=2024-12-01&checkOut=2024-12-05&children=0&infants=0&page=2"}, "response": {"body": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "filters": {"amenities": {"available": ["wifi", "pool", "gym", "spa", "restaurant", "parking"], "selected": []}, "distance": {"available": [1, 2, 5, 10], "selected": [], "unit": "km"}, "priceRange": {"currency": "AUD", "max": 600, "min": 100}, "starRating": {"available": [3, 4, 5], "selected": []}}, "guests": {"adults": 2, "children": 0, "infants": 0}, "location": "Sydney", "pagination": {"page": 2, "resultsPerPage": 20, "totalPages": 3, "totalResults": 45}, "results": [{"property": {"amenities": ["wifi", "parking"], "availability": {"available": true, "lastBookedMinutesAgo": 30, "roomsLeft": 5}, "id": "property-page2-1", "images": [{"alt": "Hotel page 2", "url": "https://example.com/hotel-page2.jpg"}], "location": {"city": "Sydney", "coordinates": {"latitude": -33.87, "longitude": 151.21}, "country": "Australia", "distance": {"description": "2.1km from city center", "unit": "km", "value": 2.1}, "state": "NSW"}, "name": "Hotel Page 2 Item 1", "offers": [], "pricing": {"baseRate": 150, "currency": "AUD", "totalRate": 165}, "rating": {"type": "star", "value": 3}}}]}, "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to search properties by name", "providerState": "properties are available for search", "request": {"method": "GET", "path": "/properties", "query": "name=Sydney"}, "response": {"body": [{"amenities": ["wifi", "pool", "gym"], "id": "property-123", "images": [{"alt": "Hotel exterior", "url": "https://example.com/hotel1.jpg"}], "location": {"city": "Sydney", "coordinates": {"latitude": -33.8688, "longitude": 151.2093}, "country": "Australia", "state": "NSW"}, "name": "Test Hotel Sydney", "pricing": {"baseRate": 200, "currency": "AUD", "totalRate": 220}, "rating": {"type": "star", "value": 4}}, {"amenities": ["wifi", "pool", "gym", "spa", "restaurant"], "id": "property-456", "images": [{"alt": "Resort view", "url": "https://example.com/resort1.jpg"}], "location": {"city": "Sydney", "coordinates": {"latitude": -33.865, "longitude": 151.2094}, "country": "Australia", "state": "NSW"}, "name": "Luxury Resort Sydney", "pricing": {"baseRate": 450, "currency": "AUD", "totalRate": 495}, "rating": {"type": "star", "value": 5}}], "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to search properties with no results", "providerState": "properties are available for search", "request": {"method": "GET", "path": "/properties", "query": "name=NonExistentLocation"}, "response": {"body": [], "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to search property availability", "providerState": "property exists and is available", "request": {"headers": {"Authorization": "Bearer valid-token", "X-QH-User-Id": "user-123"}, "method": "GET", "path": "/properties/property-123/availability", "query": "adults=2&checkIn=2024-12-01&checkOut=2024-12-05&children=0&infants=0"}, "response": {"body": {"available": true, "checkIn": "2024-12-01", "checkOut": "2024-12-05", "propertyId": "property-123", "rooms": [{"amenities": ["wifi", "minibar", "aircon"], "capacity": {"adults": 2, "children": 1, "infants": 1}, "description": "Comfortable room with city view", "id": "room-type-123", "images": [{"alt": "Standard room", "url": "https://example.com/room1.jpg"}], "name": "Standard Room", "offers": [{"description": "10% off for bookings made 30 days in advance", "discount": {"type": "percentage", "value": 10}, "id": "offer-456", "name": "Early Bird Special"}], "pricing": {"baseRate": 200, "currency": "AUD", "taxes": 20, "totalRate": 220}}]}, "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to search related availability for non-existent property", "providerState": "property does not exist", "request": {"method": "GET", "path": "/properties/property-404/related", "query": "adults=2&checkIn=2024-12-01&checkOut=2024-12-05&children=0&infants=0"}, "response": {"body": {"error": {"code": "PROPERTY_NOT_FOUND", "details": {"propertyId": "property-404"}, "message": "Base property not found"}, "requestId": "ccfe6b9c-7fcc-4702-8f27-c6b15e18818d", "timestamp": "2025-09-04T13:21:46.864Z"}, "headers": {"Content-Type": "application/json"}, "status": 404}}, {"description": "a request to search related property availability", "providerState": "property exists and is available", "request": {"method": "GET", "path": "/properties/property-123/related", "query": "adults=2&checkIn=2024-12-01&checkOut=2024-12-05&children=0&infants=0"}, "response": {"body": {"baseProperty": {"id": "property-123", "name": "Test Hotel Sydney"}, "results": [{"property": {"amenities": ["wifi", "pool", "gym"], "availability": {"available": true, "lastBookedMinutesAgo": 10, "roomsLeft": 2}, "id": "related-property-1", "images": [{"alt": "Similar hotel", "url": "https://example.com/similar-hotel.jpg"}], "location": {"city": "Sydney", "coordinates": {"latitude": -33.87, "longitude": 151.21}, "country": "Australia", "distance": {"description": "0.8km from Test Hotel Sydney", "unit": "km", "value": 0.8}, "state": "NSW"}, "name": "Similar Hotel Sydney", "pricing": {"baseRate": 180, "currency": "AUD", "totalRate": 198}, "rating": {"type": "star", "value": 4}, "similarity": {"reasons": ["Similar star rating", "Similar amenities", "Close proximity"], "score": 0.85}}}, {"property": {"amenities": ["wifi", "pool", "restaurant"], "availability": {"available": true, "lastBookedMinutesAgo": 25, "roomsLeft": 4}, "id": "related-property-2", "images": [{"alt": "Alternative hotel", "url": "https://example.com/alternative-hotel.jpg"}], "location": {"city": "Sydney", "coordinates": {"latitude": -33.865, "longitude": 151.205}, "country": "Australia", "distance": {"description": "1.5km from Test Hotel Sydney", "unit": "km", "value": 1.5}, "state": "NSW"}, "name": "Alternative Hotel Sydney", "pricing": {"baseRate": 220, "currency": "AUD", "totalRate": 242}, "rating": {"type": "star", "value": 4}, "similarity": {"reasons": ["Similar star rating", "Similar price range"], "score": 0.78}}}], "searchCriteria": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "guests": {"adults": 2, "children": 0, "infants": 0}}}, "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to search related property availability with no results", "providerState": "property exists and is available", "request": {"method": "GET", "path": "/properties/property-123/related", "query": "adults=10&checkIn=2024-12-01&checkOut=2024-12-05&children=5&infants=2"}, "response": {"body": {"baseProperty": {"id": "property-123", "name": "Test Hotel Sydney"}, "message": "No related properties found for the specified criteria", "results": [], "searchCriteria": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "guests": {"adults": 10, "children": 5, "infants": 2}}}, "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to search unavailable property", "providerState": "property exists but is not available", "request": {"headers": {"Authorization": "Bearer valid-token", "X-QH-User-Id": "user-123"}, "method": "GET", "path": "/properties/property-123/availability", "query": "adults=2&checkIn=2024-12-01&checkOut=2024-12-05&children=0&infants=0"}, "response": {"body": {"available": false, "checkIn": "2024-12-01", "checkOut": "2024-12-05", "message": "No rooms available for the selected dates", "propertyId": "property-123", "rooms": []}, "headers": {"Content-Type": "application/json"}, "status": 200}}], "metadata": {"pact-js": {"version": "15.0.1"}, "pactRust": {"ffi": "0.4.22", "models": "1.2.3"}, "pactSpecification": {"version": "2.0.0"}}, "provider": {"name": "hotels-api"}}