{"consumer": {"name": "qantas-hotels-ui"}, "interactions": [{"description": "a booking creation request expecting 201 Created", "providerState": "booking can be created for property", "request": {"body": {"adults": 2, "checkIn": "2024-12-01", "checkOut": "2024-12-05", "children": 0, "infants": 0, "propertyId": "property-123"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/bookings"}, "response": {"body": {"booking": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "confirmationNumber": "QH********9", "createdAt": "2015-08-06T16:53:10+01:00", "guests": {"adults": 2, "children": 0, "infants": 0}, "id": "booking-new-123", "propertyId": "property-123", "status": "confirmed", "totalAmount": {"amount": 450, "currency": "AUD"}}}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.booking.confirmationNumber": {"match": "type"}, "$.body.booking.createdAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.booking.id": {"match": "type"}, "$.body.booking.totalAmount.amount": {"match": "type"}}, "status": 201}}, {"description": "a booking request when payment service is down", "providerState": "third-party payment service is unavailable", "request": {"body": {"adults": 2, "checkIn": "2024-12-01", "checkOut": "2024-12-05", "children": 0, "infants": 0, "paymentDetails": {"cardToken": "card-token-123", "method": "credit_card"}, "propertyId": "property-123"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/bookings"}, "response": {"body": {"error": {"code": "EXTERNAL_SERVICE_ERROR", "details": {"retryAfter": 300, "service": "payment_processor"}, "message": "A required external service is currently unavailable"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.error.details.retryAfter": {"match": "type"}, "$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 500}}, {"description": "a booking request with invalid data types", "providerState": "request has invalid data types", "request": {"body": {"adults": "two", "checkIn": "2024-12-01", "checkOut": "2024-12-05", "children": true, "infants": [], "propertyId": "property-123", "totalAmount": "expensive"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/bookings"}, "response": {"body": {"error": {"code": "INVALID_DATA_TYPES", "details": {"typeErrors": [{"actualType": "string", "actualValue": "two", "expectedType": "number", "field": "adults"}]}, "message": "Request contains fields with invalid data types"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.error.details.typeErrors": {"match": "type", "min": 1}, "$.body.error.details.typeErrors[*].actualType": {"match": "type"}, "$.body.error.details.typeErrors[*].actualValue": {"match": "type"}, "$.body.error.details.typeErrors[*].expectedType": {"match": "type"}, "$.body.error.details.typeErrors[*].field": {"match": "type"}, "$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 400}}, {"description": "a booking request with invalid date formats", "providerState": "request has invalid date formats", "request": {"body": {"adults": 2, "checkIn": "2024/12/01", "checkOut": "01-12-2024", "children": 0, "infants": 0, "propertyId": "property-123"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/bookings"}, "response": {"body": {"error": {"code": "INVALID_DATE_FORMAT", "details": {"invalidDates": [{"expectedFormat": "YYYY-MM-DD", "field": "checkIn", "value": "2024/12/01"}]}, "message": "Date fields must be in YYYY-MM-DD format"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.error.details.invalidDates": {"match": "type", "min": 1}, "$.body.error.details.invalidDates[*].field": {"match": "type"}, "$.body.error.details.invalidDates[*].value": {"match": "type"}, "$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 400}}, {"description": "a booking request with missing required fields", "providerState": "request has missing required fields", "request": {"body": {"adults": 2, "checkIn": "2024-12-01", "checkOut": "2024-12-05"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/bookings"}, "response": {"body": {"error": {"code": "MISSING_REQUIRED_FIELDS", "details": {"missingFields": ["propertyId"], "providedFields": ["checkIn"]}, "message": "Request is missing required fields"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.error.details.missingFields": {"match": "type", "min": 1}, "$.body.error.details.providedFields": {"match": "type", "min": 1}, "$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 400}}, {"description": "a quote request without guest details", "providerState": "quote request missing guest information", "request": {"body": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "propertyId": "property-123"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/quotes"}, "response": {"body": {"error": {"code": "MISSING_GUEST_INFORMATION", "details": {"missingFields": ["adults", "children", "infants"], "requiredFields": ["adults", "children", "infants"]}, "message": "Guest information is required for quote calculation"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 400}}, {"description": "a request expecting 200 OK status", "providerState": "booking exists for user", "request": {"headers": {"Authorization": "Bearer valid-token"}, "method": "GET", "path": "/bookings/booking-456"}, "response": {"body": {"booking": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "confirmationNumber": "QH********9", "createdAt": "2015-08-06T16:53:10+01:00", "guests": {"adults": 2, "children": 0, "infants": 0}, "id": "booking-456", "propertyId": "property-123", "status": "confirmed", "totalAmount": {"amount": 450, "currency": "AUD"}}}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.booking.confirmationNumber": {"match": "type"}, "$.body.booking.createdAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.booking.id": {"match": "type"}, "$.body.booking.totalAmount.amount": {"match": "type"}}, "status": 200}}, {"description": "a request expecting booking response with correct schema", "providerState": "booking exists for user", "request": {"headers": {"Authorization": "Bearer valid-token"}, "method": "GET", "path": "/bookings/booking-456"}, "response": {"body": {"booking": {"checkIn": "2013-02-01", "checkOut": "2013-02-01", "confirmationNumber": "QH********9", "createdAt": "2015-08-06T16:53:10+01:00", "guests": {"adults": 2, "children": 0, "infants": 0}, "id": "booking-456", "propertyId": "property-123", "propertyName": "Test Hotel Sydney", "status": "confirmed", "totalAmount": {"amount": 450, "currency": "AUD"}, "updatedAt": "2015-08-06T16:53:10+01:00"}}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.booking.checkIn": {"match": "regex", "regex": "^([\\+-]?\\d{4}(?!\\d{2}\\b))((-?)((0[1-9]|1[0-2])(\\3([12]\\d|0[1-9]|3[01]))?|W([0-4]\\d|5[0-2])(-?[1-7])?|(00[1-9]|0[1-9]\\d|[12]\\d{2}|3([0-5]\\d|6[1-6])))?)$"}, "$.body.booking.checkOut": {"match": "regex", "regex": "^([\\+-]?\\d{4}(?!\\d{2}\\b))((-?)((0[1-9]|1[0-2])(\\3([12]\\d|0[1-9]|3[01]))?|W([0-4]\\d|5[0-2])(-?[1-7])?|(00[1-9]|0[1-9]\\d|[12]\\d{2}|3([0-5]\\d|6[1-6])))?)$"}, "$.body.booking.confirmationNumber": {"match": "type"}, "$.body.booking.createdAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.booking.guests.adults": {"match": "type"}, "$.body.booking.guests.children": {"match": "type"}, "$.body.booking.guests.infants": {"match": "type"}, "$.body.booking.id": {"match": "type"}, "$.body.booking.propertyId": {"match": "type"}, "$.body.booking.propertyName": {"match": "type"}, "$.body.booking.status": {"match": "regex", "regex": "^(confirmed|pending|cancelled)$"}, "$.body.booking.totalAmount.amount": {"match": "type"}, "$.body.booking.totalAmount.currency": {"match": "regex", "regex": "^[A-Z]{3}$"}, "$.body.booking.updatedAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request for non-existent booking", "providerState": "booking does not exist", "request": {"headers": {"Authorization": "Bearer valid-token"}, "method": "GET", "path": "/bookings/booking-404"}, "response": {"body": {"error": {"code": "BOOKING_NOT_FOUND", "details": {"bookingId": "booking-404"}, "message": "The requested booking could not be found"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 404}}, {"description": "a request for non-existent property", "providerState": "property does not exist", "request": {"headers": {"Authorization": "Bearer valid-token"}, "method": "GET", "path": "/properties/property-404"}, "response": {"body": {"error": {"code": "PROPERTY_NOT_FOUND", "details": {"propertyId": "property-404"}, "message": "The requested property could not be found"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 404}}, {"description": "a request for property search with expected schema", "providerState": "properties are available for search", "request": {"headers": {"Authorization": "Bearer valid-token"}, "method": "GET", "path": "/properties", "query": "adults=2&checkIn=2024-12-01&checkOut=2024-12-05&children=0&infants=0&location=Sydney"}, "response": {"body": {"pagination": {"limit": 10, "page": 1, "totalPages": 10, "totalResults": 95}, "properties": [{"availability": {"available": true, "roomsLeft": 5}, "id": "property-123", "location": {"city": "Sydney", "coordinates": {"latitude": -33.8688, "longitude": 151.2093}, "country": "Australia"}, "name": "Test Hotel Sydney", "pricing": {"baseRate": 200, "currency": "AUD", "totalRate": 450}, "rating": {"type": "star", "value": 4}}]}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.pagination.limit": {"match": "type"}, "$.body.pagination.page": {"match": "type"}, "$.body.pagination.totalPages": {"match": "type"}, "$.body.pagination.totalResults": {"match": "type"}, "$.body.properties": {"match": "type", "min": 1}, "$.body.properties[*].availability.available": {"match": "type"}, "$.body.properties[*].availability.roomsLeft": {"match": "type"}, "$.body.properties[*].id": {"match": "type"}, "$.body.properties[*].location.city": {"match": "type"}, "$.body.properties[*].location.coordinates.latitude": {"match": "type"}, "$.body.properties[*].location.coordinates.longitude": {"match": "type"}, "$.body.properties[*].location.country": {"match": "type"}, "$.body.properties[*].name": {"match": "type"}, "$.body.properties[*].pricing.baseRate": {"match": "type"}, "$.body.properties[*].pricing.currency": {"match": "regex", "regex": "^[A-Z]{3}$"}, "$.body.properties[*].pricing.totalRate": {"match": "type"}, "$.body.properties[*].rating.type": {"match": "regex", "regex": "^(star|circle)$"}, "$.body.properties[*].rating.value": {"match": "type"}}, "status": 200}}, {"description": "a request that causes server error", "providerState": "server encounters unexpected error", "request": {"body": {"adults": 2, "checkIn": "2024-12-01", "checkOut": "2024-12-05", "children": 0, "infants": 0, "propertyId": "property-123"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/bookings"}, "response": {"body": {"error": {"code": "INTERNAL_ERROR", "message": "An unexpected error occurred while processing the request"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 500}}, {"description": "a request that should succeed but returns error", "providerState": "service returns unexpected error status", "request": {"headers": {"Authorization": "Bearer valid-token"}, "method": "GET", "path": "/bookings/booking-456"}, "response": {"body": {"error": {"code": "SERVICE_UNAVAILABLE", "details": {"actualStatus": 503, "expectedStatus": 200, "retryAfter": 60}, "message": "Service is temporarily unavailable"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.error.details.retryAfter": {"match": "type"}, "$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 503}}, {"description": "a request to add already favourited property", "providerState": "user is authenticated as QFF member", "request": {"body": {"propertyId": "property-123"}, "headers": {"Accept": "application/json", "Authorization": "Bearer valid-token", "Content-Type": "application/json", "qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}}, "method": "POST", "path": "/member-favourites"}, "response": {"body": {"error": {"code": "FAVOURITE_ALREADY_EXISTS", "details": {"propertyId": "property-123"}, "message": "Property is already in favourites"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "success": false, "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 409}}, {"description": "a request to add property to favourites", "providerState": "user is authenticated as QFF member", "request": {"body": {"propertyId": "property-123"}, "headers": {"Accept": "application/json", "Authorization": "Bearer valid-token", "Content-Type": "application/json", "qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}}, "method": "POST", "path": "/member-favourites"}, "response": {"body": {"favourite": {"addedDate": "2015-08-06T16:53:10+01:00", "id": "fav-789", "propertyId": "property-123"}, "success": true}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.favourite.addedDate": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.favourite.id": {"match": "type"}}, "status": 201}}, {"description": "a request to authenticate QFF member", "providerState": "user is authenticated as QFF member", "request": {"body": {"accessToken": "qff-access-token-123", "loginType": "qff", "qhUserId": "qh-user-456"}, "headers": {"Accept": "application/json", "Content-Type": "application/json"}, "method": "POST", "path": "/authenticate"}, "response": {"body": {"authToken": "jwt-auth-token-789", "memberDetails": {"email": "<EMAIL>", "firstName": "<PERSON>", "isVip": true, "lastName": "<PERSON>", "memberId": "********", "pointsBalance": 125000, "preferences": {"currency": "AUD", "language": "en-AU"}, "tierStatus": "Platinum"}, "success": true, "tokenInfo": {"expiresAt": "2015-08-06T16:53:10+01:00", "issuedAt": "2015-08-06T16:53:10+01:00", "scope": ["hotels", "profile", "bookings"]}}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.authToken": {"match": "type"}, "$.body.memberDetails.pointsBalance": {"match": "type"}, "$.body.tokenInfo.expiresAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.tokenInfo.issuedAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request to authenticate guest user", "providerState": "user is not authenticated", "request": {"body": {"guestDetails": {"email": "<EMAIL>", "firstName": "Guest", "lastName": "User"}, "loginType": "guest"}, "headers": {"Accept": "application/json", "Content-Type": "application/json"}, "method": "POST", "path": "/authenticate"}, "response": {"body": {"authToken": "guest-jwt-token-123", "guestDetails": {"email": "<EMAIL>", "firstName": "Guest", "isGuest": true, "lastName": "User"}, "success": true, "tokenInfo": {"expiresAt": "2015-08-06T16:53:10+01:00", "issuedAt": "2015-08-06T16:53:10+01:00", "scope": ["hotels", "guest-bookings"]}}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.authToken": {"match": "type"}, "$.body.tokenInfo.expiresAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.tokenInfo.issuedAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request to authenticate with invalid QFF token", "providerState": "user has invalid authentication token", "request": {"body": {"accessToken": "invalid-qff-token", "loginType": "qff", "qhUserId": "qh-user-456"}, "headers": {"Accept": "application/json", "Content-Type": "application/json"}, "method": "POST", "path": "/authenticate"}, "response": {"body": {"error": {"code": "INVALID_QFF_TOKEN", "message": "QFF access token is invalid or expired"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "success": false, "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 401}}, {"description": "a request to authenticate with missing required fields", "providerState": "user is not authenticated", "request": {"body": {"loginType": "qff"}, "headers": {"Accept": "application/json", "Content-Type": "application/json"}, "method": "POST", "path": "/authenticate"}, "response": {"body": {"error": {"code": "VALIDATION_ERROR", "details": {"missingFields": ["accessToken", "qhUserId"]}, "message": "Required fields are missing"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "success": false, "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 400}}, {"description": "a request to calculate points burn amount", "providerState": "user is authenticated as QFF member", "request": {"body": {"cashAmount": {"currency": "AUD", "value": 50000}, "memberId": "********", "memberTier": "gold", "pointsAmount": 90000, "propertyType": "standard"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/points/calculate-burn"}, "response": {"body": {"calculation": {"actualPointsAmount": 90000, "burnRate": {"dollarPerPoint": 0.0056, "pointsPerDollar": 1.8}, "cashEquivalent": {"currency": "AUD", "value": 50400}, "memberTier": "gold", "requestedPointsAmount": 90000, "savings": {"currency": "AUD", "percentage": 0.8, "value": 400}}, "memberBalance": {"availablePoints": 150000, "remainingPoints": 60000, "tierStatus": "gold"}, "validation": {"isValid": true, "sufficientBalance": true, "withinLimits": true}}, "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to calculate points burn with insufficient balance", "providerState": "user is authenticated as QFF member", "request": {"body": {"cashAmount": {"currency": "AUD", "value": 100000}, "memberId": "********", "memberTier": "bronze", "pointsAmount": 300000, "propertyType": "standard"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/points/calculate-burn"}, "response": {"body": {"error": {"code": "INSUFFICIENT_POINTS_BALANCE", "details": {"availablePoints": 150000, "requestedPoints": 300000, "shortfall": 150000}, "message": "Insufficient points balance for requested burn amount"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 422}}, {"description": "a request to calculate points earn amount", "providerState": "user is authenticated as QFF member", "request": {"body": {"bookingAmount": {"currency": "AUD", "value": 75000}, "memberId": "********", "memberTier": "silver", "propertyId": "property-123", "propertyType": "standard", "stayDates": {"checkIn": "2024-12-01", "checkOut": "2024-12-05"}}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/points/calculate-earn"}, "response": {"body": {"breakdown": {"baseEarning": {"amount": {"currency": "AUD", "value": 75000}, "points": 2250, "rate": 3}, "promotionalBonus": {"campaigns": [], "points": 0}, "tierBonus": {"multiplier": 1.33, "points": 750, "rate": 1, "tier": "silver"}}, "calculation": {"basePoints": 2250, "bonusPoints": 750, "earnRate": {"basePointsPerDollar": 3, "bonusPointsPerDollar": 1, "totalPointsPerDollar": 4}, "memberTier": "silver", "totalPoints": 3000}, "memberStatus": {"currentTier": "silver", "nextTier": "gold", "pointsToNextTier": 47000, "tierProgress": 0.53}}, "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to calculate points earn with promotional campaign", "providerState": "active campaigns exist", "request": {"body": {"bookingAmount": {"currency": "AUD", "value": 100000}, "campaignCode": "DOUBLE-POINTS-2024", "memberId": "********", "memberTier": "gold", "propertyId": "property-123", "propertyType": "luxury", "stayDates": {"checkIn": "2024-12-01", "checkOut": "2024-12-05"}}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/points/calculate-earn"}, "response": {"body": {"breakdown": {"baseEarning": {"amount": {"currency": "AUD", "value": 100000}, "points": 3000, "rate": 3}, "promotionalBonus": {"campaigns": [{"bonusPoints": 5000, "code": "DOUBLE-POINTS-2024", "multiplier": 2, "name": "Double Points Campaign"}], "points": 5000}, "tierBonus": {"multiplier": 1.67, "points": 2000, "rate": 2, "tier": "gold"}}, "calculation": {"basePoints": 3000, "bonusPoints": 2000, "earnRate": {"basePointsPerDollar": 3, "bonusPointsPerDollar": 2, "promotionalPointsPerDollar": 5, "totalPointsPerDollar": 10}, "memberTier": "gold", "promotionalPoints": 5000, "totalPoints": 10000}, "memberStatus": {"currentTier": "gold", "nextTier": "platinum", "pointsToNextTier": 10000, "tierProgress": 0.9}}, "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to fetch luxury points burn tiers", "providerState": "content exists", "request": {"method": "GET", "path": "/points-burn-luxe"}, "response": {"body": {"pointsTierInstance": {"family": "points-burn-luxe", "levels": [{"benefits": ["Luxury property access", "Enhanced burn rate"], "burnRate": {"dollarPerPoint": 0.005, "pointsPerDollar": 2}, "maxPoints": 99999, "minPoints": 0, "propertyTypes": ["luxury", "boutique"], "tier": "luxe-bronze"}, {"benefits": ["Premium luxury access", "Better burn rate", "Concierge service"], "burnRate": {"dollarPerPoint": 0.0059, "pointsPerDollar": 1.7}, "maxPoints": 249999, "minPoints": 100000, "propertyTypes": ["luxury", "boutique", "premium"], "tier": "luxe-silver"}, {"benefits": ["Exclusive luxury access", "Best burn rate", "VIP concierge", "Room upgrades"], "burnRate": {"dollarPerPoint": 0.0071, "pointsPerDollar": 1.4}, "maxPoints": null, "minPoints": 250000, "propertyTypes": ["luxury", "boutique", "premium", "exclusive"], "tier": "luxe-gold"}], "metadata": {"category": "luxury", "currency": "AUD", "lastUpdated": "2015-08-06T16:53:10+01:00", "version": "1.3"}, "name": "points-burn-luxe"}}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.pointsTierInstance.metadata.lastUpdated": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request to fetch points burn tiers", "providerState": "content exists", "request": {"method": "GET", "path": "/points-burn-v2"}, "response": {"body": {"pointsTierInstance": {"family": "points-burn", "levels": [{"benefits": ["Standard burn rate"], "burnRate": {"dollarPerPoint": 0.004, "pointsPerDollar": 2.5}, "maxPoints": 49999, "minPoints": 0, "tier": "bronze"}, {"benefits": ["Enhanced burn rate", "Priority support"], "burnRate": {"dollarPerPoint": 0.005, "pointsPerDollar": 2}, "maxPoints": 99999, "minPoints": 50000, "tier": "silver"}, {"benefits": ["Premium burn rate", "Priority support", "Bonus points"], "burnRate": {"dollarPerPoint": 0.0056, "pointsPerDollar": 1.8}, "maxPoints": 199999, "minPoints": 100000, "tier": "gold"}, {"benefits": ["Best burn rate", "VIP support", "Bonus points", "Exclusive offers"], "burnRate": {"dollarPerPoint": 0.0067, "pointsPerDollar": 1.5}, "maxPoints": null, "minPoints": 200000, "tier": "platinum"}], "metadata": {"currency": "AUD", "lastUpdated": "2015-08-06T16:53:10+01:00", "version": "2.1"}, "name": "points-burn-v2"}}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.pointsTierInstance.metadata.lastUpdated": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request to fetch points burn tiers when service unavailable", "providerState": "content does not exist", "request": {"method": "GET", "path": "/points-burn-v2"}, "response": {"body": {"error": {"code": "SERVICE_UNAVAILABLE", "message": "Points burn service is temporarily unavailable"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 503}}, {"description": "a request to get member details", "providerState": "user is authenticated as QFF member", "request": {"headers": {"Accept": "application/json", "Authorization": "Bearer valid-token", "qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}}, "method": "GET", "path": "/member-details/********"}, "response": {"body": {"member": {"memberId": "********", "membershipDetails": {"accountStatus": "active", "joinDate": "2018-03-15", "lastLoginDate": "2015-08-06T16:53:10+01:00"}, "personalDetails": {"dateOfBirth": "1985-06-15", "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "phone": "+614********"}, "preferences": {"communicationPreferences": {"email": true, "push": true, "sms": false}, "currency": "AUD", "language": "en-AU", "marketingOptIn": true, "timezone": "Australia/Sydney"}, "qffDetails": {"isVip": false, "nextTierRequirement": {"creditsNeeded": 150, "tier": "Platinum"}, "pointsBalance": 75000, "statusCredits": 450, "tierStatus": "Gold"}}}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.member.membershipDetails.lastLoginDate": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.member.qffDetails.nextTierRequirement.creditsNeeded": {"match": "type"}, "$.body.member.qffDetails.pointsBalance": {"match": "type"}, "$.body.member.qffDetails.statusCredits": {"match": "type"}}, "status": 200}}, {"description": "a request to get member details with invalid token", "providerState": "user has invalid authentication token", "request": {"headers": {"Accept": "application/json", "Authorization": "Bearer invalid-token", "qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}}, "method": "GET", "path": "/member-details/********"}, "response": {"body": {"error": {"code": "UNAUTHORIZED", "message": "Invalid or expired authentication token"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 401}}, {"description": "a request to get member favourites", "providerState": "user is authenticated as QFF member", "request": {"headers": {"Accept": "application/json", "Authorization": "Bearer valid-token", "qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}}, "method": "GET", "path": "/member-favourites", "query": "limit=20&page=1"}, "response": {"body": {"favourites": [{"addedDate": "2024-01-15T10:30:00Z", "id": "fav-123", "lastViewedDate": "2024-02-20T14:45:00Z", "propertyId": "property-123", "propertyLocation": {"city": "Sydney", "coordinates": {"latitude": -33.8688, "longitude": 151.2093}, "country": "Australia"}, "propertyName": "Sydney Harbour Hotel", "propertyRating": {"type": "star", "value": 5}}, {"addedDate": "2024-02-01T09:15:00Z", "id": "fav-456", "lastViewedDate": "2024-02-18T16:20:00Z", "propertyId": "property-456", "propertyLocation": {"city": "Melbourne", "coordinates": {"latitude": -37.8136, "longitude": 144.9631}, "country": "Australia"}, "propertyName": "Melbourne Grand Hotel", "propertyRating": {"type": "star", "value": 4}}], "pagination": {"limit": 20, "page": 1, "totalPages": 1, "totalResults": 2}}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.favourites[0].propertyLocation.coordinates.latitude": {"match": "type"}, "$.body.favourites[0].propertyLocation.coordinates.longitude": {"match": "type"}, "$.body.favourites[0].propertyRating.value": {"match": "type"}, "$.body.favourites[1].propertyLocation.coordinates.latitude": {"match": "type"}, "$.body.favourites[1].propertyLocation.coordinates.longitude": {"match": "type"}, "$.body.favourites[1].propertyRating.value": {"match": "type"}}, "status": 200}}, {"description": "a request to get member favourites with no favourites", "providerState": "user is authenticated as QFF member", "request": {"headers": {"Accept": "application/json", "Authorization": "Bearer valid-token", "qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}}, "method": "GET", "path": "/member-favourites", "query": "limit=20&page=1"}, "response": {"body": {"favourites": [], "pagination": {"limit": 20, "page": 1, "totalPages": 0, "totalResults": 0}}, "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to get non-existent member details", "providerState": "user is authenticated as QFF member", "request": {"headers": {"Accept": "application/json", "Authorization": "Bearer valid-token", "qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}}, "method": "GET", "path": "/member-details/99999999"}, "response": {"body": {"error": {"code": "MEMBER_NOT_FOUND", "details": {"memberId": "99999999"}, "message": "Member not found"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 404}}, {"description": "a request to non-existent API endpoint", "providerState": "API endpoint does not exist", "request": {"headers": {"Authorization": "Bearer valid-token"}, "method": "GET", "path": "/non-existent-endpoint"}, "response": {"body": {"error": {"code": "ENDPOINT_NOT_FOUND", "details": {"method": "GET", "path": "/non-existent-endpoint"}, "message": "The requested API endpoint does not exist"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 404}}, {"description": "a request to process combination payment with insufficient points", "providerState": "payment will fail", "request": {"body": {"bookingReference": "booking-failed-points", "cashPayment": {"amount": {"currency": "AUD", "value": 33000}, "paymentMethod": {"encryptedCardNumber": "adyenjs_0_1_25$encrypted-card-number", "encryptedExpiryMonth": "adyenjs_0_1_25$encrypted-expiry-month", "encryptedExpiryYear": "adyenjs_0_1_25$encrypted-expiry-year", "encryptedSecurityCode": "adyenjs_0_1_25$encrypted-security-code", "holderName": "<PERSON>", "type": "scheme"}}, "memberId": "********", "pointsPayment": {"amount": 200000, "cashEquivalent": {"currency": "AUD", "value": 112000}}, "totalAmount": {"currency": "AUD", "value": 145000}}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/points/process-combination-payment"}, "response": {"body": {"error": {"code": "POINTS_TRANSACTION_FAILED", "details": {"cashTransactionStatus": "not_attempted", "pointsAvailable": 150000, "pointsRequested": 200000, "shortfall": 50000, "transactionId": null}, "message": "Points transaction failed due to insufficient balance"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 422}}, {"description": "a request to process combination points and cash payment", "providerState": "payment can be processed", "request": {"body": {"bookingReference": "booking-combo-123", "cashPayment": {"amount": {"currency": "AUD", "value": 17000}, "paymentMethod": {"encryptedCardNumber": "adyenjs_0_1_25$encrypted-card-number", "encryptedExpiryMonth": "adyenjs_0_1_25$encrypted-expiry-month", "encryptedExpiryYear": "adyenjs_0_1_25$encrypted-expiry-year", "encryptedSecurityCode": "adyenjs_0_1_25$encrypted-security-code", "holderName": "<PERSON>", "type": "scheme"}}, "memberId": "********", "pointsPayment": {"amount": 50000, "cashEquivalent": {"currency": "AUD", "value": 28000}}, "totalAmount": {"currency": "AUD", "value": 45000}}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/points/process-combination-payment"}, "response": {"body": {"confirmationDetails": {"confirmationNumber": "QH-COMBO-456789", "processedAt": "2015-08-06T16:53:10+01:00"}, "paymentResult": {"bookingReference": "booking-combo-123", "breakdown": {"cashContribution": {"currency": "AUD", "percentage": 37.8, "value": 17000}, "pointsContribution": {"currency": "AUD", "percentage": 62.2, "value": 28000}}, "cashTransaction": {"amount": {"currency": "AUD", "value": 17000}, "paymentMethod": {"brand": "visa", "type": "scheme"}, "pspReference": "8816178914218518", "resultCode": "Authorised", "status": "completed"}, "pointsTransaction": {"cashEquivalent": {"currency": "AUD", "value": 28000}, "memberBalance": {"newBalance": 100000, "previousBalance": 150000}, "pointsDeducted": 50000, "status": "completed", "transactionId": "pts-txn-789"}, "status": "completed", "totalAmount": {"currency": "AUD", "value": 45000}}}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.confirmationDetails.confirmationNumber": {"match": "type"}, "$.body.confirmationDetails.processedAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.paymentResult.cashTransaction.pspReference": {"match": "type"}, "$.body.paymentResult.pointsTransaction.transactionId": {"match": "type"}}, "status": 200}}, {"description": "a request to remove non-existent favourite", "providerState": "user is authenticated as QFF member", "request": {"headers": {"Accept": "application/json", "Authorization": "Bearer valid-token", "qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}}, "method": "DELETE", "path": "/member-favourites/fav-404"}, "response": {"body": {"error": {"code": "FAVOURITE_NOT_FOUND", "details": {"favouriteId": "fav-404"}, "message": "Favourite not found"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "success": false, "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 404}}, {"description": "a request to remove property from favourites", "providerState": "user is authenticated as QFF member", "request": {"headers": {"Accept": "application/json", "Authorization": "Bearer valid-token", "qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}}, "method": "DELETE", "path": "/member-favourites/fav-123"}, "response": {"body": {"message": "Property removed from favourites", "removedFavourite": {"id": "fav-123", "removedDate": "2015-08-06T16:53:10+01:00"}, "success": true}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.removedFavourite.removedDate": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request to update member profile", "providerState": "user is authenticated as QFF member", "request": {"body": {"personalDetails": {"firstName": "<PERSON>", "lastName": "<PERSON>", "phone": "+61423456789"}, "preferences": {"communicationPreferences": {"email": true, "push": false, "sms": true}, "currency": "USD", "language": "en-US", "marketingOptIn": false, "timezone": "America/New_York"}}, "headers": {"Accept": "application/json", "Authorization": "Bearer valid-token", "Content-Type": "application/json", "qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}}, "method": "PUT", "path": "/member-details/********"}, "response": {"body": {"member": {"memberId": "********", "personalDetails": {"dateOfBirth": "1985-06-15", "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "phone": "+61423456789"}, "preferences": {"communicationPreferences": {"email": true, "push": false, "sms": true}, "currency": "USD", "language": "en-US", "marketingOptIn": false, "timezone": "America/New_York"}, "updatedAt": "2015-08-06T16:53:10+01:00"}, "success": true}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.member.updatedAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request to update member profile with invalid data", "providerState": "user is authenticated as QFF member", "request": {"body": {"personalDetails": {"firstName": "", "phone": "invalid-phone"}, "preferences": {"currency": "INVALID", "language": "invalid-lang"}}, "headers": {"Accept": "application/json", "Authorization": "Bearer valid-token", "Content-Type": "application/json", "qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}}, "method": "PUT", "path": "/member-details/********"}, "response": {"body": {"error": {"code": "VALIDATION_ERROR", "details": {"validationErrors": [{"field": "personalDetails.firstName", "message": "First name cannot be empty"}, {"field": "personalDetails.phone", "message": "Invalid phone number format"}, {"field": "preferences.currency", "message": "Invalid currency code"}, {"field": "preferences.language", "message": "Invalid language code"}]}, "message": "Invalid data provided"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "success": false, "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 400}}, {"description": "a request to validate a valid authentication token", "providerState": "user is authenticated as QFF member", "request": {"headers": {"Accept": "application/json", "Authorization": "Bearer valid-jwt-token-123"}, "method": "GET", "path": "/validate"}, "response": {"body": {"memberDetails": {"email": "<EMAIL>", "firstName": "<PERSON>", "isVip": false, "lastName": "<PERSON><PERSON>", "memberId": "********", "pointsBalance": 50000, "tierStatus": "Gold"}, "tokenInfo": {"expiresAt": "2015-08-06T16:53:10+01:00", "issuedAt": "2015-08-06T16:53:10+01:00", "scope": ["hotels", "profile"]}, "valid": true}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.memberDetails.pointsBalance": {"match": "type"}, "$.body.tokenInfo.expiresAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.tokenInfo.issuedAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request to validate an expired authentication token", "providerState": "user has expired authentication token", "request": {"headers": {"Accept": "application/json", "Authorization": "Bearer expired-jwt-token-456"}, "method": "GET", "path": "/validate"}, "response": {"body": {"error": {"code": "TOKEN_EXPIRED", "details": {"expiredAt": "2015-08-06T16:53:10+01:00"}, "message": "Authentication token has expired"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00", "valid": false}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.error.details.expiredAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 401}}, {"description": "a request to validate an invalid authentication token", "providerState": "user has invalid authentication token", "request": {"headers": {"Accept": "application/json", "Authorization": "Bearer invalid-jwt-token"}, "method": "GET", "path": "/validate"}, "response": {"body": {"error": {"code": "INVALID_TOKEN", "message": "Authentication token is invalid or malformed"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00", "valid": false}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 401}}, {"description": "a request to validate token without authorization header", "providerState": "user is not authenticated", "request": {"headers": {"Accept": "application/json"}, "method": "GET", "path": "/validate"}, "response": {"body": {"error": {"code": "MISSING_AUTHORIZATION", "message": "Authorization header is required"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00", "valid": false}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 401}}, {"description": "a request when database is down", "providerState": "database is unavailable", "request": {"headers": {"Authorization": "Bearer valid-token"}, "method": "GET", "path": "/bookings/booking-456"}, "response": {"body": {"error": {"code": "DATABASE_ERROR", "details": {"type": "database_connection_failure"}, "message": "An internal server error occurred"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 500}}, {"description": "a request with expired authentication token", "providerState": "user has expired authentication token", "request": {"headers": {"Authorization": "Bearer expired-token"}, "method": "GET", "path": "/bookings/booking-456"}, "response": {"body": {"error": {"code": "TOKEN_EXPIRED", "details": {"expiredAt": "2015-08-06T16:53:10+01:00"}, "message": "Authentication token has expired"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.error.details.expiredAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 401}}, {"description": "a request with invalid authentication token", "providerState": "user has invalid authentication token", "request": {"headers": {"Authorization": "Bearer invalid-token"}, "method": "GET", "path": "/bookings/booking-456"}, "response": {"body": {"error": {"code": "INVALID_TOKEN", "message": "Authentication token is invalid or malformed"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 401}}, {"description": "a request with invalid booking payload", "providerState": "request has invalid payload data", "request": {"body": {"adults": -1, "checkIn": "invalid-date", "checkOut": "2024-12-05", "propertyId": ""}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/bookings"}, "response": {"body": {"error": {"code": "INVALID_REQUEST", "details": {"validationErrors": [{"code": "REQUIRED_FIELD", "field": "propertyId", "message": "Property ID cannot be empty"}]}, "message": "Request contains invalid data"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.error.details.validationErrors": {"match": "type", "min": 1}, "$.body.error.details.validationErrors[*].code": {"match": "type"}, "$.body.error.details.validationErrors[*].field": {"match": "type"}, "$.body.error.details.validationErrors[*].message": {"match": "type"}, "$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 400}}, {"description": "a request without Authorization header", "providerState": "request has no authentication token", "request": {"method": "GET", "path": "/bookings/booking-456"}, "response": {"body": {"error": {"code": "MISSING_AUTHENTICATION", "message": "Authentication token is required"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 401}}, {"description": "a request without Content-Type header", "providerState": "request is missing required headers", "request": {"body": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "propertyId": "property-123"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/bookings"}, "response": {"body": {"error": {"code": "MISSING_HEADER", "details": {"missingHeader": "Content-Type"}, "message": "Required header is missing"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 400}}], "metadata": {"pact-js": {"version": "15.0.1"}, "pactRust": {"ffi": "0.4.22", "models": "1.2.3"}, "pactSpecification": {"version": "2.0.0"}}, "provider": {"name": "unknown-provider"}}