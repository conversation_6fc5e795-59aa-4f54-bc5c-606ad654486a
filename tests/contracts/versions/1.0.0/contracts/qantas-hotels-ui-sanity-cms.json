{"consumer": {"name": "qantas-hotels-ui"}, "interactions": [{"description": "a request to fetch FAQ content", "providerState": "FAQs are available", "request": {"method": "GET", "path": "/content", "query": "type=faqs"}, "response": {"body": {"_type": "faqs", "content": [{"_createdAt": "2015-08-06T16:53:10+01:00", "_id": "faq-1", "_type": "faq", "_updatedAt": "2015-08-06T16:53:10+01:00", "answer": "You can make a booking by selecting your dates and property.", "category": "booking", "order": 1, "question": "How do I make a booking?"}], "lastModified": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.content[0]._createdAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.content[0]._id": {"match": "type"}, "$.body.content[0]._updatedAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.content[0].answer": {"match": "type"}, "$.body.content[0].category": {"match": "type"}, "$.body.content[0].order": {"match": "type"}, "$.body.content[0].question": {"match": "type"}, "$.body.lastModified": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request to fetch active campaigns", "providerState": "active campaigns exist", "request": {"method": "GET", "path": "/campaigns"}, "response": {"body": {"campaigns": [{"_createdAt": "2015-08-06T16:53:10+01:00", "_id": "campaign-1", "_type": "campaign", "_updatedAt": "2015-08-06T16:53:10+01:00", "bannerImage": {"_type": "image", "alt": "Summer Sale Banner", "asset": {"_ref": "image-abc123", "_type": "reference"}}, "content": [{"_key": "block-1", "_type": "block", "children": [{"_key": "span-1", "_type": "span", "text": "Book now and save on your summer getaway!"}]}], "description": "Save up to 30% on summer bookings", "endDate": "2015-08-06T16:53:10+01:00", "isActive": true, "slug": "summer-sale-2024", "startDate": "2015-08-06T16:53:10+01:00", "title": "Summer Sale 2024"}], "lastModified": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.campaigns[0]._createdAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.campaigns[0]._id": {"match": "type"}, "$.body.campaigns[0]._updatedAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.campaigns[0].bannerImage.alt": {"match": "type"}, "$.body.campaigns[0].bannerImage.asset._ref": {"match": "type"}, "$.body.campaigns[0].content[0]._key": {"match": "type"}, "$.body.campaigns[0].content[0].children[0]._key": {"match": "type"}, "$.body.campaigns[0].content[0].children[0].text": {"match": "type"}, "$.body.campaigns[0].description": {"match": "type"}, "$.body.campaigns[0].endDate": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.campaigns[0].slug": {"match": "type"}, "$.body.campaigns[0].startDate": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.campaigns[0].title": {"match": "type"}, "$.body.lastModified": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request to fetch campaigns when none are active", "providerState": "no active campaigns exist", "request": {"method": "GET", "path": "/campaigns"}, "response": {"body": {"campaigns": [], "lastModified": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.lastModified": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request to fetch non-existent content type", "providerState": "content does not exist", "request": {"method": "GET", "path": "/content", "query": "type=nonExistentType"}, "response": {"body": {"error": {"code": "CONTENT_NOT_FOUND", "details": {"contentType": "nonExistentType"}, "message": "Content type not found"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 404}}, {"description": "a request to fetch preview content with preview token", "providerState": "content exists", "request": {"headers": {"x-sanity-api-key": "preview-token-123"}, "method": "GET", "path": "/content/preview", "query": "type=faqs"}, "response": {"body": {"_type": "preview", "content": {"_createdAt": "2015-08-06T16:53:10+01:00", "_id": "preview-content-1", "_type": "faq", "_updatedAt": "2015-08-06T16:53:10+01:00", "answer": "This is preview content.", "category": "preview", "order": 1, "question": "Preview FAQ Question"}, "isPreview": true, "lastModified": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.content._createdAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.content._id": {"match": "type"}, "$.body.content._updatedAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.content.answer": {"match": "type"}, "$.body.content.category": {"match": "type"}, "$.body.content.order": {"match": "type"}, "$.body.content.question": {"match": "type"}, "$.body.lastModified": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request to fetch site message for page with no message", "providerState": "no site message exists for page", "request": {"method": "GET", "path": "/site-message", "query": "pageName=checkout"}, "response": {"body": {"lastModified": "2015-08-06T16:53:10+01:00", "message": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.lastModified": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request to fetch site message for search page", "providerState": "site message exists for page", "request": {"method": "GET", "path": "/site-message", "query": "pageName=search"}, "response": {"body": {"lastModified": "2015-08-06T16:53:10+01:00", "message": {"_createdAt": "2015-08-06T16:53:10+01:00", "_id": "site-message-1", "_type": "siteMessage", "_updatedAt": "2015-08-06T16:53:10+01:00", "content": "Welcome to our hotel search page!", "isActive": true, "messageType": "info", "pageName": "search", "priority": 1}}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.lastModified": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.message._createdAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.message._id": {"match": "type"}, "$.body.message._updatedAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.message.content": {"match": "type"}, "$.body.message.messageType": {"match": "type"}, "$.body.message.priority": {"match": "type"}}, "status": 200}}, {"description": "a request to fetch terms and conditions content", "providerState": "content exists", "request": {"method": "GET", "path": "/content", "query": "type=termsAndConditions"}, "response": {"body": {"_type": "termsAndConditions", "content": {"_createdAt": "2015-08-06T16:53:10+01:00", "_id": "terms-1", "_type": "termsAndConditions", "_updatedAt": "2015-08-06T16:53:10+01:00", "content": [{"_key": "block-1", "_type": "block", "children": [{"_key": "span-1", "_type": "span", "text": "These are the terms and conditions for using our service."}]}], "lastUpdated": "2015-08-06T16:53:10+01:00", "title": "Terms and Conditions"}, "lastModified": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.content._createdAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.content._id": {"match": "type"}, "$.body.content._updatedAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.content.content[0]._key": {"match": "type"}, "$.body.content.content[0].children[0]._key": {"match": "type"}, "$.body.content.content[0].children[0].text": {"match": "type"}, "$.body.content.lastUpdated": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.content.title": {"match": "type"}, "$.body.lastModified": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}], "metadata": {"pact-js": {"version": "15.0.1"}, "pactRust": {"ffi": "0.4.22", "models": "1.2.3"}, "pactSpecification": {"version": "2.0.0"}}, "provider": {"name": "sanity-cms"}}