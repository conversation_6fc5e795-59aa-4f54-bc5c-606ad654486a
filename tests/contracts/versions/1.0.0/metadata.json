{"type": "initial", "description": "Initial contract version", "breaking": false, "breakingChanges": [], "timestamp": "2025-09-04T13:27:29.831Z", "contracts": {"qantas-hotels-ui-adyen-payment-service.json": {"consumer": {"name": "qantas-hotels-ui"}, "interactions": [{"description": "a request to check non-existent payment status", "providerState": "payment will fail", "request": {"headers": {"Authorization": "Bearer valid-token"}, "method": "GET", "path": "/payment-status/invalid-psp-reference"}, "response": {"body": {"error": {"code": "PAYMENT_NOT_FOUND", "details": {"pspReference": "invalid-psp-reference"}, "message": "Payment not found"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 404}}, {"description": "a request to check payment status", "providerState": "payment can be processed", "request": {"headers": {"Authorization": "Bearer valid-token"}, "method": "GET", "path": "/payment-status/****************"}, "response": {"body": {"amount": {"currency": "AUD", "value": 45000}, "createdAt": "2015-08-06T16:53:10+01:00", "lastUpdated": "2015-08-06T16:53:10+01:00", "merchantReference": "booking-789", "paymentMethod": {"brand": "visa", "type": "scheme"}, "pspReference": "****************", "status": "Authorised"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.createdAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.lastUpdated": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request to get payment methods", "providerState": "payment methods are available", "request": {"body": {"amount": {"currency": "AUD", "value": 100}, "memberId": "********"}, "headers": {"Content-Type": "application/json"}, "method": "POST", "path": "/qepg/get-payment-methods"}, "response": {"body": {"paymentMethods": [{"brands": ["visa", "mc", "amex", "uatp"], "name": "Credit Card", "type": "scheme"}, {"configuration": {"merchantId": "***************", "merchantName": "Qantas Loyalty"}, "name": "Apple Pay", "type": "applepay"}, {"configuration": {"merchantId": "***************", "merchantName": "Qantas Loyalty"}, "name": "Google Pay", "type": "googlepay"}], "qepgVault": {"accountType": "qff", "memberId": "********"}, "storedPaymentMethods": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.paymentMethods[1].configuration.merchantId": {"match": "type"}, "$.body.paymentMethods[2].configuration.merchantId": {"match": "type"}}, "status": 200}}, {"description": "a request to get payment methods with invalid member", "providerState": "user has invalid authentication token", "request": {"body": {"amount": {"currency": "AUD", "value": 100}, "memberId": "invalid-member"}, "headers": {"Content-Type": "application/json"}, "method": "POST", "path": "/qepg/get-payment-methods"}, "response": {"body": {"error": {"code": "INVALID_MEMBER_ID", "details": {"memberId": "invalid-member"}, "message": "Invalid member ID provided"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 400}}, {"description": "a request to get payment methods with stored cards", "providerState": "payment methods are available", "request": {"body": {"amount": {"currency": "AUD", "value": 250}, "memberId": "********"}, "headers": {"Content-Type": "application/json"}, "method": "POST", "path": "/qepg/get-payment-methods"}, "response": {"body": {"paymentMethods": [{"brands": ["visa", "mc", "amex", "uatp"], "name": "Credit Card", "type": "scheme"}], "qepgVault": {"accountType": "qff", "memberId": "********"}, "storedPaymentMethods": [{"brand": "visa", "expiryMonth": "12", "expiryYear": "2025", "holderName": "<PERSON>", "id": "stored-card-123", "lastFour": "1234", "name": "VISA ending in 1234", "storedPaymentMethodId": "stored-card-123", "supportedShopperInteractions": ["Ecommerce"], "type": "scheme"}, {"brand": "mc", "expiryMonth": "06", "expiryYear": "2026", "holderName": "<PERSON>", "id": "stored-card-456", "lastFour": "5678", "name": "MASTERCARD ending in 5678", "storedPaymentMethodId": "stored-card-456", "supportedShopperInteractions": ["Ecommerce"], "type": "scheme"}]}, "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to process payment", "providerState": "payment can be processed", "request": {"body": {"amount": {"currency": "AUD", "value": 45000}, "merchantAccount": "QantasLoyaltyAU", "paymentMethod": {"encryptedCardNumber": "adyenjs_0_1_25$encrypted-card-number", "encryptedExpiryMonth": "adyenjs_0_1_25$encrypted-expiry-month", "encryptedExpiryYear": "adyenjs_0_1_25$encrypted-expiry-year", "encryptedSecurityCode": "adyenjs_0_1_25$encrypted-security-code", "holderName": "<PERSON>", "type": "scheme"}, "reference": "booking-789", "returnUrl": "https://hotels.qantas.com/checkout/payment-result", "shopperReference": "********", "storePaymentMethod": false}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/payments"}, "response": {"body": {"additionalData": {"authCode": "123456", "cardSummary": "1234", "expiryDate": "12/2025"}, "amount": {"currency": "AUD", "value": 45000}, "merchantReference": "booking-789", "paymentMethod": {"brand": "visa", "type": "scheme"}, "pspReference": "****************", "resultCode": "Authorised"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.additionalData.authCode": {"match": "type"}, "$.body.pspReference": {"match": "type"}}, "status": 200}}, {"description": "a request to process payment with insufficient funds", "providerState": "payment will fail", "request": {"body": {"amount": {"currency": "AUD", "value": 100000}, "merchantAccount": "QantasLoyaltyAU", "paymentMethod": {"encryptedCardNumber": "adyenjs_0_1_25$encrypted-card-number", "encryptedExpiryMonth": "adyenjs_0_1_25$encrypted-expiry-month", "encryptedExpiryYear": "adyenjs_0_1_25$encrypted-expiry-year", "encryptedSecurityCode": "adyenjs_0_1_25$encrypted-security-code", "holderName": "<PERSON>", "type": "scheme"}, "reference": "booking-failed", "returnUrl": "https://hotels.qantas.com/checkout/payment-result", "shopperReference": "********"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/payments"}, "response": {"body": {"additionalData": {"cardSummary": "1234", "refusalReasonRaw": "05 : Do not honor"}, "amount": {"currency": "AUD", "value": 100000}, "merchantReference": "booking-failed", "paymentMethod": {"brand": "visa", "type": "scheme"}, "pspReference": "****************", "refusalReason": "Insufficient funds", "resultCode": "Refused"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.pspReference": {"match": "type"}}, "status": 200}}, {"description": "a request to process payment with invalid card", "providerState": "payment will fail", "request": {"body": {"amount": {"currency": "AUD", "value": 30000}, "merchantAccount": "QantasLoyaltyAU", "paymentMethod": {"encryptedCardNumber": "adyenjs_0_1_25$invalid-card-number", "encryptedExpiryMonth": "adyenjs_0_1_25$invalid-expiry-month", "encryptedExpiryYear": "adyenjs_0_1_25$invalid-expiry-year", "encryptedSecurityCode": "adyenjs_0_1_25$invalid-security-code", "holderName": "<PERSON>", "type": "scheme"}, "reference": "booking-invalid", "returnUrl": "https://hotels.qantas.com/checkout/payment-result", "shopperReference": "********"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/payments"}, "response": {"body": {"error": {"code": "INVALID_CARD_DATA", "details": {"fieldErrors": [{"field": "encryptedCardNumber", "message": "Invalid card number format"}, {"field": "encryptedExpiryMonth", "message": "Invalid expiry month"}]}, "message": "Invalid card data provided"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 422}}, {"description": "a request to process payment with stored card", "providerState": "payment can be processed", "request": {"body": {"amount": {"currency": "AUD", "value": 25000}, "merchantAccount": "QantasLoyaltyAU", "paymentMethod": {"encryptedSecurityCode": "adyenjs_0_1_25$encrypted-security-code", "storedPaymentMethodId": "stored-card-123", "type": "scheme"}, "recurringProcessingModel": "Subscription", "reference": "booking-456", "returnUrl": "https://hotels.qantas.com/checkout/payment-result", "shopperInteraction": "Ecommerce", "shopperReference": "********"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/payments"}, "response": {"body": {"additionalData": {"authCode": "654321", "cardSummary": "1234", "expiryDate": "12/2025", "recurring": "true"}, "amount": {"currency": "AUD", "value": 25000}, "merchantReference": "booking-456", "paymentMethod": {"brand": "visa", "type": "scheme"}, "pspReference": "****************", "resultCode": "Authorised"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.additionalData.authCode": {"match": "type"}, "$.body.pspReference": {"match": "type"}}, "status": 200}}], "metadata": {"pact-js": {"version": "15.0.1"}, "pactRust": {"ffi": "0.4.22", "models": "1.2.3"}, "pactSpecification": {"version": "2.0.0"}}, "provider": {"name": "adyen-payment-service"}}, "qantas-hotels-ui-geolocation-services.json": {"consumer": {"name": "qantas-hotels-ui"}, "interactions": [{"description": "a request to detect user geolocation", "providerState": "user geolocation can be detected", "request": {"method": "GET", "path": "/geolocation"}, "response": {"body": {"accuracy": "city", "city": "Sydney", "confidence": 0.95, "coordinates": {"latitude": -33.8688, "longitude": 151.2093}, "country": "Australia", "countryCode": "AU", "detectionMethod": "ip_geolocation", "geolocation": "NSW", "ipAddress": "*************", "state": "New South Wales", "stateCode": "NSW", "timezone": "Australia/Sydney"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.accuracy": {"match": "type"}, "$.body.confidence": {"match": "type"}, "$.body.coordinates.latitude": {"match": "type"}, "$.body.coordinates.longitude": {"match": "type"}, "$.body.ipAddress": {"match": "type"}}, "status": 200}}, {"description": "a request to detect user geolocation when detection fails", "providerState": "user geolocation cannot be detected", "request": {"method": "GET", "path": "/geolocation"}, "response": {"body": {"error": {"code": "GEOLOCATION_UNAVAILABLE", "details": {"reason": "ip_geolocation_service_unavailable"}, "message": "Unable to determine user location"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 503}}, {"description": "a request to detect user geolocation with low accuracy", "providerState": "user geolocation has low accuracy", "request": {"method": "GET", "path": "/geolocation"}, "response": {"body": {"accuracy": "country", "city": null, "confidence": 0.65, "coordinates": {"latitude": -25.2744, "longitude": 133.7751}, "country": "Australia", "countryCode": "AU", "detectionMethod": "ip_geolocation", "geolocation": "Australia", "ipAddress": "*************", "state": null, "stateCode": null, "timezone": "Australia/Sydney"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.confidence": {"match": "type"}, "$.body.coordinates.latitude": {"match": "type"}, "$.body.coordinates.longitude": {"match": "type"}, "$.body.ipAddress": {"match": "type"}}, "status": 200}}, {"description": "a request to fetch boundaries for non-existent location", "providerState": "location boundaries do not exist", "request": {"method": "GET", "path": "/locations/location-nonexistent/boundaries"}, "response": {"body": {"error": {"code": "LOCATION_NOT_FOUND", "details": {"locationId": "location-nonexistent"}, "message": "Location not found"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 404}}, {"description": "a request to fetch location boundaries", "providerState": "location boundaries are available", "request": {"method": "GET", "path": "/locations/location-sydney-1/boundaries"}, "response": {"body": {"accuracy": "high", "area": 2058.7, "boundaries": {"coordinates": [[[151, -34], [151.5, -34], [151.5, -33.5], [151, -33.5], [151, -34]]], "type": "Polygon"}, "center": {"latitude": -33.8688, "longitude": 151.2093}, "location": {"displayName": "Sydney, NSW, Australia", "id": "location-sydney-1", "name": "Sydney"}, "radius": 25.5}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.area": {"match": "type"}, "$.body.boundaries.coordinates[0][0][0]": {"match": "type"}, "$.body.boundaries.coordinates[0][0][1]": {"match": "type"}, "$.body.boundaries.coordinates[0][1][0]": {"match": "type"}, "$.body.boundaries.coordinates[0][1][1]": {"match": "type"}, "$.body.boundaries.coordinates[0][2][0]": {"match": "type"}, "$.body.boundaries.coordinates[0][2][1]": {"match": "type"}, "$.body.boundaries.coordinates[0][3][0]": {"match": "type"}, "$.body.boundaries.coordinates[0][3][1]": {"match": "type"}, "$.body.boundaries.coordinates[0][4][0]": {"match": "type"}, "$.body.boundaries.coordinates[0][4][1]": {"match": "type"}, "$.body.center.latitude": {"match": "type"}, "$.body.center.longitude": {"match": "type"}, "$.body.radius": {"match": "type"}}, "status": 200}}, {"description": "a request to search locations by name", "providerState": "locations are available for search", "request": {"method": "GET", "path": "/locations", "query": "name=Sydney"}, "response": {"body": [{"coordinates": {"latitude": -33.8688, "longitude": 151.2093}, "country": "Australia", "countryCode": "AU", "displayName": "Sydney, NSW, Australia", "id": "location-1", "isActive": true, "name": "Sydney", "popularity": 95, "state": "NSW", "stateCode": "NSW", "timezone": "Australia/Sydney", "type": "city"}, {"coordinates": {"latitude": -33.8651, "longitude": 151.2099}, "country": "Australia", "countryCode": "AU", "displayName": "Sydney CBD, Sydney, NSW, Australia", "id": "location-2", "isActive": true, "name": "Sydney CBD", "parentLocation": {"id": "location-1", "name": "Sydney"}, "popularity": 88, "state": "NSW", "stateCode": "NSW", "timezone": "Australia/Sydney", "type": "district"}], "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body[0].coordinates.latitude": {"match": "type"}, "$.body[0].coordinates.longitude": {"match": "type"}, "$.body[0].id": {"match": "type"}, "$.body[0].popularity": {"match": "type"}, "$.body[1].coordinates.latitude": {"match": "type"}, "$.body[1].coordinates.longitude": {"match": "type"}, "$.body[1].id": {"match": "type"}, "$.body[1].parentLocation.id": {"match": "type"}, "$.body[1].popularity": {"match": "type"}}, "status": 200}}, {"description": "a request to search locations with invalid parameters", "providerState": "invalid search parameters provided", "request": {"method": "GET", "path": "/locations", "query": "name="}, "response": {"body": {"error": {"code": "INVALID_SEARCH_PARAMETERS", "details": {"parameter": "name", "value": ""}, "message": "Search name parameter cannot be empty"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 400}}, {"description": "a request to search locations with no matches", "providerState": "no locations match search criteria", "request": {"method": "GET", "path": "/locations", "query": "name=NonExistentLocation"}, "response": {"body": [], "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to search locations with partial name match", "providerState": "locations are available for search", "request": {"method": "GET", "path": "/locations", "query": "name=<PERSON><PERSON>"}, "response": {"body": [{"coordinates": {"latitude": -37.8136, "longitude": 144.9631}, "country": "Australia", "countryCode": "AU", "displayName": "Melbourne, VIC, Australia", "id": "location-3", "isActive": true, "name": "Melbourne", "popularity": 92, "state": "Victoria", "stateCode": "VIC", "timezone": "Australia/Melbourne", "type": "city"}], "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body[0].coordinates.latitude": {"match": "type"}, "$.body[0].coordinates.longitude": {"match": "type"}, "$.body[0].id": {"match": "type"}, "$.body[0].popularity": {"match": "type"}}, "status": 200}}], "metadata": {"pact-js": {"version": "15.0.1"}, "pactRust": {"ffi": "0.4.22", "models": "1.2.3"}, "pactSpecification": {"version": "2.0.0"}}, "provider": {"name": "geolocation-services"}}, "qantas-hotels-ui-hotels-api.json": {"consumer": {"name": "qantas-hotels-ui"}, "interactions": [{"description": "a request to create a booking", "providerState": "booking can be created for property", "request": {"body": {"adults": 2, "checkIn": "2024-12-01", "checkOut": "2024-12-05", "children": 0, "guestDetails": {"email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "phone": "+614********"}, "infants": 0, "offerId": "offer-456", "paymentDetails": {"cardToken": "card-token-123", "method": "credit_card"}, "propertyId": "property-123", "roomTypeId": "room-type-123"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json", "qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}}, "method": "POST", "path": "/bookings"}, "response": {"body": {"booking": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "confirmationNumber": "QH********9", "createdAt": "2015-08-06T16:53:10+01:00", "guests": {"adults": 2, "children": 0, "infants": 0}, "id": "booking-789", "propertyId": "property-123", "status": "confirmed", "totalAmount": {"amount": 450, "currency": "AUD"}}}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.booking.confirmationNumber": {"match": "type"}, "$.body.booking.createdAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.booking.id": {"match": "type"}, "$.body.booking.totalAmount.amount": {"match": "type"}}, "status": 201}}, {"description": "a request to create a booking for unavailable property", "providerState": "booking cannot be created for property", "request": {"body": {"adults": 2, "checkIn": "2024-12-01", "checkOut": "2024-12-05", "children": 0, "infants": 0, "offerId": "offer-456", "propertyId": "property-404", "roomTypeId": "room-type-123"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json", "qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}}, "method": "POST", "path": "/bookings"}, "response": {"body": {"error": {"code": "PROPERTY_UNAVAILABLE", "details": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "propertyId": "property-404"}, "message": "Property is not available for the selected dates"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 422}}, {"description": "a request to create a booking with invalid token", "providerState": "user has invalid authentication token", "request": {"body": {"adults": 2, "checkIn": "2024-12-01", "checkOut": "2024-12-05", "children": 0, "infants": 0, "offerId": "offer-456", "propertyId": "property-123", "roomTypeId": "room-type-123"}, "headers": {"Authorization": "Bearer invalid-token", "Content-Type": "application/json", "qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}}, "method": "POST", "path": "/bookings"}, "response": {"body": {"error": {"code": "UNAUTHORIZED", "message": "Invalid or expired authentication token"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 401}}, {"description": "a request to create a quote for invalid property", "providerState": "property does not exist", "request": {"body": {"adults": 2, "checkIn": "2024-12-01", "checkOut": "2024-12-05", "children": 0, "flightBookerToken": null, "infants": 0, "offerId": "offer-456", "paidByDeposit": "false", "propertyId": "property-404", "roomTypeId": "room-type-123"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json", "X-QH-User-Id": "user-123"}, "method": "POST", "path": "/quotes"}, "response": {"body": {"error": {"code": "PROPERTY_NOT_FOUND", "details": {"propertyId": "property-404"}, "message": "Property not found"}, "requestId": "df31f989-a7de-4970-a9da-ac05dca6156f", "timestamp": "2025-09-04T13:21:50.933Z"}, "headers": {"Content-Type": "application/json"}, "status": 404}}, {"description": "a request to create a quote with cash payment", "providerState": "property exists and is available", "request": {"body": {"adults": 2, "checkIn": "2024-12-01", "checkOut": "2024-12-05", "children": 0, "flightBookerToken": null, "infants": 0, "offerId": "offer-456", "paidByDeposit": "false", "propertyId": "property-123", "roomTypeId": "room-type-123"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json", "X-QH-User-Id": "user-123"}, "method": "POST", "path": "/quotes"}, "response": {"body": {"quote": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "createdAt": "2025-09-04T13:21:50.465Z", "guests": {"adults": 2, "children": 0, "infants": 0}, "id": "quote-789", "offerId": "offer-456", "paymentOptions": {"cash": {"amount": 920, "available": true, "currency": "AUD"}, "points": {"available": false, "reason": "Insufficient points balance"}, "pointsAndCash": {"available": false, "reason": "Insufficient points balance"}}, "policies": {"cancellation": "Free cancellation up to 24 hours before check-in", "modification": "Modifications allowed up to 48 hours before check-in"}, "pricing": {"baseRate": 200, "breakdown": [{"amount": 800, "description": "Room rate (4 nights)"}, {"amount": 120, "description": "Taxes and fees"}], "currency": "AUD", "fees": 10, "taxes": 20, "totalRate": 230}, "propertyId": "property-123", "roomTypeId": "room-type-123", "validUntil": "2025-09-05"}}, "headers": {"Content-Type": "application/json"}, "status": 201}}, {"description": "a request to create a quote with deposit payment", "providerState": "property exists and is available", "request": {"body": {"adults": 2, "checkIn": "2025-10-04", "checkOut": "2025-10-08", "children": 0, "flightBookerToken": null, "infants": 0, "offerId": "offer-456", "paidByDeposit": "true", "propertyId": "property-123", "roomTypeId": "room-type-123"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json", "X-QH-User-Id": "user-123"}, "method": "POST", "path": "/quotes"}, "response": {"body": {"quote": {"checkIn": "2025-10-04", "checkOut": "2025-10-08", "createdAt": "2025-09-04T13:21:50.466Z", "guests": {"adults": 2, "children": 0, "infants": 0}, "id": "quote-deposit-789", "offerId": "offer-456", "paymentOptions": {"cash": {"amount": 920, "available": true, "currency": "AUD"}, "deposit": {"available": true, "currency": "AUD", "depositAmount": 184, "dueDate": "2025-09-27", "remainingAmount": 736}}, "policies": {"cancellation": "Free cancellation up to 24 hours before check-in", "deposit": "Deposit payment available for bookings made 21+ days before check-in", "modification": "Modifications allowed up to 48 hours before check-in"}, "pricing": {"baseRate": 200, "breakdown": [{"amount": 800, "description": "Room rate (4 nights)"}, {"amount": 120, "description": "Taxes and fees"}], "currency": "AUD", "fees": 10, "taxes": 20, "totalRate": 230}, "propertyId": "property-123", "roomTypeId": "room-type-123", "validUntil": "2025-09-05"}}, "headers": {"Content-Type": "application/json"}, "status": 201}}, {"description": "a request to create a quote with invalid dates", "providerState": "property exists and is available", "request": {"body": {"adults": 2, "checkIn": "2023-01-01", "checkOut": "2024-12-01", "children": 0, "flightBookerToken": null, "infants": 0, "offerId": "offer-456", "paidByDeposit": "false", "propertyId": "property-123", "roomTypeId": "room-type-123"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json", "X-QH-User-Id": "user-123"}, "method": "POST", "path": "/quotes"}, "response": {"body": {"error": {"code": "INVALID_DATES", "details": {"checkIn": "2023-01-01", "checkOut": "2024-12-01", "issues": ["Check-in date is in the past", "Check-out date must be after check-in date"]}, "message": "Check-in date must be in the future and before check-out date"}, "requestId": "ac087d3e-cbcc-4cb9-8fd9-699bab1160b7", "timestamp": "2025-09-04T13:21:51.111Z"}, "headers": {"Content-Type": "application/json"}, "status": 400}}, {"description": "a request to create a quote with invalid token", "providerState": "user has invalid authentication token", "request": {"body": {"adults": 2, "checkIn": "2024-12-01", "checkOut": "2024-12-05", "children": 0, "flightBookerToken": null, "infants": 0, "offerId": "offer-456", "paidByDeposit": "false", "propertyId": "property-123", "roomTypeId": "room-type-123"}, "headers": {"Authorization": "Bearer invalid-token", "Content-Type": "application/json", "X-QH-User-Id": "user-123"}, "method": "POST", "path": "/quotes"}, "response": {"body": {"error": {"code": "UNAUTHORIZED", "message": "Invalid or expired authentication token"}, "requestId": "e586df9e-7d21-4012-96fa-353de8f6e2a3", "timestamp": "2025-09-04T13:21:51.226Z"}, "headers": {"Content-Type": "application/json"}, "status": 401}}, {"description": "a request to create a quote with points and cash payment", "providerState": "user is authenticated as QFF member", "request": {"body": {"adults": 2, "checkIn": "2024-12-01", "checkOut": "2024-12-05", "children": 0, "flightBookerToken": "flight-token-123", "infants": 0, "offerId": "offer-456", "paidByDeposit": "false", "propertyId": "property-123", "roomTypeId": "room-type-123"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json", "X-QH-User-Id": "user-123"}, "method": "POST", "path": "/quotes"}, "response": {"body": {"quote": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "createdAt": "2025-09-04T13:21:50.465Z", "guests": {"adults": 2, "children": 0, "infants": 0}, "id": "quote-points-789", "offerId": "offer-456", "paymentOptions": {"cash": {"amount": 920, "available": true, "currency": "AUD"}, "points": {"amount": 92000, "available": true, "conversionRate": 0.01, "currency": "QFF_POINTS"}, "pointsAndCash": {"available": true, "cashAmount": 460, "currency": "AUD", "pointsAmount": 46000, "pointsCurrency": "QFF_POINTS"}}, "pointsEarn": {"basePoints": 920, "bonusPoints": 184, "totalPoints": 1104}, "policies": {"cancellation": "Free cancellation up to 24 hours before check-in", "modification": "Modifications allowed up to 48 hours before check-in"}, "pricing": {"baseRate": 200, "breakdown": [{"amount": 800, "description": "Room rate (4 nights)"}, {"amount": 120, "description": "Taxes and fees"}], "currency": "AUD", "fees": 10, "taxes": 20, "totalRate": 230}, "propertyId": "property-123", "roomTypeId": "room-type-123", "validUntil": "2025-09-05"}}, "headers": {"Content-Type": "application/json"}, "status": 201}}, {"description": "a request to fetch booking by ID", "providerState": "booking exists for user", "request": {"headers": {"qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}, "$.query.cachebuster": {"match": "type"}}, "method": "GET", "path": "/bookings/booking-456", "query": "cachebuster=0%2e********9"}, "response": {"body": {"booking": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "confirmationNumber": "QH********9", "createdAt": "2015-08-06T16:53:10+01:00", "guestDetails": {"email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "phone": "+614********"}, "guests": {"adults": 2, "children": 0, "infants": 0}, "id": "booking-456", "propertyId": "property-123", "propertyName": "Test Hotel Sydney", "status": "confirmed", "totalAmount": {"amount": 450, "currency": "AUD"}, "updatedAt": "2015-08-06T16:53:10+01:00"}}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.booking.confirmationNumber": {"match": "type"}, "$.body.booking.createdAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.booking.totalAmount.amount": {"match": "type"}, "$.body.booking.updatedAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request to fetch exclusive offers", "providerState": "content exists", "request": {"method": "GET", "path": "/exclusive-offers"}, "response": {"body": [{"description": "Exclusive weekend package with premium amenities", "discount": {"type": "percentage", "value": 25}, "id": "exclusive-offer-123", "inclusions": ["Complimentary breakfast", "Late checkout", "Welcome drink"], "name": "VIP Weekend Package", "pricing": {"currency": "AUD", "discountedRate": 300, "originalRate": 400, "savings": 100}, "propertyId": "property-123", "propertyName": "Test Hotel Sydney", "terms": "Valid for weekend stays only. Subject to availability.", "validFrom": "2025-09-04", "validTo": "2025-10-04"}, {"description": "Special rates for stays of 7 nights or more", "discount": {"type": "fixed", "value": 150}, "id": "exclusive-offer-456", "inclusions": ["Free WiFi", "Daily housekeeping", "Access to fitness center"], "name": "Extended Stay Special", "pricing": {"currency": "AUD", "discountedRate": 350, "originalRate": 500, "savings": 150}, "propertyId": "property-456", "propertyName": "Luxury Resort Sydney", "terms": "Minimum 7 night stay required. Non-refundable.", "validFrom": "2025-09-04", "validTo": "2025-11-03"}], "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to fetch exclusive offers with no results", "providerState": "content exists", "request": {"method": "GET", "path": "/exclusive-offers"}, "response": {"body": [], "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to fetch non-existent booking", "providerState": "booking does not exist", "request": {"headers": {"qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}, "$.query.cachebuster[0]": {"match": "type"}}, "method": "GET", "path": "/bookings/booking-404", "query": "cachebuster=0%2e********9"}, "response": {"body": {"error": {"code": "BOOKING_NOT_FOUND", "details": {"bookingId": "booking-404"}, "message": "Booking not found"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 404}}, {"description": "a request to fetch non-existent property details", "providerState": "property does not exist", "request": {"headers": {"X-QH-User-Id": "user-123"}, "method": "GET", "path": "/properties/property-404"}, "response": {"body": {"error": {"code": "PROPERTY_NOT_FOUND", "details": {"propertyId": "property-404"}, "message": "Property not found"}, "requestId": "0cd9367a-e7ee-429b-bf27-13d24c9a01c0", "timestamp": "2025-09-04T13:21:53.227Z"}, "headers": {"Content-Type": "application/json"}, "status": 404}}, {"description": "a request to fetch property details", "providerState": "property exists and is available", "request": {"headers": {"X-QH-User-Id": "user-123"}, "method": "GET", "path": "/properties/property-123"}, "response": {"body": {"amenities": [{"category": "connectivity", "id": "wifi", "name": "Free WiFi"}, {"category": "recreation", "id": "pool", "name": "Swimming Pool"}], "contact": {"email": "<EMAIL>", "phone": "+61 2 1234 5678"}, "description": "A beautiful hotel in the heart of Sydney", "id": "property-123", "images": [{"alt": "Hotel exterior", "type": "exterior", "url": "https://example.com/hotel1.jpg"}, {"alt": "Standard room", "type": "room", "url": "https://example.com/room1.jpg"}], "location": {"address": "123 George Street, Sydney NSW 2000", "city": "Sydney", "coordinates": {"latitude": -33.8688, "longitude": 151.2093}, "country": "Australia", "postcode": "2000", "state": "NSW"}, "name": "Test Hotel Sydney", "policies": {"cancellation": "Free cancellation up to 24 hours before check-in", "checkIn": "15:00", "checkOut": "11:00"}, "rating": {"type": "star", "value": 4}}, "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to search location availability", "providerState": "properties are available for search", "request": {"headers": {"Authorization": "Bearer valid-token", "X-QH-User-Id": "user-123"}, "method": "GET", "path": "/locations/Sydney/availability", "query": "adults=2&checkIn=2024-12-01&checkOut=2024-12-05&children=0&infants=0&page=1"}, "response": {"body": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "filters": {"amenities": {"available": ["wifi", "pool", "gym", "spa", "restaurant"], "selected": []}, "distance": {"available": [1, 2, 5, 10], "selected": [], "unit": "km"}, "priceRange": {"currency": "AUD", "max": 495, "min": 200}, "starRating": {"available": [3, 4, 5], "selected": []}}, "guests": {"adults": 2, "children": 0, "infants": 0}, "location": "Sydney", "pagination": {"page": 1, "resultsPerPage": 20, "totalPages": 1, "totalResults": 2}, "results": [{"property": {"amenities": ["wifi", "pool", "gym"], "availability": {"available": true, "lastBookedMinutesAgo": 15, "roomsLeft": 3}, "id": "property-123", "images": [{"alt": "Hotel exterior", "url": "https://example.com/hotel1.jpg"}], "location": {"city": "Sydney", "coordinates": {"latitude": -33.8688, "longitude": 151.2093}, "country": "Australia", "distance": {"description": "0.5km from city center", "unit": "km", "value": 0.5}, "state": "NSW"}, "name": "Test Hotel Sydney", "offers": [{"description": "10% off for early bookings", "id": "offer-123", "name": "Early Bird Special"}], "pricing": {"baseRate": 200, "currency": "AUD", "savings": 30, "strikethroughRate": 250, "totalRate": 220}, "rating": {"type": "star", "value": 4}}}, {"property": {"amenities": ["wifi", "pool", "gym", "spa", "restaurant"], "availability": {"available": true, "lastBookedMinutesAgo": 5, "roomsLeft": 1}, "id": "property-456", "images": [{"alt": "Resort view", "url": "https://example.com/resort1.jpg"}], "location": {"city": "Sydney", "coordinates": {"latitude": -33.865, "longitude": 151.2094}, "country": "Australia", "distance": {"description": "1.2km from city center", "unit": "km", "value": 1.2}, "state": "NSW"}, "name": "Luxury Resort Sydney", "offers": [], "pricing": {"baseRate": 450, "currency": "AUD", "totalRate": 495}, "rating": {"type": "star", "value": 5}}}]}, "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to search location availability with filters", "providerState": "properties are available for search", "request": {"headers": {"Authorization": "Bearer valid-token", "X-QH-User-Id": "user-123"}, "method": "GET", "path": "/locations/Sydney/availability", "query": "adults=2&amenities=spa&checkIn=2024-12-01&checkOut=2024-12-05&children=0&infants=0&page=1&sortBy=price&sortOrder=asc&starRating=5"}, "response": {"body": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "filters": {"amenities": {"available": ["wifi", "pool", "gym", "spa", "restaurant"], "selected": ["spa"]}, "distance": {"available": [1, 2, 5, 10], "selected": [], "unit": "km"}, "priceRange": {"currency": "AUD", "max": 495, "min": 450}, "starRating": {"available": [5], "selected": [5]}}, "guests": {"adults": 2, "children": 0, "infants": 0}, "location": "Sydney", "pagination": {"page": 1, "resultsPerPage": 20, "totalPages": 1, "totalResults": 1}, "results": [{"property": {"amenities": ["wifi", "pool", "gym", "spa", "restaurant"], "availability": {"available": true, "lastBookedMinutesAgo": 5, "roomsLeft": 1}, "id": "property-456", "images": [{"alt": "Resort view", "url": "https://example.com/resort1.jpg"}], "location": {"city": "Sydney", "coordinates": {"latitude": -33.865, "longitude": 151.2094}, "country": "Australia", "distance": {"description": "1.2km from city center", "unit": "km", "value": 1.2}, "state": "NSW"}, "name": "Luxury Resort Sydney", "offers": [], "pricing": {"baseRate": 450, "currency": "AUD", "totalRate": 495}, "rating": {"type": "star", "value": 5}}}]}, "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to search location availability with no results", "providerState": "properties are available for search", "request": {"headers": {"Authorization": "Bearer valid-token", "X-QH-User-Id": "user-123"}, "method": "GET", "path": "/locations/InvalidLocation/availability", "query": "adults=2&checkIn=2024-12-01&checkOut=2024-12-05&children=0&infants=0&page=1"}, "response": {"body": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "filters": {"amenities": {"available": [], "selected": []}, "distance": {"available": [], "selected": [], "unit": "km"}, "priceRange": {"currency": "AUD", "max": 0, "min": 0}, "starRating": {"available": [], "selected": []}}, "guests": {"adults": 2, "children": 0, "infants": 0}, "location": "InvalidLocation", "message": "No properties found for the specified location and dates", "pagination": {"page": 1, "resultsPerPage": 20, "totalPages": 0, "totalResults": 0}, "results": []}, "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to search location availability with pagination", "providerState": "properties are available for search", "request": {"headers": {"Authorization": "Bearer valid-token", "X-QH-User-Id": "user-123"}, "method": "GET", "path": "/locations/Sydney/availability", "query": "adults=2&checkIn=2024-12-01&checkOut=2024-12-05&children=0&infants=0&page=2"}, "response": {"body": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "filters": {"amenities": {"available": ["wifi", "pool", "gym", "spa", "restaurant", "parking"], "selected": []}, "distance": {"available": [1, 2, 5, 10], "selected": [], "unit": "km"}, "priceRange": {"currency": "AUD", "max": 600, "min": 100}, "starRating": {"available": [3, 4, 5], "selected": []}}, "guests": {"adults": 2, "children": 0, "infants": 0}, "location": "Sydney", "pagination": {"page": 2, "resultsPerPage": 20, "totalPages": 3, "totalResults": 45}, "results": [{"property": {"amenities": ["wifi", "parking"], "availability": {"available": true, "lastBookedMinutesAgo": 30, "roomsLeft": 5}, "id": "property-page2-1", "images": [{"alt": "Hotel page 2", "url": "https://example.com/hotel-page2.jpg"}], "location": {"city": "Sydney", "coordinates": {"latitude": -33.87, "longitude": 151.21}, "country": "Australia", "distance": {"description": "2.1km from city center", "unit": "km", "value": 2.1}, "state": "NSW"}, "name": "Hotel Page 2 Item 1", "offers": [], "pricing": {"baseRate": 150, "currency": "AUD", "totalRate": 165}, "rating": {"type": "star", "value": 3}}}]}, "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to search properties by name", "providerState": "properties are available for search", "request": {"method": "GET", "path": "/properties", "query": "name=Sydney"}, "response": {"body": [{"amenities": ["wifi", "pool", "gym"], "id": "property-123", "images": [{"alt": "Hotel exterior", "url": "https://example.com/hotel1.jpg"}], "location": {"city": "Sydney", "coordinates": {"latitude": -33.8688, "longitude": 151.2093}, "country": "Australia", "state": "NSW"}, "name": "Test Hotel Sydney", "pricing": {"baseRate": 200, "currency": "AUD", "totalRate": 220}, "rating": {"type": "star", "value": 4}}, {"amenities": ["wifi", "pool", "gym", "spa", "restaurant"], "id": "property-456", "images": [{"alt": "Resort view", "url": "https://example.com/resort1.jpg"}], "location": {"city": "Sydney", "coordinates": {"latitude": -33.865, "longitude": 151.2094}, "country": "Australia", "state": "NSW"}, "name": "Luxury Resort Sydney", "pricing": {"baseRate": 450, "currency": "AUD", "totalRate": 495}, "rating": {"type": "star", "value": 5}}], "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to search properties with no results", "providerState": "properties are available for search", "request": {"method": "GET", "path": "/properties", "query": "name=NonExistentLocation"}, "response": {"body": [], "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to search property availability", "providerState": "property exists and is available", "request": {"headers": {"Authorization": "Bearer valid-token", "X-QH-User-Id": "user-123"}, "method": "GET", "path": "/properties/property-123/availability", "query": "adults=2&checkIn=2024-12-01&checkOut=2024-12-05&children=0&infants=0"}, "response": {"body": {"available": true, "checkIn": "2024-12-01", "checkOut": "2024-12-05", "propertyId": "property-123", "rooms": [{"amenities": ["wifi", "minibar", "aircon"], "capacity": {"adults": 2, "children": 1, "infants": 1}, "description": "Comfortable room with city view", "id": "room-type-123", "images": [{"alt": "Standard room", "url": "https://example.com/room1.jpg"}], "name": "Standard Room", "offers": [{"description": "10% off for bookings made 30 days in advance", "discount": {"type": "percentage", "value": 10}, "id": "offer-456", "name": "Early Bird Special"}], "pricing": {"baseRate": 200, "currency": "AUD", "taxes": 20, "totalRate": 220}}]}, "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to search related availability for non-existent property", "providerState": "property does not exist", "request": {"method": "GET", "path": "/properties/property-404/related", "query": "adults=2&checkIn=2024-12-01&checkOut=2024-12-05&children=0&infants=0"}, "response": {"body": {"error": {"code": "PROPERTY_NOT_FOUND", "details": {"propertyId": "property-404"}, "message": "Base property not found"}, "requestId": "ccfe6b9c-7fcc-4702-8f27-c6b15e18818d", "timestamp": "2025-09-04T13:21:46.864Z"}, "headers": {"Content-Type": "application/json"}, "status": 404}}, {"description": "a request to search related property availability", "providerState": "property exists and is available", "request": {"method": "GET", "path": "/properties/property-123/related", "query": "adults=2&checkIn=2024-12-01&checkOut=2024-12-05&children=0&infants=0"}, "response": {"body": {"baseProperty": {"id": "property-123", "name": "Test Hotel Sydney"}, "results": [{"property": {"amenities": ["wifi", "pool", "gym"], "availability": {"available": true, "lastBookedMinutesAgo": 10, "roomsLeft": 2}, "id": "related-property-1", "images": [{"alt": "Similar hotel", "url": "https://example.com/similar-hotel.jpg"}], "location": {"city": "Sydney", "coordinates": {"latitude": -33.87, "longitude": 151.21}, "country": "Australia", "distance": {"description": "0.8km from Test Hotel Sydney", "unit": "km", "value": 0.8}, "state": "NSW"}, "name": "Similar Hotel Sydney", "pricing": {"baseRate": 180, "currency": "AUD", "totalRate": 198}, "rating": {"type": "star", "value": 4}, "similarity": {"reasons": ["Similar star rating", "Similar amenities", "Close proximity"], "score": 0.85}}}, {"property": {"amenities": ["wifi", "pool", "restaurant"], "availability": {"available": true, "lastBookedMinutesAgo": 25, "roomsLeft": 4}, "id": "related-property-2", "images": [{"alt": "Alternative hotel", "url": "https://example.com/alternative-hotel.jpg"}], "location": {"city": "Sydney", "coordinates": {"latitude": -33.865, "longitude": 151.205}, "country": "Australia", "distance": {"description": "1.5km from Test Hotel Sydney", "unit": "km", "value": 1.5}, "state": "NSW"}, "name": "Alternative Hotel Sydney", "pricing": {"baseRate": 220, "currency": "AUD", "totalRate": 242}, "rating": {"type": "star", "value": 4}, "similarity": {"reasons": ["Similar star rating", "Similar price range"], "score": 0.78}}}], "searchCriteria": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "guests": {"adults": 2, "children": 0, "infants": 0}}}, "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to search related property availability with no results", "providerState": "property exists and is available", "request": {"method": "GET", "path": "/properties/property-123/related", "query": "adults=10&checkIn=2024-12-01&checkOut=2024-12-05&children=5&infants=2"}, "response": {"body": {"baseProperty": {"id": "property-123", "name": "Test Hotel Sydney"}, "message": "No related properties found for the specified criteria", "results": [], "searchCriteria": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "guests": {"adults": 10, "children": 5, "infants": 2}}}, "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to search unavailable property", "providerState": "property exists but is not available", "request": {"headers": {"Authorization": "Bearer valid-token", "X-QH-User-Id": "user-123"}, "method": "GET", "path": "/properties/property-123/availability", "query": "adults=2&checkIn=2024-12-01&checkOut=2024-12-05&children=0&infants=0"}, "response": {"body": {"available": false, "checkIn": "2024-12-01", "checkOut": "2024-12-05", "message": "No rooms available for the selected dates", "propertyId": "property-123", "rooms": []}, "headers": {"Content-Type": "application/json"}, "status": 200}}], "metadata": {"pact-js": {"version": "15.0.1"}, "pactRust": {"ffi": "0.4.22", "models": "1.2.3"}, "pactSpecification": {"version": "2.0.0"}}, "provider": {"name": "hotels-api"}}, "qantas-hotels-ui-sanity-cms.json": {"consumer": {"name": "qantas-hotels-ui"}, "interactions": [{"description": "a request to fetch FAQ content", "providerState": "FAQs are available", "request": {"method": "GET", "path": "/content", "query": "type=faqs"}, "response": {"body": {"_type": "faqs", "content": [{"_createdAt": "2015-08-06T16:53:10+01:00", "_id": "faq-1", "_type": "faq", "_updatedAt": "2015-08-06T16:53:10+01:00", "answer": "You can make a booking by selecting your dates and property.", "category": "booking", "order": 1, "question": "How do I make a booking?"}], "lastModified": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.content[0]._createdAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.content[0]._id": {"match": "type"}, "$.body.content[0]._updatedAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.content[0].answer": {"match": "type"}, "$.body.content[0].category": {"match": "type"}, "$.body.content[0].order": {"match": "type"}, "$.body.content[0].question": {"match": "type"}, "$.body.lastModified": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request to fetch active campaigns", "providerState": "active campaigns exist", "request": {"method": "GET", "path": "/campaigns"}, "response": {"body": {"campaigns": [{"_createdAt": "2015-08-06T16:53:10+01:00", "_id": "campaign-1", "_type": "campaign", "_updatedAt": "2015-08-06T16:53:10+01:00", "bannerImage": {"_type": "image", "alt": "Summer Sale Banner", "asset": {"_ref": "image-abc123", "_type": "reference"}}, "content": [{"_key": "block-1", "_type": "block", "children": [{"_key": "span-1", "_type": "span", "text": "Book now and save on your summer getaway!"}]}], "description": "Save up to 30% on summer bookings", "endDate": "2015-08-06T16:53:10+01:00", "isActive": true, "slug": "summer-sale-2024", "startDate": "2015-08-06T16:53:10+01:00", "title": "Summer Sale 2024"}], "lastModified": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.campaigns[0]._createdAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.campaigns[0]._id": {"match": "type"}, "$.body.campaigns[0]._updatedAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.campaigns[0].bannerImage.alt": {"match": "type"}, "$.body.campaigns[0].bannerImage.asset._ref": {"match": "type"}, "$.body.campaigns[0].content[0]._key": {"match": "type"}, "$.body.campaigns[0].content[0].children[0]._key": {"match": "type"}, "$.body.campaigns[0].content[0].children[0].text": {"match": "type"}, "$.body.campaigns[0].description": {"match": "type"}, "$.body.campaigns[0].endDate": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.campaigns[0].slug": {"match": "type"}, "$.body.campaigns[0].startDate": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.campaigns[0].title": {"match": "type"}, "$.body.lastModified": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request to fetch campaigns when none are active", "providerState": "no active campaigns exist", "request": {"method": "GET", "path": "/campaigns"}, "response": {"body": {"campaigns": [], "lastModified": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.lastModified": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request to fetch non-existent content type", "providerState": "content does not exist", "request": {"method": "GET", "path": "/content", "query": "type=nonExistentType"}, "response": {"body": {"error": {"code": "CONTENT_NOT_FOUND", "details": {"contentType": "nonExistentType"}, "message": "Content type not found"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 404}}, {"description": "a request to fetch preview content with preview token", "providerState": "content exists", "request": {"headers": {"x-sanity-api-key": "preview-token-123"}, "method": "GET", "path": "/content/preview", "query": "type=faqs"}, "response": {"body": {"_type": "preview", "content": {"_createdAt": "2015-08-06T16:53:10+01:00", "_id": "preview-content-1", "_type": "faq", "_updatedAt": "2015-08-06T16:53:10+01:00", "answer": "This is preview content.", "category": "preview", "order": 1, "question": "Preview FAQ Question"}, "isPreview": true, "lastModified": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.content._createdAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.content._id": {"match": "type"}, "$.body.content._updatedAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.content.answer": {"match": "type"}, "$.body.content.category": {"match": "type"}, "$.body.content.order": {"match": "type"}, "$.body.content.question": {"match": "type"}, "$.body.lastModified": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request to fetch site message for page with no message", "providerState": "no site message exists for page", "request": {"method": "GET", "path": "/site-message", "query": "pageName=checkout"}, "response": {"body": {"lastModified": "2015-08-06T16:53:10+01:00", "message": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.lastModified": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request to fetch site message for search page", "providerState": "site message exists for page", "request": {"method": "GET", "path": "/site-message", "query": "pageName=search"}, "response": {"body": {"lastModified": "2015-08-06T16:53:10+01:00", "message": {"_createdAt": "2015-08-06T16:53:10+01:00", "_id": "site-message-1", "_type": "siteMessage", "_updatedAt": "2015-08-06T16:53:10+01:00", "content": "Welcome to our hotel search page!", "isActive": true, "messageType": "info", "pageName": "search", "priority": 1}}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.lastModified": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.message._createdAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.message._id": {"match": "type"}, "$.body.message._updatedAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.message.content": {"match": "type"}, "$.body.message.messageType": {"match": "type"}, "$.body.message.priority": {"match": "type"}}, "status": 200}}, {"description": "a request to fetch terms and conditions content", "providerState": "content exists", "request": {"method": "GET", "path": "/content", "query": "type=termsAndConditions"}, "response": {"body": {"_type": "termsAndConditions", "content": {"_createdAt": "2015-08-06T16:53:10+01:00", "_id": "terms-1", "_type": "termsAndConditions", "_updatedAt": "2015-08-06T16:53:10+01:00", "content": [{"_key": "block-1", "_type": "block", "children": [{"_key": "span-1", "_type": "span", "text": "These are the terms and conditions for using our service."}]}], "lastUpdated": "2015-08-06T16:53:10+01:00", "title": "Terms and Conditions"}, "lastModified": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.content._createdAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.content._id": {"match": "type"}, "$.body.content._updatedAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.content.content[0]._key": {"match": "type"}, "$.body.content.content[0].children[0]._key": {"match": "type"}, "$.body.content.content[0].children[0].text": {"match": "type"}, "$.body.content.lastUpdated": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.content.title": {"match": "type"}, "$.body.lastModified": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}], "metadata": {"pact-js": {"version": "15.0.1"}, "pactRust": {"ffi": "0.4.22", "models": "1.2.3"}, "pactSpecification": {"version": "2.0.0"}}, "provider": {"name": "sanity-cms"}}, "qantas-hotels-ui-unknown-provider.json": {"consumer": {"name": "qantas-hotels-ui"}, "interactions": [{"description": "a booking creation request expecting 201 Created", "providerState": "booking can be created for property", "request": {"body": {"adults": 2, "checkIn": "2024-12-01", "checkOut": "2024-12-05", "children": 0, "infants": 0, "propertyId": "property-123"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/bookings"}, "response": {"body": {"booking": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "confirmationNumber": "QH********9", "createdAt": "2015-08-06T16:53:10+01:00", "guests": {"adults": 2, "children": 0, "infants": 0}, "id": "booking-new-123", "propertyId": "property-123", "status": "confirmed", "totalAmount": {"amount": 450, "currency": "AUD"}}}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.booking.confirmationNumber": {"match": "type"}, "$.body.booking.createdAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.booking.id": {"match": "type"}, "$.body.booking.totalAmount.amount": {"match": "type"}}, "status": 201}}, {"description": "a booking request when payment service is down", "providerState": "third-party payment service is unavailable", "request": {"body": {"adults": 2, "checkIn": "2024-12-01", "checkOut": "2024-12-05", "children": 0, "infants": 0, "paymentDetails": {"cardToken": "card-token-123", "method": "credit_card"}, "propertyId": "property-123"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/bookings"}, "response": {"body": {"error": {"code": "EXTERNAL_SERVICE_ERROR", "details": {"retryAfter": 300, "service": "payment_processor"}, "message": "A required external service is currently unavailable"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.error.details.retryAfter": {"match": "type"}, "$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 500}}, {"description": "a booking request with invalid data types", "providerState": "request has invalid data types", "request": {"body": {"adults": "two", "checkIn": "2024-12-01", "checkOut": "2024-12-05", "children": true, "infants": [], "propertyId": "property-123", "totalAmount": "expensive"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/bookings"}, "response": {"body": {"error": {"code": "INVALID_DATA_TYPES", "details": {"typeErrors": [{"actualType": "string", "actualValue": "two", "expectedType": "number", "field": "adults"}]}, "message": "Request contains fields with invalid data types"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.error.details.typeErrors": {"match": "type", "min": 1}, "$.body.error.details.typeErrors[*].actualType": {"match": "type"}, "$.body.error.details.typeErrors[*].actualValue": {"match": "type"}, "$.body.error.details.typeErrors[*].expectedType": {"match": "type"}, "$.body.error.details.typeErrors[*].field": {"match": "type"}, "$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 400}}, {"description": "a booking request with invalid date formats", "providerState": "request has invalid date formats", "request": {"body": {"adults": 2, "checkIn": "2024/12/01", "checkOut": "01-12-2024", "children": 0, "infants": 0, "propertyId": "property-123"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/bookings"}, "response": {"body": {"error": {"code": "INVALID_DATE_FORMAT", "details": {"invalidDates": [{"expectedFormat": "YYYY-MM-DD", "field": "checkIn", "value": "2024/12/01"}]}, "message": "Date fields must be in YYYY-MM-DD format"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.error.details.invalidDates": {"match": "type", "min": 1}, "$.body.error.details.invalidDates[*].field": {"match": "type"}, "$.body.error.details.invalidDates[*].value": {"match": "type"}, "$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 400}}, {"description": "a booking request with missing required fields", "providerState": "request has missing required fields", "request": {"body": {"adults": 2, "checkIn": "2024-12-01", "checkOut": "2024-12-05"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/bookings"}, "response": {"body": {"error": {"code": "MISSING_REQUIRED_FIELDS", "details": {"missingFields": ["propertyId"], "providedFields": ["checkIn"]}, "message": "Request is missing required fields"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.error.details.missingFields": {"match": "type", "min": 1}, "$.body.error.details.providedFields": {"match": "type", "min": 1}, "$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 400}}, {"description": "a quote request without guest details", "providerState": "quote request missing guest information", "request": {"body": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "propertyId": "property-123"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/quotes"}, "response": {"body": {"error": {"code": "MISSING_GUEST_INFORMATION", "details": {"missingFields": ["adults", "children", "infants"], "requiredFields": ["adults", "children", "infants"]}, "message": "Guest information is required for quote calculation"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 400}}, {"description": "a request expecting 200 OK status", "providerState": "booking exists for user", "request": {"headers": {"Authorization": "Bearer valid-token"}, "method": "GET", "path": "/bookings/booking-456"}, "response": {"body": {"booking": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "confirmationNumber": "QH********9", "createdAt": "2015-08-06T16:53:10+01:00", "guests": {"adults": 2, "children": 0, "infants": 0}, "id": "booking-456", "propertyId": "property-123", "status": "confirmed", "totalAmount": {"amount": 450, "currency": "AUD"}}}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.booking.confirmationNumber": {"match": "type"}, "$.body.booking.createdAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.booking.id": {"match": "type"}, "$.body.booking.totalAmount.amount": {"match": "type"}}, "status": 200}}, {"description": "a request expecting booking response with correct schema", "providerState": "booking exists for user", "request": {"headers": {"Authorization": "Bearer valid-token"}, "method": "GET", "path": "/bookings/booking-456"}, "response": {"body": {"booking": {"checkIn": "2013-02-01", "checkOut": "2013-02-01", "confirmationNumber": "QH********9", "createdAt": "2015-08-06T16:53:10+01:00", "guests": {"adults": 2, "children": 0, "infants": 0}, "id": "booking-456", "propertyId": "property-123", "propertyName": "Test Hotel Sydney", "status": "confirmed", "totalAmount": {"amount": 450, "currency": "AUD"}, "updatedAt": "2015-08-06T16:53:10+01:00"}}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.booking.checkIn": {"match": "regex", "regex": "^([\\+-]?\\d{4}(?!\\d{2}\\b))((-?)((0[1-9]|1[0-2])(\\3([12]\\d|0[1-9]|3[01]))?|W([0-4]\\d|5[0-2])(-?[1-7])?|(00[1-9]|0[1-9]\\d|[12]\\d{2}|3([0-5]\\d|6[1-6])))?)$"}, "$.body.booking.checkOut": {"match": "regex", "regex": "^([\\+-]?\\d{4}(?!\\d{2}\\b))((-?)((0[1-9]|1[0-2])(\\3([12]\\d|0[1-9]|3[01]))?|W([0-4]\\d|5[0-2])(-?[1-7])?|(00[1-9]|0[1-9]\\d|[12]\\d{2}|3([0-5]\\d|6[1-6])))?)$"}, "$.body.booking.confirmationNumber": {"match": "type"}, "$.body.booking.createdAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.booking.guests.adults": {"match": "type"}, "$.body.booking.guests.children": {"match": "type"}, "$.body.booking.guests.infants": {"match": "type"}, "$.body.booking.id": {"match": "type"}, "$.body.booking.propertyId": {"match": "type"}, "$.body.booking.propertyName": {"match": "type"}, "$.body.booking.status": {"match": "regex", "regex": "^(confirmed|pending|cancelled)$"}, "$.body.booking.totalAmount.amount": {"match": "type"}, "$.body.booking.totalAmount.currency": {"match": "regex", "regex": "^[A-Z]{3}$"}, "$.body.booking.updatedAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request for non-existent booking", "providerState": "booking does not exist", "request": {"headers": {"Authorization": "Bearer valid-token"}, "method": "GET", "path": "/bookings/booking-404"}, "response": {"body": {"error": {"code": "BOOKING_NOT_FOUND", "details": {"bookingId": "booking-404"}, "message": "The requested booking could not be found"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 404}}, {"description": "a request for non-existent property", "providerState": "property does not exist", "request": {"headers": {"Authorization": "Bearer valid-token"}, "method": "GET", "path": "/properties/property-404"}, "response": {"body": {"error": {"code": "PROPERTY_NOT_FOUND", "details": {"propertyId": "property-404"}, "message": "The requested property could not be found"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 404}}, {"description": "a request for property search with expected schema", "providerState": "properties are available for search", "request": {"headers": {"Authorization": "Bearer valid-token"}, "method": "GET", "path": "/properties", "query": "adults=2&checkIn=2024-12-01&checkOut=2024-12-05&children=0&infants=0&location=Sydney"}, "response": {"body": {"pagination": {"limit": 10, "page": 1, "totalPages": 10, "totalResults": 95}, "properties": [{"availability": {"available": true, "roomsLeft": 5}, "id": "property-123", "location": {"city": "Sydney", "coordinates": {"latitude": -33.8688, "longitude": 151.2093}, "country": "Australia"}, "name": "Test Hotel Sydney", "pricing": {"baseRate": 200, "currency": "AUD", "totalRate": 450}, "rating": {"type": "star", "value": 4}}]}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.pagination.limit": {"match": "type"}, "$.body.pagination.page": {"match": "type"}, "$.body.pagination.totalPages": {"match": "type"}, "$.body.pagination.totalResults": {"match": "type"}, "$.body.properties": {"match": "type", "min": 1}, "$.body.properties[*].availability.available": {"match": "type"}, "$.body.properties[*].availability.roomsLeft": {"match": "type"}, "$.body.properties[*].id": {"match": "type"}, "$.body.properties[*].location.city": {"match": "type"}, "$.body.properties[*].location.coordinates.latitude": {"match": "type"}, "$.body.properties[*].location.coordinates.longitude": {"match": "type"}, "$.body.properties[*].location.country": {"match": "type"}, "$.body.properties[*].name": {"match": "type"}, "$.body.properties[*].pricing.baseRate": {"match": "type"}, "$.body.properties[*].pricing.currency": {"match": "regex", "regex": "^[A-Z]{3}$"}, "$.body.properties[*].pricing.totalRate": {"match": "type"}, "$.body.properties[*].rating.type": {"match": "regex", "regex": "^(star|circle)$"}, "$.body.properties[*].rating.value": {"match": "type"}}, "status": 200}}, {"description": "a request that causes server error", "providerState": "server encounters unexpected error", "request": {"body": {"adults": 2, "checkIn": "2024-12-01", "checkOut": "2024-12-05", "children": 0, "infants": 0, "propertyId": "property-123"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/bookings"}, "response": {"body": {"error": {"code": "INTERNAL_ERROR", "message": "An unexpected error occurred while processing the request"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 500}}, {"description": "a request that should succeed but returns error", "providerState": "service returns unexpected error status", "request": {"headers": {"Authorization": "Bearer valid-token"}, "method": "GET", "path": "/bookings/booking-456"}, "response": {"body": {"error": {"code": "SERVICE_UNAVAILABLE", "details": {"actualStatus": 503, "expectedStatus": 200, "retryAfter": 60}, "message": "Service is temporarily unavailable"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.error.details.retryAfter": {"match": "type"}, "$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 503}}, {"description": "a request to add already favourited property", "providerState": "user is authenticated as QFF member", "request": {"body": {"propertyId": "property-123"}, "headers": {"Accept": "application/json", "Authorization": "Bearer valid-token", "Content-Type": "application/json", "qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}}, "method": "POST", "path": "/member-favourites"}, "response": {"body": {"error": {"code": "FAVOURITE_ALREADY_EXISTS", "details": {"propertyId": "property-123"}, "message": "Property is already in favourites"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "success": false, "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 409}}, {"description": "a request to add property to favourites", "providerState": "user is authenticated as QFF member", "request": {"body": {"propertyId": "property-123"}, "headers": {"Accept": "application/json", "Authorization": "Bearer valid-token", "Content-Type": "application/json", "qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}}, "method": "POST", "path": "/member-favourites"}, "response": {"body": {"favourite": {"addedDate": "2015-08-06T16:53:10+01:00", "id": "fav-789", "propertyId": "property-123"}, "success": true}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.favourite.addedDate": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.favourite.id": {"match": "type"}}, "status": 201}}, {"description": "a request to authenticate QFF member", "providerState": "user is authenticated as QFF member", "request": {"body": {"accessToken": "qff-access-token-123", "loginType": "qff", "qhUserId": "qh-user-456"}, "headers": {"Accept": "application/json", "Content-Type": "application/json"}, "method": "POST", "path": "/authenticate"}, "response": {"body": {"authToken": "jwt-auth-token-789", "memberDetails": {"email": "<EMAIL>", "firstName": "<PERSON>", "isVip": true, "lastName": "<PERSON>", "memberId": "********", "pointsBalance": 125000, "preferences": {"currency": "AUD", "language": "en-AU"}, "tierStatus": "Platinum"}, "success": true, "tokenInfo": {"expiresAt": "2015-08-06T16:53:10+01:00", "issuedAt": "2015-08-06T16:53:10+01:00", "scope": ["hotels", "profile", "bookings"]}}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.authToken": {"match": "type"}, "$.body.memberDetails.pointsBalance": {"match": "type"}, "$.body.tokenInfo.expiresAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.tokenInfo.issuedAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request to authenticate guest user", "providerState": "user is not authenticated", "request": {"body": {"guestDetails": {"email": "<EMAIL>", "firstName": "Guest", "lastName": "User"}, "loginType": "guest"}, "headers": {"Accept": "application/json", "Content-Type": "application/json"}, "method": "POST", "path": "/authenticate"}, "response": {"body": {"authToken": "guest-jwt-token-123", "guestDetails": {"email": "<EMAIL>", "firstName": "Guest", "isGuest": true, "lastName": "User"}, "success": true, "tokenInfo": {"expiresAt": "2015-08-06T16:53:10+01:00", "issuedAt": "2015-08-06T16:53:10+01:00", "scope": ["hotels", "guest-bookings"]}}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.authToken": {"match": "type"}, "$.body.tokenInfo.expiresAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.tokenInfo.issuedAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request to authenticate with invalid QFF token", "providerState": "user has invalid authentication token", "request": {"body": {"accessToken": "invalid-qff-token", "loginType": "qff", "qhUserId": "qh-user-456"}, "headers": {"Accept": "application/json", "Content-Type": "application/json"}, "method": "POST", "path": "/authenticate"}, "response": {"body": {"error": {"code": "INVALID_QFF_TOKEN", "message": "QFF access token is invalid or expired"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "success": false, "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 401}}, {"description": "a request to authenticate with missing required fields", "providerState": "user is not authenticated", "request": {"body": {"loginType": "qff"}, "headers": {"Accept": "application/json", "Content-Type": "application/json"}, "method": "POST", "path": "/authenticate"}, "response": {"body": {"error": {"code": "VALIDATION_ERROR", "details": {"missingFields": ["accessToken", "qhUserId"]}, "message": "Required fields are missing"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "success": false, "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 400}}, {"description": "a request to calculate points burn amount", "providerState": "user is authenticated as QFF member", "request": {"body": {"cashAmount": {"currency": "AUD", "value": 50000}, "memberId": "********", "memberTier": "gold", "pointsAmount": 90000, "propertyType": "standard"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/points/calculate-burn"}, "response": {"body": {"calculation": {"actualPointsAmount": 90000, "burnRate": {"dollarPerPoint": 0.0056, "pointsPerDollar": 1.8}, "cashEquivalent": {"currency": "AUD", "value": 50400}, "memberTier": "gold", "requestedPointsAmount": 90000, "savings": {"currency": "AUD", "percentage": 0.8, "value": 400}}, "memberBalance": {"availablePoints": 150000, "remainingPoints": 60000, "tierStatus": "gold"}, "validation": {"isValid": true, "sufficientBalance": true, "withinLimits": true}}, "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to calculate points burn with insufficient balance", "providerState": "user is authenticated as QFF member", "request": {"body": {"cashAmount": {"currency": "AUD", "value": 100000}, "memberId": "********", "memberTier": "bronze", "pointsAmount": 300000, "propertyType": "standard"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/points/calculate-burn"}, "response": {"body": {"error": {"code": "INSUFFICIENT_POINTS_BALANCE", "details": {"availablePoints": 150000, "requestedPoints": 300000, "shortfall": 150000}, "message": "Insufficient points balance for requested burn amount"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 422}}, {"description": "a request to calculate points earn amount", "providerState": "user is authenticated as QFF member", "request": {"body": {"bookingAmount": {"currency": "AUD", "value": 75000}, "memberId": "********", "memberTier": "silver", "propertyId": "property-123", "propertyType": "standard", "stayDates": {"checkIn": "2024-12-01", "checkOut": "2024-12-05"}}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/points/calculate-earn"}, "response": {"body": {"breakdown": {"baseEarning": {"amount": {"currency": "AUD", "value": 75000}, "points": 2250, "rate": 3}, "promotionalBonus": {"campaigns": [], "points": 0}, "tierBonus": {"multiplier": 1.33, "points": 750, "rate": 1, "tier": "silver"}}, "calculation": {"basePoints": 2250, "bonusPoints": 750, "earnRate": {"basePointsPerDollar": 3, "bonusPointsPerDollar": 1, "totalPointsPerDollar": 4}, "memberTier": "silver", "totalPoints": 3000}, "memberStatus": {"currentTier": "silver", "nextTier": "gold", "pointsToNextTier": 47000, "tierProgress": 0.53}}, "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to calculate points earn with promotional campaign", "providerState": "active campaigns exist", "request": {"body": {"bookingAmount": {"currency": "AUD", "value": 100000}, "campaignCode": "DOUBLE-POINTS-2024", "memberId": "********", "memberTier": "gold", "propertyId": "property-123", "propertyType": "luxury", "stayDates": {"checkIn": "2024-12-01", "checkOut": "2024-12-05"}}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/points/calculate-earn"}, "response": {"body": {"breakdown": {"baseEarning": {"amount": {"currency": "AUD", "value": 100000}, "points": 3000, "rate": 3}, "promotionalBonus": {"campaigns": [{"bonusPoints": 5000, "code": "DOUBLE-POINTS-2024", "multiplier": 2, "name": "Double Points Campaign"}], "points": 5000}, "tierBonus": {"multiplier": 1.67, "points": 2000, "rate": 2, "tier": "gold"}}, "calculation": {"basePoints": 3000, "bonusPoints": 2000, "earnRate": {"basePointsPerDollar": 3, "bonusPointsPerDollar": 2, "promotionalPointsPerDollar": 5, "totalPointsPerDollar": 10}, "memberTier": "gold", "promotionalPoints": 5000, "totalPoints": 10000}, "memberStatus": {"currentTier": "gold", "nextTier": "platinum", "pointsToNextTier": 10000, "tierProgress": 0.9}}, "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to fetch luxury points burn tiers", "providerState": "content exists", "request": {"method": "GET", "path": "/points-burn-luxe"}, "response": {"body": {"pointsTierInstance": {"family": "points-burn-luxe", "levels": [{"benefits": ["Luxury property access", "Enhanced burn rate"], "burnRate": {"dollarPerPoint": 0.005, "pointsPerDollar": 2}, "maxPoints": 99999, "minPoints": 0, "propertyTypes": ["luxury", "boutique"], "tier": "luxe-bronze"}, {"benefits": ["Premium luxury access", "Better burn rate", "Concierge service"], "burnRate": {"dollarPerPoint": 0.0059, "pointsPerDollar": 1.7}, "maxPoints": 249999, "minPoints": 100000, "propertyTypes": ["luxury", "boutique", "premium"], "tier": "luxe-silver"}, {"benefits": ["Exclusive luxury access", "Best burn rate", "VIP concierge", "Room upgrades"], "burnRate": {"dollarPerPoint": 0.0071, "pointsPerDollar": 1.4}, "maxPoints": null, "minPoints": 250000, "propertyTypes": ["luxury", "boutique", "premium", "exclusive"], "tier": "luxe-gold"}], "metadata": {"category": "luxury", "currency": "AUD", "lastUpdated": "2015-08-06T16:53:10+01:00", "version": "1.3"}, "name": "points-burn-luxe"}}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.pointsTierInstance.metadata.lastUpdated": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request to fetch points burn tiers", "providerState": "content exists", "request": {"method": "GET", "path": "/points-burn-v2"}, "response": {"body": {"pointsTierInstance": {"family": "points-burn", "levels": [{"benefits": ["Standard burn rate"], "burnRate": {"dollarPerPoint": 0.004, "pointsPerDollar": 2.5}, "maxPoints": 49999, "minPoints": 0, "tier": "bronze"}, {"benefits": ["Enhanced burn rate", "Priority support"], "burnRate": {"dollarPerPoint": 0.005, "pointsPerDollar": 2}, "maxPoints": 99999, "minPoints": 50000, "tier": "silver"}, {"benefits": ["Premium burn rate", "Priority support", "Bonus points"], "burnRate": {"dollarPerPoint": 0.0056, "pointsPerDollar": 1.8}, "maxPoints": 199999, "minPoints": 100000, "tier": "gold"}, {"benefits": ["Best burn rate", "VIP support", "Bonus points", "Exclusive offers"], "burnRate": {"dollarPerPoint": 0.0067, "pointsPerDollar": 1.5}, "maxPoints": null, "minPoints": 200000, "tier": "platinum"}], "metadata": {"currency": "AUD", "lastUpdated": "2015-08-06T16:53:10+01:00", "version": "2.1"}, "name": "points-burn-v2"}}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.pointsTierInstance.metadata.lastUpdated": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request to fetch points burn tiers when service unavailable", "providerState": "content does not exist", "request": {"method": "GET", "path": "/points-burn-v2"}, "response": {"body": {"error": {"code": "SERVICE_UNAVAILABLE", "message": "Points burn service is temporarily unavailable"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 503}}, {"description": "a request to get member details", "providerState": "user is authenticated as QFF member", "request": {"headers": {"Accept": "application/json", "Authorization": "Bearer valid-token", "qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}}, "method": "GET", "path": "/member-details/********"}, "response": {"body": {"member": {"memberId": "********", "membershipDetails": {"accountStatus": "active", "joinDate": "2018-03-15", "lastLoginDate": "2015-08-06T16:53:10+01:00"}, "personalDetails": {"dateOfBirth": "1985-06-15", "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "phone": "+614********"}, "preferences": {"communicationPreferences": {"email": true, "push": true, "sms": false}, "currency": "AUD", "language": "en-AU", "marketingOptIn": true, "timezone": "Australia/Sydney"}, "qffDetails": {"isVip": false, "nextTierRequirement": {"creditsNeeded": 150, "tier": "Platinum"}, "pointsBalance": 75000, "statusCredits": 450, "tierStatus": "Gold"}}}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.member.membershipDetails.lastLoginDate": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.member.qffDetails.nextTierRequirement.creditsNeeded": {"match": "type"}, "$.body.member.qffDetails.pointsBalance": {"match": "type"}, "$.body.member.qffDetails.statusCredits": {"match": "type"}}, "status": 200}}, {"description": "a request to get member details with invalid token", "providerState": "user has invalid authentication token", "request": {"headers": {"Accept": "application/json", "Authorization": "Bearer invalid-token", "qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}}, "method": "GET", "path": "/member-details/********"}, "response": {"body": {"error": {"code": "UNAUTHORIZED", "message": "Invalid or expired authentication token"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 401}}, {"description": "a request to get member favourites", "providerState": "user is authenticated as QFF member", "request": {"headers": {"Accept": "application/json", "Authorization": "Bearer valid-token", "qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}}, "method": "GET", "path": "/member-favourites", "query": "limit=20&page=1"}, "response": {"body": {"favourites": [{"addedDate": "2024-01-15T10:30:00Z", "id": "fav-123", "lastViewedDate": "2024-02-20T14:45:00Z", "propertyId": "property-123", "propertyLocation": {"city": "Sydney", "coordinates": {"latitude": -33.8688, "longitude": 151.2093}, "country": "Australia"}, "propertyName": "Sydney Harbour Hotel", "propertyRating": {"type": "star", "value": 5}}, {"addedDate": "2024-02-01T09:15:00Z", "id": "fav-456", "lastViewedDate": "2024-02-18T16:20:00Z", "propertyId": "property-456", "propertyLocation": {"city": "Melbourne", "coordinates": {"latitude": -37.8136, "longitude": 144.9631}, "country": "Australia"}, "propertyName": "Melbourne Grand Hotel", "propertyRating": {"type": "star", "value": 4}}], "pagination": {"limit": 20, "page": 1, "totalPages": 1, "totalResults": 2}}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.favourites[0].propertyLocation.coordinates.latitude": {"match": "type"}, "$.body.favourites[0].propertyLocation.coordinates.longitude": {"match": "type"}, "$.body.favourites[0].propertyRating.value": {"match": "type"}, "$.body.favourites[1].propertyLocation.coordinates.latitude": {"match": "type"}, "$.body.favourites[1].propertyLocation.coordinates.longitude": {"match": "type"}, "$.body.favourites[1].propertyRating.value": {"match": "type"}}, "status": 200}}, {"description": "a request to get member favourites with no favourites", "providerState": "user is authenticated as QFF member", "request": {"headers": {"Accept": "application/json", "Authorization": "Bearer valid-token", "qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}}, "method": "GET", "path": "/member-favourites", "query": "limit=20&page=1"}, "response": {"body": {"favourites": [], "pagination": {"limit": 20, "page": 1, "totalPages": 0, "totalResults": 0}}, "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to get non-existent member details", "providerState": "user is authenticated as QFF member", "request": {"headers": {"Accept": "application/json", "Authorization": "Bearer valid-token", "qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}}, "method": "GET", "path": "/member-details/99999999"}, "response": {"body": {"error": {"code": "MEMBER_NOT_FOUND", "details": {"memberId": "99999999"}, "message": "Member not found"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 404}}, {"description": "a request to non-existent API endpoint", "providerState": "API endpoint does not exist", "request": {"headers": {"Authorization": "Bearer valid-token"}, "method": "GET", "path": "/non-existent-endpoint"}, "response": {"body": {"error": {"code": "ENDPOINT_NOT_FOUND", "details": {"method": "GET", "path": "/non-existent-endpoint"}, "message": "The requested API endpoint does not exist"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 404}}, {"description": "a request to process combination payment with insufficient points", "providerState": "payment will fail", "request": {"body": {"bookingReference": "booking-failed-points", "cashPayment": {"amount": {"currency": "AUD", "value": 33000}, "paymentMethod": {"encryptedCardNumber": "adyenjs_0_1_25$encrypted-card-number", "encryptedExpiryMonth": "adyenjs_0_1_25$encrypted-expiry-month", "encryptedExpiryYear": "adyenjs_0_1_25$encrypted-expiry-year", "encryptedSecurityCode": "adyenjs_0_1_25$encrypted-security-code", "holderName": "<PERSON>", "type": "scheme"}}, "memberId": "********", "pointsPayment": {"amount": 200000, "cashEquivalent": {"currency": "AUD", "value": 112000}}, "totalAmount": {"currency": "AUD", "value": 145000}}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/points/process-combination-payment"}, "response": {"body": {"error": {"code": "POINTS_TRANSACTION_FAILED", "details": {"cashTransactionStatus": "not_attempted", "pointsAvailable": 150000, "pointsRequested": 200000, "shortfall": 50000, "transactionId": null}, "message": "Points transaction failed due to insufficient balance"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 422}}, {"description": "a request to process combination points and cash payment", "providerState": "payment can be processed", "request": {"body": {"bookingReference": "booking-combo-123", "cashPayment": {"amount": {"currency": "AUD", "value": 17000}, "paymentMethod": {"encryptedCardNumber": "adyenjs_0_1_25$encrypted-card-number", "encryptedExpiryMonth": "adyenjs_0_1_25$encrypted-expiry-month", "encryptedExpiryYear": "adyenjs_0_1_25$encrypted-expiry-year", "encryptedSecurityCode": "adyenjs_0_1_25$encrypted-security-code", "holderName": "<PERSON>", "type": "scheme"}}, "memberId": "********", "pointsPayment": {"amount": 50000, "cashEquivalent": {"currency": "AUD", "value": 28000}}, "totalAmount": {"currency": "AUD", "value": 45000}}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/points/process-combination-payment"}, "response": {"body": {"confirmationDetails": {"confirmationNumber": "QH-COMBO-456789", "processedAt": "2015-08-06T16:53:10+01:00"}, "paymentResult": {"bookingReference": "booking-combo-123", "breakdown": {"cashContribution": {"currency": "AUD", "percentage": 37.8, "value": 17000}, "pointsContribution": {"currency": "AUD", "percentage": 62.2, "value": 28000}}, "cashTransaction": {"amount": {"currency": "AUD", "value": 17000}, "paymentMethod": {"brand": "visa", "type": "scheme"}, "pspReference": "8816178914218518", "resultCode": "Authorised", "status": "completed"}, "pointsTransaction": {"cashEquivalent": {"currency": "AUD", "value": 28000}, "memberBalance": {"newBalance": 100000, "previousBalance": 150000}, "pointsDeducted": 50000, "status": "completed", "transactionId": "pts-txn-789"}, "status": "completed", "totalAmount": {"currency": "AUD", "value": 45000}}}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.confirmationDetails.confirmationNumber": {"match": "type"}, "$.body.confirmationDetails.processedAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.paymentResult.cashTransaction.pspReference": {"match": "type"}, "$.body.paymentResult.pointsTransaction.transactionId": {"match": "type"}}, "status": 200}}, {"description": "a request to remove non-existent favourite", "providerState": "user is authenticated as QFF member", "request": {"headers": {"Accept": "application/json", "Authorization": "Bearer valid-token", "qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}}, "method": "DELETE", "path": "/member-favourites/fav-404"}, "response": {"body": {"error": {"code": "FAVOURITE_NOT_FOUND", "details": {"favouriteId": "fav-404"}, "message": "Favourite not found"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "success": false, "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 404}}, {"description": "a request to remove property from favourites", "providerState": "user is authenticated as QFF member", "request": {"headers": {"Accept": "application/json", "Authorization": "Bearer valid-token", "qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}}, "method": "DELETE", "path": "/member-favourites/fav-123"}, "response": {"body": {"message": "Property removed from favourites", "removedFavourite": {"id": "fav-123", "removedDate": "2015-08-06T16:53:10+01:00"}, "success": true}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.removedFavourite.removedDate": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request to update member profile", "providerState": "user is authenticated as QFF member", "request": {"body": {"personalDetails": {"firstName": "<PERSON>", "lastName": "<PERSON>", "phone": "+61423456789"}, "preferences": {"communicationPreferences": {"email": true, "push": false, "sms": true}, "currency": "USD", "language": "en-US", "marketingOptIn": false, "timezone": "America/New_York"}}, "headers": {"Accept": "application/json", "Authorization": "Bearer valid-token", "Content-Type": "application/json", "qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}}, "method": "PUT", "path": "/member-details/********"}, "response": {"body": {"member": {"memberId": "********", "personalDetails": {"dateOfBirth": "1985-06-15", "email": "<EMAIL>", "firstName": "<PERSON>", "lastName": "<PERSON>", "phone": "+61423456789"}, "preferences": {"communicationPreferences": {"email": true, "push": false, "sms": true}, "currency": "USD", "language": "en-US", "marketingOptIn": false, "timezone": "America/New_York"}, "updatedAt": "2015-08-06T16:53:10+01:00"}, "success": true}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.member.updatedAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request to update member profile with invalid data", "providerState": "user is authenticated as QFF member", "request": {"body": {"personalDetails": {"firstName": "", "phone": "invalid-phone"}, "preferences": {"currency": "INVALID", "language": "invalid-lang"}}, "headers": {"Accept": "application/json", "Authorization": "Bearer valid-token", "Content-Type": "application/json", "qh-meta": "{\"qhUserId\":\"user-123\"}"}, "matchingRules": {"$.header['qh-meta']": {"match": "type"}}, "method": "PUT", "path": "/member-details/********"}, "response": {"body": {"error": {"code": "VALIDATION_ERROR", "details": {"validationErrors": [{"field": "personalDetails.firstName", "message": "First name cannot be empty"}, {"field": "personalDetails.phone", "message": "Invalid phone number format"}, {"field": "preferences.currency", "message": "Invalid currency code"}, {"field": "preferences.language", "message": "Invalid language code"}]}, "message": "Invalid data provided"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "success": false, "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 400}}, {"description": "a request to validate a valid authentication token", "providerState": "user is authenticated as QFF member", "request": {"headers": {"Accept": "application/json", "Authorization": "Bearer valid-jwt-token-123"}, "method": "GET", "path": "/validate"}, "response": {"body": {"memberDetails": {"email": "<EMAIL>", "firstName": "<PERSON>", "isVip": false, "lastName": "<PERSON><PERSON>", "memberId": "********", "pointsBalance": 50000, "tierStatus": "Gold"}, "tokenInfo": {"expiresAt": "2015-08-06T16:53:10+01:00", "issuedAt": "2015-08-06T16:53:10+01:00", "scope": ["hotels", "profile"]}, "valid": true}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.memberDetails.pointsBalance": {"match": "type"}, "$.body.tokenInfo.expiresAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.tokenInfo.issuedAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 200}}, {"description": "a request to validate an expired authentication token", "providerState": "user has expired authentication token", "request": {"headers": {"Accept": "application/json", "Authorization": "Bearer expired-jwt-token-456"}, "method": "GET", "path": "/validate"}, "response": {"body": {"error": {"code": "TOKEN_EXPIRED", "details": {"expiredAt": "2015-08-06T16:53:10+01:00"}, "message": "Authentication token has expired"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00", "valid": false}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.error.details.expiredAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 401}}, {"description": "a request to validate an invalid authentication token", "providerState": "user has invalid authentication token", "request": {"headers": {"Accept": "application/json", "Authorization": "Bearer invalid-jwt-token"}, "method": "GET", "path": "/validate"}, "response": {"body": {"error": {"code": "INVALID_TOKEN", "message": "Authentication token is invalid or malformed"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00", "valid": false}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 401}}, {"description": "a request to validate token without authorization header", "providerState": "user is not authenticated", "request": {"headers": {"Accept": "application/json"}, "method": "GET", "path": "/validate"}, "response": {"body": {"error": {"code": "MISSING_AUTHORIZATION", "message": "Authorization header is required"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00", "valid": false}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 401}}, {"description": "a request when database is down", "providerState": "database is unavailable", "request": {"headers": {"Authorization": "Bearer valid-token"}, "method": "GET", "path": "/bookings/booking-456"}, "response": {"body": {"error": {"code": "DATABASE_ERROR", "details": {"type": "database_connection_failure"}, "message": "An internal server error occurred"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 500}}, {"description": "a request with expired authentication token", "providerState": "user has expired authentication token", "request": {"headers": {"Authorization": "Bearer expired-token"}, "method": "GET", "path": "/bookings/booking-456"}, "response": {"body": {"error": {"code": "TOKEN_EXPIRED", "details": {"expiredAt": "2015-08-06T16:53:10+01:00"}, "message": "Authentication token has expired"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.error.details.expiredAt": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}, "$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 401}}, {"description": "a request with invalid authentication token", "providerState": "user has invalid authentication token", "request": {"headers": {"Authorization": "Bearer invalid-token"}, "method": "GET", "path": "/bookings/booking-456"}, "response": {"body": {"error": {"code": "INVALID_TOKEN", "message": "Authentication token is invalid or malformed"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 401}}, {"description": "a request with invalid booking payload", "providerState": "request has invalid payload data", "request": {"body": {"adults": -1, "checkIn": "invalid-date", "checkOut": "2024-12-05", "propertyId": ""}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/bookings"}, "response": {"body": {"error": {"code": "INVALID_REQUEST", "details": {"validationErrors": [{"code": "REQUIRED_FIELD", "field": "propertyId", "message": "Property ID cannot be empty"}]}, "message": "Request contains invalid data"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.error.details.validationErrors": {"match": "type", "min": 1}, "$.body.error.details.validationErrors[*].code": {"match": "type"}, "$.body.error.details.validationErrors[*].field": {"match": "type"}, "$.body.error.details.validationErrors[*].message": {"match": "type"}, "$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 400}}, {"description": "a request without Authorization header", "providerState": "request has no authentication token", "request": {"method": "GET", "path": "/bookings/booking-456"}, "response": {"body": {"error": {"code": "MISSING_AUTHENTICATION", "message": "Authentication token is required"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 401}}, {"description": "a request without Content-Type header", "providerState": "request is missing required headers", "request": {"body": {"checkIn": "2024-12-01", "checkOut": "2024-12-05", "propertyId": "property-123"}, "headers": {"Authorization": "Bearer valid-token", "Content-Type": "application/json"}, "method": "POST", "path": "/bookings"}, "response": {"body": {"error": {"code": "MISSING_HEADER", "details": {"missingHeader": "Content-Type"}, "message": "Required header is missing"}, "requestId": "ce118b6e-d8e1-11e7-9296-cec278b6b50a", "timestamp": "2015-08-06T16:53:10+01:00"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"$.body.requestId": {"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}, "$.body.timestamp": {"match": "regex", "regex": "^\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)$"}}, "status": 400}}], "metadata": {"pact-js": {"version": "15.0.1"}, "pactRust": {"ffi": "0.4.22", "models": "1.2.3"}, "pactSpecification": {"version": "2.0.0"}}, "provider": {"name": "unknown-provider"}}}}