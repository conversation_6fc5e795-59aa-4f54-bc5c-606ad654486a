/**
 * Contract tests for Sanity CMS content endpoints
 * Tests the consumer contract for content retrieval, campaigns, and FAQs
 */

const { Pact, Matchers } = require('@pact-foundation/pact');
const path = require('path');
const axios = require('axios');
const { HTTP_STATUS, HTTP_HEADERS, CONTENT_TYPES, PROVIDER_STATES } = require('../shared/constants');

// Define fail function for test assertions
const fail = (message) => {
  throw new Error(message);
};

describe('Sanity CMS Content Endpoints Contract Tests', () => {
  let provider;
  const mockServerBaseUrl = 'http://localhost:1238';

  beforeAll(() => {
    provider = new Pact({
      consumer: 'qantas-hotels-ui',
      provider: 'sanity-cms',
      port: 1238,
      log: path.resolve(process.cwd(), 'logs', 'pact.log'),
      dir: path.resolve(process.cwd(), 'pacts'),
      logLevel: 'INFO',
    });

    return provider.setup();
  });

  afterAll(() => {
    return provider.finalize();
  });

  afterEach(async () => {
    await provider.verify();
  });

  describe('GET /content - FAQ Content', () => {
    describe('successful FAQ retrieval', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.CONTENT.FAQS_AVAILABLE,
          uponReceiving: 'a request to fetch FAQ content',
          withRequest: {
            method: 'GET',
            path: '/content',
            query: {
              type: 'faqs',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              content: [
                {
                  _id: Matchers.somethingLike('faq-1'),
                  _type: 'faq',
                  question: Matchers.somethingLike('How do I make a booking?'),
                  answer: Matchers.somethingLike('You can make a booking by selecting your dates and property.'),
                  category: Matchers.somethingLike('booking'),
                  order: Matchers.integer(1),
                  _createdAt: Matchers.iso8601DateTime(),
                  _updatedAt: Matchers.iso8601DateTime(),
                },
              ],
              _type: 'faqs',
              lastModified: Matchers.iso8601DateTime(),
            },
          },
        });
      });

      it('should fetch FAQ content successfully', async () => {
        const response = await axios({
          method: 'GET',
          url: `${mockServerBaseUrl}/content`,
          params: {
            type: 'faqs',
          },
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toMatchObject({
          content: expect.arrayContaining([
            expect.objectContaining({
              _type: 'faq',
              question: expect.any(String),
              answer: expect.any(String),
              category: expect.any(String),
              order: expect.any(Number),
            }),
          ]),
          _type: 'faqs',
        });
        expect(response.data.lastModified).toBeDefined();
      });
    });
  });

  describe('GET /content - Terms and Conditions', () => {
    describe('successful terms and conditions retrieval', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.CONTENT.CONTENT_EXISTS,
          uponReceiving: 'a request to fetch terms and conditions content',
          withRequest: {
            method: 'GET',
            path: '/content',
            query: {
              type: 'termsAndConditions',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              content: {
                _id: Matchers.somethingLike('terms-1'),
                _type: 'termsAndConditions',
                title: Matchers.somethingLike('Terms and Conditions'),
                content: [
                  {
                    _key: Matchers.somethingLike('block-1'),
                    _type: 'block',
                    children: [
                      {
                        _key: Matchers.somethingLike('span-1'),
                        _type: 'span',
                        text: Matchers.somethingLike('These are the terms and conditions for using our service.'),
                      },
                    ],
                  },
                ],
                lastUpdated: Matchers.iso8601DateTime(),
                _createdAt: Matchers.iso8601DateTime(),
                _updatedAt: Matchers.iso8601DateTime(),
              },
              _type: 'termsAndConditions',
              lastModified: Matchers.iso8601DateTime(),
            },
          },
        });
      });

      it('should fetch terms and conditions content successfully', async () => {
        const response = await axios({
          method: 'GET',
          url: `${mockServerBaseUrl}/content`,
          params: {
            type: 'termsAndConditions',
          },
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toMatchObject({
          content: {
            _type: 'termsAndConditions',
            title: expect.any(String),
            content: expect.arrayContaining([
              expect.objectContaining({
                _type: 'block',
                children: expect.any(Array),
              }),
            ]),
          },
          _type: 'termsAndConditions',
        });
        expect(response.data.lastModified).toBeDefined();
      });
    });
  });

  describe('GET /content/preview - Preview Content', () => {
    describe('successful preview content retrieval', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.CONTENT.CONTENT_EXISTS,
          uponReceiving: 'a request to fetch preview content with preview token',
          withRequest: {
            method: 'GET',
            path: '/content/preview',
            headers: {
              'x-sanity-api-key': 'preview-token-123',
            },
            query: {
              type: 'faqs',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              content: {
                _id: Matchers.somethingLike('preview-content-1'),
                _type: 'faq',
                question: Matchers.somethingLike('Preview FAQ Question'),
                answer: Matchers.somethingLike('This is preview content.'),
                category: Matchers.somethingLike('preview'),
                order: Matchers.integer(1),
                _createdAt: Matchers.iso8601DateTime(),
                _updatedAt: Matchers.iso8601DateTime(),
              },
              _type: 'preview',
              lastModified: Matchers.iso8601DateTime(),
              isPreview: true,
            },
          },
        });
      });

      it('should fetch preview content with preview token successfully', async () => {
        const response = await axios({
          method: 'GET',
          url: `${mockServerBaseUrl}/content/preview`,
          headers: {
            'x-sanity-api-key': 'preview-token-123',
          },
          params: {
            type: 'faqs',
          },
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toMatchObject({
          content: {
            _type: 'faq',
            question: expect.any(String),
            answer: expect.any(String),
          },
          _type: 'preview',
          isPreview: true,
        });
        expect(response.data.lastModified).toBeDefined();
      });
    });
  });

  describe('GET /content - Content Not Found', () => {
    describe('content not found error', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.CONTENT.CONTENT_NOT_FOUND,
          uponReceiving: 'a request to fetch non-existent content type',
          withRequest: {
            method: 'GET',
            path: '/content',
            query: {
              type: 'nonExistentType',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.NOT_FOUND,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              error: {
                code: 'CONTENT_NOT_FOUND',
                message: 'Content type not found',
                details: {
                  contentType: 'nonExistentType',
                },
              },
              timestamp: Matchers.iso8601DateTime(),
              requestId: Matchers.uuid(),
            },
          },
        });
      });

      it('should return 404 for non-existent content type', async () => {
        try {
          await axios({
            method: 'GET',
            url: `${mockServerBaseUrl}/content`,
            params: {
              type: 'nonExistentType',
            },
          });
          fail('Expected request to fail with 404');
        } catch (error) {
          expect(error.response?.status).toBe(HTTP_STATUS.NOT_FOUND);
          if (error.response) {
            expect(error.response.data).toMatchObject({
              error: {
                code: 'CONTENT_NOT_FOUND',
                message: 'Content type not found',
                details: {
                  contentType: 'nonExistentType',
                },
              },
            });
            expect(error.response.data.timestamp).toBeDefined();
            expect(error.response.data.requestId).toBeDefined();
          }
        }
      });
    });
  });

  describe('GET /campaigns - Active Campaigns', () => {
    describe('successful campaign retrieval', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: PROVIDER_STATES.CAMPAIGNS_ACTIVE,
          uponReceiving: 'a request to fetch active campaigns',
          withRequest: {
            method: 'GET',
            path: '/campaigns',
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              campaigns: [
                {
                  _id: Matchers.somethingLike('campaign-1'),
                  _type: 'campaign',
                  slug: Matchers.somethingLike('summer-sale-2024'),
                  title: Matchers.somethingLike('Summer Sale 2024'),
                  description: Matchers.somethingLike('Save up to 30% on summer bookings'),
                  startDate: Matchers.iso8601DateTime(),
                  endDate: Matchers.iso8601DateTime(),
                  isActive: true,
                  bannerImage: {
                    _type: 'image',
                    asset: {
                      _ref: Matchers.somethingLike('image-abc123'),
                      _type: 'reference',
                    },
                    alt: Matchers.somethingLike('Summer Sale Banner'),
                  },
                  content: [
                    {
                      _key: Matchers.somethingLike('block-1'),
                      _type: 'block',
                      children: [
                        {
                          _key: Matchers.somethingLike('span-1'),
                          _type: 'span',
                          text: Matchers.somethingLike('Book now and save on your summer getaway!'),
                        },
                      ],
                    },
                  ],
                  _createdAt: Matchers.iso8601DateTime(),
                  _updatedAt: Matchers.iso8601DateTime(),
                },
              ],
              lastModified: Matchers.iso8601DateTime(),
            },
          },
        });
      });

      it('should fetch active campaigns successfully', async () => {
        const response = await axios({
          method: 'GET',
          url: `${mockServerBaseUrl}/campaigns`,
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toMatchObject({
          campaigns: expect.arrayContaining([
            expect.objectContaining({
              _type: 'campaign',
              slug: expect.any(String),
              title: expect.any(String),
              description: expect.any(String),
              isActive: true,
              bannerImage: expect.objectContaining({
                _type: 'image',
                asset: expect.objectContaining({
                  _type: 'reference',
                }),
              }),
              content: expect.any(Array),
            }),
          ]),
        });
        expect(response.data.campaigns).toHaveLength(1);
        expect(response.data.lastModified).toBeDefined();
      });
    });
  });

  describe('GET /campaigns - No Active Campaigns', () => {
    describe('no campaigns available', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: 'no active campaigns exist',
          uponReceiving: 'a request to fetch campaigns when none are active',
          withRequest: {
            method: 'GET',
            path: '/campaigns',
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              campaigns: [],
              lastModified: Matchers.iso8601DateTime(),
            },
          },
        });
      });

      it('should return empty campaigns array when no campaigns are active', async () => {
        const response = await axios({
          method: 'GET',
          url: `${mockServerBaseUrl}/campaigns`,
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toMatchObject({
          campaigns: [],
        });
        expect(response.data.lastModified).toBeDefined();
      });
    });
  });

  describe('GET /site-message - Site Message Available', () => {
    describe('successful site message retrieval', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: 'site message exists for page',
          uponReceiving: 'a request to fetch site message for search page',
          withRequest: {
            method: 'GET',
            path: '/site-message',
            query: {
              pageName: 'search',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              message: {
                _id: Matchers.somethingLike('site-message-1'),
                _type: 'siteMessage',
                pageName: 'search',
                content: Matchers.somethingLike('Welcome to our hotel search page!'),
                isActive: true,
                priority: Matchers.integer(1),
                messageType: Matchers.somethingLike('info'),
                _createdAt: Matchers.iso8601DateTime(),
                _updatedAt: Matchers.iso8601DateTime(),
              },
              lastModified: Matchers.iso8601DateTime(),
            },
          },
        });
      });

      it('should fetch site message for specific page successfully', async () => {
        const response = await axios({
          method: 'GET',
          url: `${mockServerBaseUrl}/site-message`,
          params: {
            pageName: 'search',
          },
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toMatchObject({
          message: {
            _type: 'siteMessage',
            pageName: 'search',
            content: expect.any(String),
            isActive: true,
            priority: expect.any(Number),
            messageType: expect.any(String),
          },
        });
        expect(response.data.lastModified).toBeDefined();
      });
    });
  });

  describe('GET /site-message - No Site Message', () => {
    describe('no site message available', () => {
      beforeEach(() => {
        return provider.addInteraction({
          state: 'no site message exists for page',
          uponReceiving: 'a request to fetch site message for page with no message',
          withRequest: {
            method: 'GET',
            path: '/site-message',
            query: {
              pageName: 'checkout',
            },
          },
          willRespondWith: {
            status: HTTP_STATUS.OK,
            headers: {
              [HTTP_HEADERS.CONTENT_TYPE]: CONTENT_TYPES.JSON,
            },
            body: {
              message: null,
              lastModified: Matchers.iso8601DateTime(),
            },
          },
        });
      });

      it('should return null message when no site message exists for page', async () => {
        const response = await axios({
          method: 'GET',
          url: `${mockServerBaseUrl}/site-message`,
          params: {
            pageName: 'checkout',
          },
        });

        expect(response.status).toBe(HTTP_STATUS.OK);
        expect(response.data).toMatchObject({
          message: null,
        });
        expect(response.data.lastModified).toBeDefined();
      });
    });
  });
});
