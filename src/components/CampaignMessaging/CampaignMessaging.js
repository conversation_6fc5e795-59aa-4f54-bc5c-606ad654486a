import React, { useRef } from 'react';
import { useSelector } from 'react-redux';
import isEmpty from 'lodash/isEmpty';
import { Flex, Box } from '@qga/roo-ui/components';
import { getCampaignName, getCampaignTitle, getCampaignMessage, getCampaignIsEnabled } from 'store/campaign/campaignSelectors';
import CampaignMessageLink from './CampaignMessageLink';
import { BannerText } from './primitives';
import { CAMPAIGN_BANNER_ENABLED } from 'config';
import { getIsExclusive } from 'store/property/propertySelectors';
import useViewPromotionEvent from 'hooks/useViewPromotionEvent';
import { useBreakpoints } from 'hooks/useBreakpoints';
import useStandardCampaign from 'hooks/optimizely/useStandardCampaign';

const CampaignMessaging = () => {
  const ref = useRef(null);
  const globalCampaignName = useSelector(getCampaignName);
  const globalCampaignTitle = useSelector(getCampaignTitle);
  const globalCampaignMessage = useSelector(getCampaignMessage);
  const globalCampaignEnabled = useSelector(getCampaignIsEnabled);

  const { isStandardCampaignEnabled, message: standardCampaignMessage, title: standardCampaignTitle } = useStandardCampaign();

  const { isGreaterThanOrEqualToBreakpoint } = useBreakpoints();
  const isNotMobile = isGreaterThanOrEqualToBreakpoint(0);
  const isExclusive = useSelector(getIsExclusive);

  useViewPromotionEvent({
    ref,
    promotion: {
      name: globalCampaignName,
      slot: 'banner',
    },
  });

  const title = globalCampaignEnabled ? (globalCampaignTitle ?? null) : isStandardCampaignEnabled ? (standardCampaignTitle ?? null) : null;

  const message = globalCampaignEnabled
    ? (globalCampaignMessage ?? null)
    : isStandardCampaignEnabled
      ? (standardCampaignMessage ?? null)
      : null;

  const noContent = isEmpty(title) && isEmpty(message);
  const noActiveCampaigns = !globalCampaignEnabled && !isStandardCampaignEnabled;

  if (!CAMPAIGN_BANNER_ENABLED || isExclusive || noActiveCampaigns || noContent) return null;

  return (
    <Box ref={ref} data-testid="campaign-banner-wrapper">
      <Flex bg="bayBlue30" justifyContent="center" alignItems="center" p={3} flexDirection={['column', 'row']}>
        <CampaignMessageLink title={title}>
          <BannerText data-testid="title" dangerouslySetInnerHTML={{ __html: title }} />
          {!isEmpty(message) && (
            <>
              {isNotMobile && !isEmpty(title) && (
                <BannerText mx="1" xs>
                  -
                </BannerText>
              )}
              <BannerText data-testid="message">{message}</BannerText>
            </>
          )}
        </CampaignMessageLink>
      </Flex>
    </Box>
  );
};

export default CampaignMessaging;
