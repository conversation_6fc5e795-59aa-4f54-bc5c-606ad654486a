import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { Provider } from 'react-redux';
import testStore from 'redux-mock-store';
import CampaignMessaging from './CampaignMessaging';
import { getCampaignTitle, getCampaignName, getCampaignMessage, getCampaignIsEnabled } from 'store/campaign/campaignSelectors';
import useStandardCampaign from 'hooks/optimizely/useStandardCampaign';
import { getIsExclusive } from 'store/property/propertySelectors';
import { useBreakpoints } from 'hooks/useBreakpoints';

import * as config from 'config';

jest.mock('config', () => ({
  CAMPAIGN_BANNER_ENABLED: true,
}));
jest.mock('store/campaign/campaignSelectors');
jest.mock('store/property/propertySelectors');
jest.mock('hooks/optimizely/useYieldifyCampaign');
jest.mock('hooks/optimizely/useStandardCampaign');
jest.mock('hooks/useBreakpoints');
jest.mock('hooks/useViewPromotionEvent', () => ({
  __esModule: true,
  default: jest.fn(),
}));

const mockStore = testStore([]);

const setup = (initialState = {}, props = {}) => {
  const store = mockStore({
    campaign: {
      name: '',
      title: '',
      message: '',
    },
    property: {
      isExclusive: false,
    },
    ...initialState,
  });

  return render(
    <Provider store={store}>
      <CampaignMessaging {...props} />
    </Provider>,
  );
};

describe('CampaignMessaging', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    config.CAMPAIGN_BANNER_ENABLED = true;
    getCampaignIsEnabled.mockReturnValue(false);

    getCampaignName.mockReturnValue(null);
    getCampaignTitle.mockReturnValue(null);
    getCampaignMessage.mockReturnValue(null);
    getIsExclusive.mockReturnValue(false);

    useStandardCampaign.mockReturnValue({
      isReady: true,
      isStandardCampaignEnabled: false,
      message: '',
      title: '',
    });
    useBreakpoints.mockReturnValue({
      isGreaterThanOrEqualToBreakpoint: jest.fn(() => true),
    });
  });

  it('does not render if no global or standard campaigns are active', () => {
    setup();
    expect(screen.queryByTestId('campaign-banner-wrapper')).not.toBeInTheDocument();
  });

  describe('Global Campaign', () => {
    describe('when is active', () => {
      beforeEach(() => {
        getCampaignIsEnabled.mockReturnValue(true);
      });

      it('renders the global campaign content when available', () => {
        getCampaignTitle.mockReturnValue('Global Campaign Title');
        getCampaignMessage.mockReturnValue('Global Campaign Message');

        setup();

        expect(screen.getByText('Global Campaign Title')).toBeInTheDocument();
        expect(screen.getByText('Global Campaign Message')).toBeInTheDocument();
      });

      it('does not render the standard campaign content when enabled', () => {
        useStandardCampaign.mockReturnValue({
          isReady: true,
          isStandardCampaignEnabled: true,
          message: 'Standard Campaign Message',
          title: 'Standard Campaign Title',
        });

        getCampaignTitle.mockReturnValue('Global Campaign Title');
        getCampaignMessage.mockReturnValue('Global Campaign Message');

        setup();

        expect(screen.queryByText('Standard Campaign Title')).not.toBeInTheDocument();
        expect(screen.queryByText('Standard Campaign Message')).not.toBeInTheDocument();
      });
    });

    describe('when is not Active', () => {
      beforeEach(() => {
        getCampaignIsEnabled.mockReturnValue(false);
      });

      it('renders the standard campaign content when enabled', () => {
        useStandardCampaign.mockReturnValue({
          isReady: true,
          isStandardCampaignEnabled: true,
          message: 'Standard Campaign Message',
          title: 'Standard Campaign Title',
        });
        setup();

        expect(screen.getByText('Standard Campaign Title')).toBeInTheDocument();
        expect(screen.getByText('Standard Campaign Message')).toBeInTheDocument();
      });

      it('does NOT render if the standard campaign content is not available', () => {
        useStandardCampaign.mockReturnValue({
          isReady: true,
          isStandardCampaignEnabled: false,
          message: '',
          title: '',
        });

        setup();

        expect(screen.queryByTestId('campaign-banner-wrapper')).not.toBeInTheDocument();
      });
    });
  });

  it('does not render if isExclusive is true', () => {
    getCampaignIsEnabled.mockReturnValue(true);
    getIsExclusive.mockReturnValue(true);
    getCampaignTitle.mockReturnValue('Global Campaign Title');
    setup(undefined);
    expect(screen.queryByTestId('campaign-banner-wrapper')).not.toBeInTheDocument();
  });

  it('displays the title with rich text link if provided by global campaign', () => {
    getCampaignIsEnabled.mockReturnValue(true);
    getCampaignTitle.mockReturnValue(
      'Earn <a href="https://www.qantas.com/test-link">double Qantas Points on Hotels & Holidays</a> bookings',
    );
    getCampaignMessage.mockReturnValue('Some message');
    setup();
    const titleElement = screen.getByTestId('title');
    expect(titleElement).toBeInTheDocument();
    expect(titleElement).toHaveTextContent('Earn double Qantas Points on Hotels & Holidays bookings');
    const linkElement = screen.getByRole('link', { name: /double Qantas Points on Hotels & Holidays/i });
    expect(linkElement).toHaveAttribute('href', 'https://www.qantas.com/test-link');
  });

  it('does not display the message if the message text is blank for global campaign', () => {
    getCampaignIsEnabled.mockReturnValue(true);
    getCampaignTitle.mockReturnValue('Global Campaign Title');
    getCampaignMessage.mockReturnValue('');
    setup();
    expect(screen.getByTestId('title')).toBeInTheDocument();
    expect(screen.queryByTestId('message')).not.toBeInTheDocument();
  });

  it('does not display the message if the message text is blank for standard campaign', () => {
    useStandardCampaign.mockReturnValue({
      isReady: true,
      isStandardCampaignEnabled: true,
      message: '',
      title: 'Standard Campaign Title',
    });
    setup();
    expect(screen.getByTestId('title')).toBeInTheDocument();
    expect(screen.queryByTestId('message')).not.toBeInTheDocument();
  });

  it('hides the hyphen separator on mobile when title is present', () => {
    getCampaignIsEnabled.mockReturnValue(true);
    useBreakpoints.mockReturnValue({
      isGreaterThanOrEqualToBreakpoint: jest.fn((breakpoint) => breakpoint !== 0),
    });
    getCampaignTitle.mockReturnValue('Global Campaign Title');
    getCampaignMessage.mockReturnValue('Global Campaign Message');
    setup();
    expect(screen.queryByText('-')).not.toBeInTheDocument();
  });

  it('shows the hyphen separator on desktop when title and message are present', () => {
    getCampaignIsEnabled.mockReturnValue(true);
    getCampaignTitle.mockReturnValue('Global Campaign Title');
    getCampaignMessage.mockReturnValue('Global Campaign Message');
    setup();
    expect(screen.getByText('-')).toBeInTheDocument();
  });
});
