import React from 'react';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import OfferDetailsModal from './OfferDetailsModal';
import { renderWithProviders } from 'test-utils/reactUtils/renderWithProviders';
import { useDataLayer } from 'hooks/useDataLayer';
import type { Inclusion } from 'types/property';
import useCustomInclusions from 'hooks/optimizely/useCustomInclusions';
import { mocked } from 'test-utils';

jest.mock('hooks/optimizely/useCustomInclusions');
jest.mock('hooks/useDataLayer');
jest.mock('store/ui/uiSelectors');
jest.mock('components/OfferInclusions', () => ({ inclusions }) => (
  <div data-testid="offer-inclusions">
    <div>
      Inclusions:
      {inclusions?.map((item: Inclusion, index: number) => (
        <div key={index}>{item.name}</div>
      ))}
    </div>
  </div>
));

const defaultProps = {
  offerDescription: 'Offer description',
  offerName: 'Offer name',
  roomName: 'Room name',
};

const propsWithInclusions = {
  ...defaultProps,
  inclusions: [{ name: 'Breakfast' }, { name: 'WiFi' }],
};

const render = (props = {}) => renderWithProviders(<OfferDetailsModal {...defaultProps} {...props} />);
const emitInteractionEvent = jest.fn();

// Helper function to open modal
const openModal = async (_, buttonName = /show details of offer name/i) => {
  await userEvent.click(await screen.findByRole('button', { name: buttonName }));
};

beforeEach(() => {
  (useDataLayer as jest.Mock).mockReturnValue({ emitInteractionEvent });
  mocked(useCustomInclusions).mockReturnValue({
    isReady: true,
    isCustomInclusions: false,
  });
});

it('renders the CTA', () => {
  render();
  expect(screen.getByRole('button', { name: /show details of offer name/i })).toBeInTheDocument();
});

describe('Modal functionality', () => {
  it('dispatches event to data layer when opened', async () => {
    render();
    await openModal(userEvent);

    expect(emitInteractionEvent).toHaveBeenCalledWith({ type: 'Offer Details Pop Up', value: 'Link Selected' });
  });

  it('displays open button with correct text', async () => {
    render();
    await openModal(userEvent);

    expect(screen.getByText('View offer details')).toBeInTheDocument();
  });

  describe('the modal body', () => {
    it('displays the room name when available', async () => {
      render();
      await openModal(userEvent);

      expect(screen.getByText(defaultProps.roomName)).toBeInTheDocument();
    });

    it('displays offer name when available', async () => {
      render();
      await openModal(userEvent);

      expect(screen.getByText(defaultProps.offerName)).toBeInTheDocument();
    });

    describe('inclusions', () => {
      it('does not display the inclusions block when not available', async () => {
        render();
        await openModal(userEvent);

        expect(screen.queryByText('Inclusions:')).not.toBeInTheDocument();
      });

      it('displays inclusions title when available', async () => {
        render({ ...propsWithInclusions });
        await openModal(userEvent);

        expect(screen.getByText('INCLUSIONS:')).toBeInTheDocument();
      });

      it('sends the Inclusions list to OfferInclusions when available', async () => {
        render(propsWithInclusions);
        await openModal(userEvent);

        propsWithInclusions.inclusions.forEach((inclusion) => {
          expect(screen.getByTestId('offer-inclusions')).toHaveTextContent(inclusion.name);
        });
      });
    });

    describe('offerDescription when the useCustomInclusions flag', () => {
      describe('is false', () => {
        it('does not render even if the description when available', async () => {
          render();
          await openModal(userEvent);

          const offerDescription = screen.queryAllByTestId('offer-description');
          expect(offerDescription).toHaveLength(0);
        });

        it('does not render when the offer description is not available', async () => {
          render({ defaultProps, offerDescription: null });
          await openModal(userEvent);
          const offerDescription = screen.queryAllByTestId('offer-description');
          expect(offerDescription).toHaveLength(0);
        });
      });
      describe('is true', () => {
        beforeEach(() => {
          (useCustomInclusions as jest.Mock).mockReturnValue({
            isReady: true,
            isCustomInclusions: true,
          });
        });

        it('only displays offer description when available', async () => {
          render();
          await openModal(userEvent);

          const offerDescription = screen.getByTestId('offer-description');
          expect(offerDescription).toHaveTextContent(defaultProps.offerDescription);
        });

        it('does not render it if offer description is not available', async () => {
          render({ defaultProps, offerDescription: null });
          await openModal(userEvent);
          const offerDescription = screen.queryAllByTestId('offer-description');
          expect(offerDescription).toHaveLength(0);
        });
      });
    });
  });

  describe('Button accessibility', () => {
    it('has correct aria-label based on offer name', () => {
      render();
      expect(screen.getByRole('button')).toHaveAttribute('aria-label', 'Show details of Offer name');
    });

    it('updates aria-label when offer name changes', () => {
      render({ offerName: 'Special Deal' });
      expect(screen.getByRole('button')).toHaveAttribute('aria-label', 'Show details of Special Deal');
    });

    describe('when useCustomInclusions is true', () => {
      beforeEach(() => {
        (useCustomInclusions as jest.Mock).mockReturnValue({
          isReady: true,
          isCustomInclusions: true,
        });
      });

      it.each(['{Enter}', ' '])('can be activated with %s key', async (key) => {
        render();

        const button = screen.getByRole('button', { name: /show details of offer name/i });
        button.focus();
        await userEvent.keyboard(key);

        expect(screen.getByTestId('offer-description')).toBeInTheDocument();
      });

      it('handles empty offer name in aria-label', () => {
        render({ offerName: '' });
        expect(screen.getByRole('button')).toHaveAttribute('aria-label', 'Show details of ');
      });
    });
  });

  describe('Edge cases', () => {
    beforeEach(() => {
      (useCustomInclusions as jest.Mock).mockReturnValue({
        isReady: true,
        isCustomInclusions: true,
      });
    });
    it('handles long offer descriptions', async () => {
      const longDescription =
        'This is a very long offer description that contains multiple sentences and should still be displayed correctly in the modal without any truncation or formatting issues.';
      render({ offerDescription: longDescription });

      await openModal(userEvent);
      expect(screen.getByText(longDescription)).toBeInTheDocument();
    });

    it('handles special characters in offer name and description', async () => {
      const specialOfferName = 'Offer & Deal™ (50% off)';
      const specialDescription = 'Special characters: @#$%^&*()_+-={}[]|\\:";\'<>?,./';
      render({ offerName: specialOfferName, offerDescription: specialDescription });

      const escapedName = specialOfferName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      await openModal(userEvent, new RegExp(`show details of ${escapedName}`, 'i'));
      expect(screen.getByText(specialDescription)).toBeInTheDocument();
    });

    it('renders with minimal required props', () => {
      render({ offerName: 'Test Offer', offerDescription: undefined });
      expect(screen.getByRole('button')).toBeInTheDocument();
    });
  });
});
