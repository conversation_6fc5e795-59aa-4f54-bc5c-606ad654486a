import React, { useCallback } from 'react';
import { Heading, Text, Flex } from '@qga/roo-ui/components';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import { useModal } from 'lib/hooks';
import { useDataLayer } from 'hooks/useDataLayer';
import Modal from 'components/Modal';
import TextButton from 'components/TextButton';
import OfferInclusions from 'components/OfferInclusions';
import type { Inclusion, Offer } from 'types/property';
import useCustomInclusions from 'hooks/optimizely/useCustomInclusions';

export interface OfferDetailsModalProps {
  offerDescription?: string | null;
  offerName?: string;
  roomName?: string;
  inclusions?: Inclusion[];
  valueAdds?: Offer['valueAdds'];
}

const ModalSection = styled(Flex)<{ py?: string }>`
  border-bottom: 1px solid ${themeGet('colors.greys.alto')};
  padding: ${themeGet('space.6')};
  padding-block: ${(props) => props.py || themeGet('space.6')};
  flex-direction: column;
  &:last-child {
    border-bottom: none;
  }
`;

const OfferDetailsModal: React.FC<OfferDetailsModalProps> = ({ offerDescription = null, offerName, roomName, inclusions, valueAdds }) => {
  const { openModal, modalProps } = useModal();
  const { emitInteractionEvent } = useDataLayer();
  const { isCustomInclusions } = useCustomInclusions();
  const showDescription = isCustomInclusions && offerDescription;

  const handleOnClick = useCallback(() => {
    openModal();
    emitInteractionEvent({ type: 'Offer Details Pop Up', value: 'Link Selected' });
  }, [emitInteractionEvent, openModal]);

  return (
    <>
      <TextButton onClick={handleOnClick} color="greys.steel" fontSize="sm" aria-label={`Show details of ${offerName}`} mb={3}>
        View offer details
      </TextButton>
      <Modal {...modalProps} title="Offer details" fullScreen={false} padding={0} footerComponent={null} titleSize={['md']}>
        <Flex mb="5" flexDirection={'column'}>
          <ModalSection py="16px">
            {roomName && (
              <Heading.h4 fontSize="22px" mb={0}>
                {roomName}
              </Heading.h4>
            )}
            {offerName && <Text fontSize={'md'}>{offerName}</Text>}
          </ModalSection>
          {inclusions && (
            <ModalSection>
              <Text fontSize="14px" fontWeight={'bold'} pb={2}>
                INCLUSIONS:
              </Text>
              <OfferInclusions inclusions={inclusions} valueAdds={valueAdds} viewAll={true} />
            </ModalSection>
          )}
          {showDescription && (
            <ModalSection data-testid="offer-description">
              <Text py={4} fontSize="14px">
                {offerDescription}
              </Text>
            </ModalSection>
          )}
        </Flex>
      </Modal>
    </>
  );
};

export default OfferDetailsModal;
