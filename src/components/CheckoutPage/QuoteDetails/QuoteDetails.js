import React, { Fragment } from 'react';
import { useSelector } from 'react-redux';
import { getQuote, getStayDates, getOccupants } from 'store/quote/quoteSelectors';
import get from 'lodash/get';
import { Wrapper, Box, Text, Flex } from '@qga/roo-ui/components';
import PropertyDetails from './PropertyDetails';
import StayDates from './StayDates';
import PriceDetails from './PriceDetails';
import QuoteItem from './QuoteItem';
import CancellationRefundModal from 'components/CancellationRefundModal';
import OccupantsSummary from 'components/OccupantsSummary';
import PointsEarnSummary from './PointsEarnSummary';
import RoomsAvailabilityMessage from 'components/RoomsAvailabilityMessage';
import useAvailableRoomsMessage from 'hooks/optimizely/useAvailableRoomsMessage';
import OfferInclusions from 'components/OfferInclusions';

const QuoteDetails = ({ ...rest }) => {
  const { showMessage } = useAvailableRoomsMessage();
  const quote = useSelector(getQuote);
  const { checkIn, checkOut } = useSelector(getStayDates) || {};
  const occupants = useSelector(getOccupants);

  if (!quote) return null;

  const { property, offer, roomType } = quote;
  const inclusions = get(offer, 'inclusions');
  const valueAdds = get(offer, 'valueAdds');
  const hasInclusions = inclusions?.length > 0;

  const cancellationPolicy = get(offer, 'cancellationPolicy');
  const allocationsAvailable = get(offer, 'allocationsAvailable', 0);

  return (
    <Wrapper {...rest}>
      <Box mb={[0, 10]}>
        <PropertyDetails property={property} roomType={roomType} offer={offer} />
        {offer && (
          <Fragment>
            <Box boxShadow="hard" borderRadius="defaultRoundBottomOnly" overflow="hidden">
              <Box px={[3, 8]} bg="white" pb={4}>
                {hasInclusions && (
                  <QuoteItem borderBottom={1} py={0}>
                    <OfferInclusions inclusions={inclusions} valueAdds={valueAdds} isExpandText={true} />
                  </QuoteItem>
                )}
                <QuoteItem>
                  <Flex justifyContent="space-between" mb={1}>
                    <Text>Guests</Text>
                    <OccupantsSummary occupants={occupants} />
                  </Flex>
                  <StayDates checkIn={checkIn} checkOut={checkOut} />
                  <CancellationRefundModal cancellationPolicy={cancellationPolicy} fontSize="sm" mt={4} flexDirection="row" />
                </QuoteItem>
                <QuoteItem borderBottom={0}>
                  <PointsEarnSummary pointsEarned={offer.pointsEarned} />
                  {showMessage && <RoomsAvailabilityMessage allocationsAvailable={allocationsAvailable} mt={1} />}
                </QuoteItem>
              </Box>
              <PriceDetails
                property={property}
                offer={offer}
                checkIn={checkIn}
                checkOut={checkOut}
                p={6}
                bg="lightBlue"
                borderTop={1}
                borderColor="greys.alto"
              />
            </Box>
          </Fragment>
        )}
      </Box>
    </Wrapper>
  );
};

export default QuoteDetails;
