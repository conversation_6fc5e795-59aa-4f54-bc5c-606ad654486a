import React from 'react';
import isArray from 'lodash/isArray';
import find from 'lodash/find';
import { themeGet } from 'styled-system';
import styled from '@emotion/styled';
import { Text, Flex, Box } from '@qga/roo-ui/components';
import Image from 'components/Image';
import PromotionalSash from 'components/PromotionalSash';
import PromotionalSashRedVariant from 'components/PromotionalSashRedVariant';
import RoomTypeDetails from './RoomTypeDetails';
import QuoteItem from 'components/CheckoutPage/QuoteDetails/QuoteItem';
import { useShowRedSashToggle } from 'hooks/useShowRedSashToggle';
import { CustomerRating, Property, RoomType, Offer } from 'types/property';
import PropertyRatings from 'components/PropertyRatings';
import OfferDetailsModal from 'components/OfferDetailsModal';

const AbsolutePromoBox = styled(Box)`
  position: absolute;
  top: 0;
  left: ${themeGet('space.2')};
`;

export interface PropertyDetailsProps {
  property: Property;
  roomType?: RoomType | null;
  offer?: Offer | null;
}

const getTripAdvisorRating = (customerRatings?: CustomerRating[]): CustomerRating | null => {
  if (!isArray(customerRatings)) return null;
  const found = find(customerRatings, ['source', 'trip_advisor']);
  return found ?? null;
};

const PropertyDetails: React.FC<PropertyDetailsProps> = ({ property, roomType = null, offer = null }) => {
  const { rating, ratingType, mainImage, name } = property;
  const { urlMedium, urlLarge, caption } = mainImage || {};
  const { description: offerDescription, name: offerName, inclusions, valueAdds } = offer || {};
  const hasPromotion = !!offer?.promotion;
  const hasRoomAndOffer = !!(roomType && offer) && !!offerName;
  const propertyImageSrcSet = mainImage ? `${urlLarge} 2x, ${urlMedium} 1x` : undefined;
  const tripAdvisorRating = getTripAdvisorRating(property.customerRatings);
  const promotionName = offer?.promotion?.name || '';
  const { isReady, showRedSash } = useShowRedSashToggle({ promotionName });

  return (
    <Flex flexDirection="column" position="relative" borderRadius="defaultRoundTopOnly" overflow="hidden">
      {hasPromotion && (
        <AbsolutePromoBox>
          {isReady && showRedSash ? (
            <PromotionalSashRedVariant promotionName={promotionName} />
          ) : (
            <PromotionalSash promotionName={promotionName} />
          )}
        </AbsolutePromoBox>
      )}
      <Image height="200px" lazy src={urlMedium} alt={caption} srcSet={propertyImageSrcSet} width="100%" />
      <Box px={[3, 8]} bg="white">
        <QuoteItem>
          <Flex>
            <Flex flexDirection="column" width="100%">
              <Text fontSize="base" fontWeight="bold" color="greys.charcoal">
                {name}
              </Text>
              <Flex flexDirection="row" alignItems="center" mt={1}>
                <PropertyRatings
                  hotelRating={rating}
                  hotelRatingType={ratingType}
                  hotelRatingSize={16}
                  customerRating={tripAdvisorRating}
                />
              </Flex>
            </Flex>
          </Flex>
          {hasRoomAndOffer && (
            <Flex mt={2} flexDirection="column">
              <RoomTypeDetails roomType={roomType} />
              <Text fontSize={['sm', 'base']} my={1} data-testid="offer-name">
                {offerName}
              </Text>
            </Flex>
          )}
          <OfferDetailsModal
            inclusions={inclusions}
            valueAdds={valueAdds}
            offerDescription={offerDescription}
            offerName={offerName}
            roomName={roomType?.name}
          />
        </QuoteItem>
      </Box>
    </Flex>
  );
};

export default PropertyDetails;
