/* eslint-disable @typescript-eslint/no-unused-vars */
import React from 'react';
import { screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import PropertyDetails from './PropertyDetails';
import type { PropertyDetailsProps } from './PropertyDetails';
import type { CustomerRating, Offer, RoomType } from 'types/property';
import { useShowRedSashToggle } from 'hooks/useShowRedSashToggle';
import { mocked } from 'test-utils';
import { renderWithProviders } from 'test-utils/reactUtils';
import { offerComplete as offer } from 'components/PropertyPage/PropertyAvailability/RoomTypes/RoomTypeList/Offer/components/offer.fixture';

jest.mock('components/Image', () => {
  return jest.fn(({ src, alt }: { src: string; alt: string }) => <img src={src} alt={alt} data-testid="mock-image" />);
});
jest.mock('components/PropertyRatings', () => {
  return jest.fn(({ hotelRating, customerRating }: { hotelRating: number; customerRating: CustomerRating | null }) => (
    <div data-testid="mock-property-ratings">
      {hotelRating && <span data-testid="mock-hotel-rating">Hotel Rating: {hotelRating}</span>}
      {customerRating && <span data-testid="mock-customer-rating">Customer Rating: {JSON.stringify(customerRating)}</span>}
    </div>
  ));
});

jest.mock('./RoomTypeDetails', () => {
  return jest.fn(({ roomType }: { roomType: RoomType }) => <div data-testid="mock-room-type-details">{roomType?.name}</div>);
});
jest.mock('components/PromotionalSash', () => {
  return jest.fn(() => <div data-testid="mock-promotional-sash">Promotional Sash</div>);
});
jest.mock('components/PromotionalSashRedVariant', () => {
  return jest.fn(() => <div data-testid="mock-promotional-sash-red">Promotional Sash Red Variant</div>);
});
jest.mock('components/OfferDetailsModal', () => {
  return jest.fn(({ offerName, offerDescription, inclusions, roomName }) => (
    <div data-testid="mock-offer-details-modal">
      <div>
        Inclusions:
        {inclusions?.map((item, index) => (
          <div key={index}>{item.name}</div>
        ))}
      </div>
      <div>Offer Name: {offerName}</div>
      <div>Offer Description: {offerDescription}</div>
      <div>Room Name: {roomName}</div>
    </div>
  ));
});

jest.mock('@qga/roo-ui/components', () => ({
  StarRating: jest.fn(({ rating, ratingType, ...props }: Record<string, unknown>) => <div data-testid="roo-ui-star-rating" {...props} />),
  Box: ({ children, flexDirection, borderBottom, borderColor, borderRadius, ...props }: Record<string, unknown>) => (
    <div data-testid="roo-ui-box">{children}</div>
  ),
  Text: ({ children, ...props }: Record<string, unknown>) => (
    <span data-testid="roo-ui-text" {...props}>
      {children}
    </span>
  ),
  Flex: ({ children, flexDirection, ...props }: Record<string, unknown>) => <div data-testid="roo-ui-flex">{children}</div>,
}));

jest.mock('hooks/useShowRedSashToggle');

const defaultProps: PropertyDetailsProps = {
  property: {
    id: 'property-id',
    name: 'Grand Hotel',
    description: 'A nice hotel',
    category: 'Hotel',
    address: {
      streetAddress: 'Main St.',
      suburb: 'Melbourne',
      state: 'Vic',
      postcode: '3000',
      country: 'Au',
      countryCode: 'AU',
    },
    images: [],
    mainImage: {
      urlMedium: 'www.imageMedium.com',
      urlLarge: 'www.imageLarge.com',
      caption: 'image description',
      urlSmall: '',
      urlOriginal: '',
    },
    customerRatings: [],
    roomType: {
      name: 'room name',
      id: 'room-id',
      maxOccupantCount: 2,
      offer: [],
      images: [],
      featuredOfferId: '',
      mainImage: {
        caption: '',
        urlSmall: '',
        urlOriginal: '',
        urlMedium: '',
        urlLarge: '',
      },
      RoomTypeFacilities: {},
    },
    roomTypes: [],
    ratingType: 'SELF_RATED',
    rating: 5,
    promotionSashes: [],
    featuredOfferId: '',
  },
  offer: offer,
  roomType: {
    name: 'room name',
    id: 'room-id',
    maxOccupantCount: 2,
    offer: [],
    images: [],
    featuredOfferId: '',
    mainImage: {
      caption: '',
      urlSmall: '',
      urlOriginal: '',
      urlMedium: '',
      urlLarge: '',
    },
    RoomTypeFacilities: {},
  },
};

describe('<PropertyDetails />', () => {
  const { property } = defaultProps;

  beforeEach(() => {
    jest.clearAllMocks();
    mocked(useShowRedSashToggle).mockReturnValue({ showRedSash: false, isReady: false });
  });

  describe('when promotion is present', () => {
    it('returns <PromotionalSash />', () => {
      renderWithProviders(<PropertyDetails {...defaultProps} />);

      expect(screen.getByTestId('mock-promotional-sash')).toBeInTheDocument();
    });
  });

  describe('when promotion is not present', () => {
    it('does not return <PromotionalSash />', () => {
      defaultProps.offer!.promotion = null;
      renderWithProviders(<PropertyDetails {...defaultProps} />);

      expect(screen.queryByTestId('mock-promotional-sash')).not.toBeInTheDocument();
    });
  });

  it('renders the property image with the correct props', () => {
    renderWithProviders(<PropertyDetails {...defaultProps} />);

    expect(screen.getByTestId('mock-image')).toHaveAttribute('alt', property.mainImage.caption);
    expect(screen.getByTestId('mock-image')).toHaveAttribute('src', property.mainImage.urlMedium);
  });

  it('renders the property name', () => {
    renderWithProviders(<PropertyDetails {...defaultProps} />);
    const propertyName = property.name;

    expect(screen.getByText(propertyName)).toBeInTheDocument();
  });

  it('renders the star ratings with the correct props', () => {
    renderWithProviders(<PropertyDetails {...defaultProps} />);

    expect(screen.getByTestId('mock-hotel-rating')).toBeInTheDocument();
  });

  it('does not render star rating if rating is 0', () => {
    const propsWithZeroRating = {
      ...defaultProps,
      property: {
        ...defaultProps.property,
        rating: 0,
      },
    };

    renderWithProviders(<PropertyDetails {...propsWithZeroRating} />);

    expect(screen.queryByTestId('roo-ui-star-rating')).not.toBeInTheDocument();
  });

  it('hides the TripAdvisor rating when absent', () => {
    const propsWithoutTripAdvisor = {
      ...defaultProps,
      property: {
        ...defaultProps.property,
        customerRatings: [],
      },
    };
    renderWithProviders(<PropertyDetails {...propsWithoutTripAdvisor} />);

    expect(screen.queryByTestId('mock-tripadvisor-rating')).not.toBeInTheDocument();
  });

  it('shows the TripAdvisor rating when present', () => {
    const tripAdvisorRating = {
      tripAdvisorId: '1388984',
      source: 'trip_advisor',
      id: '14546728',
      ratingImageUrl: 'www.tripadvisor.com/img/cdsi/img2/ratings/traveler/4.5-15969-4.png',
      averageRating: 4.5,
      reviewCount: 4352,
    };
    const props = {
      ...defaultProps,
      property: {
        ...defaultProps.property,
        customerRatings: [tripAdvisorRating],
      },
    };
    renderWithProviders(<PropertyDetails {...props} />);

    expect(screen.getByTestId('mock-property-ratings')).toBeInTheDocument();
    expect(screen.getByTestId('mock-hotel-rating')).toHaveTextContent('Hotel Rating: 5');
    expect(screen.getByTestId('mock-customer-rating')).toHaveTextContent(JSON.stringify(tripAdvisorRating));
  });

  it('hides the PropertyRatings when both ratings are absent', () => {
    const propsWithoutRatings = {
      ...defaultProps,
      property: {
        ...defaultProps.property,
        customerRatings: [],
        rating: 0,
      },
    };
    renderWithProviders(<PropertyDetails {...propsWithoutRatings} />);

    expect(screen.queryByTestId('mock-hotel-rating')).not.toBeInTheDocument();
    expect(screen.queryByTestId('mock-customer-rating')).not.toBeInTheDocument();
  });

  it('only shows hotel rating if customer rating is absent', () => {
    const propsWithOnlyHotelRating = {
      ...defaultProps,
      property: {
        ...defaultProps.property,
        customerRatings: [],
        rating: 4,
      },
    };
    renderWithProviders(<PropertyDetails {...propsWithOnlyHotelRating} />);

    expect(screen.getByTestId('mock-property-ratings')).toBeInTheDocument();
    expect(screen.getByTestId('mock-hotel-rating')).toHaveTextContent('Hotel Rating: 4');
    expect(screen.queryByTestId('mock-customer-rating')).not.toBeInTheDocument();
  });

  describe('when the room and offer are present', () => {
    it('renders the RoomTypeDetails', () => {
      renderWithProviders(<PropertyDetails {...defaultProps} />);

      expect(screen.getByTestId('mock-room-type-details')).toBeInTheDocument();
      expect(screen.getByTestId('mock-room-type-details')).toHaveTextContent(defaultProps.roomType!.name);
    });

    it('renders the offer name', () => {
      renderWithProviders(<PropertyDetails {...defaultProps} />);

      expect(screen.getByTestId('offer-name')).toBeInTheDocument();
      expect(screen.getByTestId('offer-name')).toHaveTextContent(defaultProps.offer!.name);
    });

    it('renders the OfferDetailsModal', () => {
      renderWithProviders(<PropertyDetails {...defaultProps} />);

      expect(screen.getByTestId('mock-offer-details-modal')).toBeInTheDocument();
      expect(screen.getByTestId('mock-offer-details-modal')).toHaveTextContent(defaultProps.offer!.name);
    });
  });

  describe('when the room type is not present due to an unavailable quote', () => {
    it('does not render the RoomTypeDetails', () => {
      const props = {
        ...defaultProps,
        roomType: null,
      };
      renderWithProviders(<PropertyDetails {...props} />);

      expect(screen.queryByTestId('mock-room-type-details')).not.toBeInTheDocument();
    });

    it('does not render the OfferDetails', () => {
      const props = {
        ...defaultProps,
        roomType: null,
      };
      renderWithProviders(<PropertyDetails {...props} />);

      expect(screen.queryByTestId('mock-offer-details')).not.toBeInTheDocument();
    });
  });

  describe('when the offer is not present due to an unavailable quote', () => {
    it('does not render the RoomTypeDetails', () => {
      const props = {
        ...defaultProps,
        offer: null,
      };
      renderWithProviders(<PropertyDetails {...props} />);

      expect(screen.queryByTestId('mock-room-type-details')).not.toBeInTheDocument();
    });

    it('does not render the OfferDetails', () => {
      const props = {
        ...defaultProps,
        offer: null,
      };
      renderWithProviders(<PropertyDetails {...props} />);

      expect(screen.queryByTestId('mock-offer-details')).not.toBeInTheDocument();
    });
  });
});
describe('when useShowRedSashToggle is not ready', () => {
  beforeEach(() => {
    mocked(useShowRedSashToggle).mockImplementation(({ promotionName }) => {
      if (promotionName?.toLowerCase() === 'member deal') {
        return { showRedSash: false, isReady: false };
      }
      return { showRedSash: false, isReady: false };
    });
  });

  it('does not render PromotionalSashRedVariant', () => {
    defaultProps.offer!.promotion = {
      name: 'Member deal',
    };
    renderWithProviders(<PropertyDetails {...defaultProps} />);

    expect(screen.queryByTestId('mock-promotional-sash-red')).not.toBeInTheDocument();
    expect(screen.getByTestId('mock-promotional-sash')).toBeInTheDocument();
  });
});

describe('when useShowRedSashToggle is ready', () => {
  beforeEach(() => {
    mocked(useShowRedSashToggle).mockImplementation(({ promotionName }) => {
      if (promotionName?.toLowerCase() === 'member deal') {
        return { showRedSash: true, isReady: true };
      }
      return { showRedSash: false, isReady: true };
    });
  });
  describe('and showRedSash is false', () => {
    it('does not render PromotionalSashRedVariant', () => {
      mocked(useShowRedSashToggle).mockImplementation(({ promotionName }) => {
        if (promotionName?.toLowerCase() === 'member deal') {
          return { showRedSash: false, isReady: true };
        }
        return { showRedSash: false, isReady: true };
      });
      defaultProps.offer!.promotion = {
        name: 'Member deal',
      };
      renderWithProviders(<PropertyDetails {...defaultProps} />);

      expect(screen.queryByTestId('mock-promotional-sash-red')).not.toBeInTheDocument();
      expect(screen.getByTestId('mock-promotional-sash')).toBeInTheDocument();
    });
  });

  describe('and showRedSash is true and promotion name is "Member deal"', () => {
    it('renders PromotionalSashRedVariant', () => {
      defaultProps.offer!.promotion = {
        name: 'Member deal',
      };
      renderWithProviders(<PropertyDetails {...defaultProps} />);

      expect(screen.getByTestId('mock-promotional-sash-red')).toBeInTheDocument();
      expect(screen.queryByTestId('mock-promotional-sash')).not.toBeInTheDocument();
    });
  });
  describe('and showRedSash is true and promotion name is not "Member deal"', () => {
    it('does not render PromotionalSashRedVariant', () => {
      defaultProps.offer!.promotion = {
        name: 'luxury offer',
      };
      renderWithProviders(<PropertyDetails {...defaultProps} />);

      expect(screen.queryByTestId('mock-promotional-sash-red')).not.toBeInTheDocument();
      expect(screen.getByTestId('mock-promotional-sash')).toBeInTheDocument();
    });
  });

  describe('OfferDetailsModal', () => {
    it('renders the OfferDetailsModal component', () => {
      renderWithProviders(<PropertyDetails {...defaultProps} />);
      expect(screen.getByTestId('mock-offer-details-modal')).toBeInTheDocument();
    });

    it('sends the inclusions to the component', () => {
      renderWithProviders(<PropertyDetails {...defaultProps} />);
      const inclusions = defaultProps.offer?.inclusions ?? [];
      inclusions.forEach((inclusion) => {
        if (inclusion?.name) {
          expect(screen.getByText(inclusion.name)).toBeInTheDocument();
        }
      });
    });
  });
  it('sends the offer description to the component', () => {
    renderWithProviders(<PropertyDetails {...defaultProps} />);
    expect(screen.getByText(`Offer Description: ${defaultProps.offer?.description}`)).toBeInTheDocument();
  });
  it('sends the offer name to the component', () => {
    renderWithProviders(<PropertyDetails {...defaultProps} />);
    expect(screen.getByText(`Offer Name: ${defaultProps.offer?.name}`)).toBeInTheDocument();
  });
  it('sends the room name to the component', () => {
    renderWithProviders(<PropertyDetails {...defaultProps} />);
    expect(screen.getByText(`Room Name: ${defaultProps.roomType?.name}`)).toBeInTheDocument();
  });
});
