import React, { useCallback, useEffect, useRef, useState } from 'react';
import * as sessionStorage from 'lib/browser/sessionStorage';
import PropTypes from 'prop-types';
import { isEmpty } from 'lodash';
import snakeCase from 'lodash/snakeCase';
import { Box, Flex, Heading, Hide, Link } from '@qga/roo-ui/components';
import { useSelector, useDispatch } from 'react-redux';
import { useMount, useUnmount } from 'react-use';
import PageBlock from 'components/PageBlock';
import PropertyHelmet from './PropertyHelmet';
import PropertySummaryFacilities from './PropertySummary/PropertySummaryFacilities';
import PropertyDescription from './PropertyDescription';
import PropertyFacilities from './PropertyFacilities';
import StandardOfferMessage from './StandardOfferMessage';
import PropertyKnowBeforeYouGo from './PropertyKnowBeforeYouGo';
import SearchControls from './SearchControls';
import PropertyAvailability from './PropertyAvailability';
import PropertyAvailabilitySummary from './PropertyAvailabilitySummary';
import BackToSearchLink from './BackToSearchLink';
import { getProperty, getTripAdvisorRating } from 'store/property/propertySelectors';
import { getExclusiveOffer, getIsExclusiveOfferAvailable } from 'store/exclusiveOffer/exclusiveOfferSelectors';
import { getFullscreenGalleryContent, getIsMobileApp } from 'store/ui/uiSelectors';
import { getQueryParams } from 'store/router/routerSelectors';
import PropertyMap from 'components/PropertyMap';
import PropertyHeader from './PropertyHeader';
import { useModal } from 'lib/hooks';
import WelcomeMessage from 'components/WelcomeMessage';
import DynamicMessageBox from 'components/ListSearchPage/Messaging/DynamicMessageBox';
import Waypoints from 'components/Waypoints';
import FullscreenImageGallery from 'components/FullscreenImageGallery';
import LegacyBrowserBoundary from 'components/LegacyBrowserBoundary';
import RecommendedProperties from './RecommendedProperties';
import { useDataLayer } from 'hooks/useDataLayer';
import CampaignMessaging from 'components/CampaignMessaging';
import StandardImageGallery from 'components/StandardImageGallery';
import OfferDetails from './OfferDetails';
import ExclusiveOffersFetcher from './ExclusiveOffersFetcher';
import ExclusiveOfferCrossSellBanner from './ExclusiveOfferCrossSellBanner';
import ExclusiveOfferUnavailableDialog from './ExclusiveOfferUnavailableDialog';
import Markdown from 'components/Markdown';
import DisclosureSection from './DisclosureSection';
import { EXCLUSIVE_OFFERS_ENABLED } from 'config';
import { useTrackPropertyPageView } from './hooks/useTrackPropertyPageView';
import { fetchProperty, clearProperty } from 'store/property/propertyActions';
import { clearPointsBurnTiers, fetchPointsBurnTiers } from 'store/pointsBurnTiers/pointsBurnActions';
import { clearExclusiveOffer, fetchExclusiveOffer, fetchExclusiveOfferList } from 'store/exclusiveOffer/exclusiveOfferActions';
import { setIsPointsPay } from 'store/ui/uiActions';
import { useBreakpoints } from 'hooks/useBreakpoints';
import { useRouter } from 'next/router';
import RequestCallbackModal from '../RequestCallbackModal';
import BackToDealsLink from './BackToDealsLink';
import ValuePropositionsList from 'components/Content/ValuePropositionsList/ValuePropositionsList';
import { getCvp } from 'store/propertyExclusiveOffersPage/propertyExclusiveOffersPageSelectors';
import { fetchPropertyExclusiveOffersPage } from 'store/propertyExclusiveOffersPage/propertyExclusiveOffersPageActions';
import { clearGclid } from 'components/HomePage/sessionStorage';
import { clearPointsLevels } from 'store/pointsConversion/pointsConversionActions';
import { fetchCampaign } from 'store/campaign/campaignActions';
import InPageNavBar from 'components/InPageNavBar';
import { getHasValidQuery } from 'store/propertyAvailability/propertyAvailabilitySelectors';
import { ButtonBox, ButtonLink, ClickThroughWrapper } from './PropertyLayout.style';
import { UpdateNavigationIconBack } from 'lib/qta/qta';
import PropertySummaryMap from './PropertySummary/PropertySummaryMap';
import MobileAppBoundary from 'components/MobileAppBoundary';
import Renovations from 'components/PropertyPage/Renovations';
import ExclusiveContactDetails from 'components/PropertyPage/ExclusiveContactDetails';
import usePropertyPageStandardGa4Event from 'hooks/usePropertyPageStandardGa4Event';
import usePropertyPageExclusiveGa4Event from 'hooks/usePropertyPageExclusiveGa4Event';
import { updateRegion } from './localStorage';
import useCtaClickEvent from 'hooks/useCtaClickEvent';

const NavMenu = ({ ...rest }) => (
  <LegacyBrowserBoundary>
    <InPageNavBar {...rest} />
  </LegacyBrowserBoundary>
);

const PropertyLayout = ({ isExclusiveOffer = false, previewToken }) => {
  const { emitInteractionEvent } = useDataLayer();
  const { openModal, modalProps } = useModal();
  const isMobile = useBreakpoints().isLessThanBreakpoint(0);
  const hasValidQuery = useSelector(getHasValidQuery);
  const dispatch = useDispatch();
  const router = useRouter();
  const gclid = router.query.gclid ?? null;
  const { ctaClickEvent } = useCtaClickEvent();

  const handleViewRoomOnClick = useCallback(() => {
    ctaClickEvent({ itemText: 'View Rooms', itemType: 'button', url: '#view-rooms' });
    emitInteractionEvent({ type: 'From Price', value: `View Room Selected Mobile` });
  }, [ctaClickEvent, emitInteractionEvent]);

  useEffect(() => {
    const popstateListener = () => {
      if (isMobile) {
        sessionStorage.set('isFromPropertyPage', true);
      }
    };

    window.addEventListener('popstate', popstateListener);
    return () => {
      window.removeEventListener('popstate', popstateListener);
    };
  }, [isMobile]);

  useEffect(() => {
    clearGclid();
  }, []);

  const removeGclidQueryParam = () => {
    const query = router.query;
    const pathname = router.pathname;
    delete query.gclid;
    router.replace(
      {
        query: query,
        pathname: pathname,
      },
      null,
      { shallow: true },
    );
  };
  const windowTop = typeof window !== 'undefined' ? window.innerHeight - 67 : '';
  const windowWidth = typeof window !== 'undefined' ? window.innerWidth : 1920;
  const smallTablet = windowWidth > 750 && windowWidth < 1020;

  const isMobileApp = useSelector(getIsMobileApp);
  const menuOffsetHeight = isMobileApp ? 0 : isMobile ? 40 : 58;

  const { dealsRegion, dealType } = useSelector(getQueryParams) || {};
  const isFromDeals = dealsRegion ? true : false;
  const property = useSelector(getProperty);
  const region = property?.regionFullName;
  updateRegion(region);
  const tripAdvisorRating = useSelector(getTripAdvisorRating);
  const { images, startIndex } = useSelector(getFullscreenGalleryContent);
  const exclusiveOffer = useSelector(getExclusiveOffer);
  const hasExclusiveOffers = useSelector(getIsExclusiveOfferAvailable);
  const valuePropositions = useSelector(getCvp);

  const hasPropertyFacilities = property?.propertyFacilities && property?.propertyFacilities?.length > 0;

  useTrackPropertyPageView();

  useMount(() => {
    dispatch(fetchProperty());
    dispatch(fetchPointsBurnTiers());

    if (gclid) {
      removeGclidQueryParam();
    }

    if (isExclusiveOffer) {
      let payload = {};
      if (previewToken) {
        const type = 'property';
        const { propertyid: propertyId } = router.query;
        payload = { type, previewToken, propertyId };
      }
      dispatch(fetchExclusiveOffer({ ...payload }));
    }
  });

  useUnmount(() => {
    dispatch(setIsPointsPay(false));
    dispatch(clearProperty());
    dispatch(clearExclusiveOffer());
    dispatch(clearPointsLevels);
    dispatch(clearPointsBurnTiers);
  });

  useEffect(() => {
    dispatch(fetchProperty());
    dispatch(fetchPointsBurnTiers());
    dispatch(fetchPropertyExclusiveOffersPage());
    dispatch(fetchExclusiveOfferList());
    dispatch(fetchCampaign());
  }, [router.pathname, dispatch]);

  useEffect(() => {
    if (!isEmpty(images)) {
      openModal();
    }
  }, [openModal, images]);

  const [menuItems, setMenuItems] = useState({
    PHOTOS: {
      name: 'photos',
      text: 'Photos',
      id: 'photos',
      justifyContent: 'flex-start',
      ref: useRef(),
      linkRef: useRef(),
    },
    ROOMS: {
      name: 'rooms',
      text: 'Rooms',
      id: 'rooms',
      justifyContent: 'flex-start',
      ref: useRef(),
      linkRef: useRef(),
    },
    HIGHLIGHTS: {
      name: 'highlights',
      text: 'Highlights',
      id: 'highlights',
      justifyContent: 'flex-start',
      ref: useRef(),
      linkRef: useRef(),
      isExclusive: true,
      isExpanded: true,
    },
    OFFER_TERMS: {
      name: 'offer-terms',
      text: 'Offer Terms',
      id: 'offer-terms',
      justifyContent: 'center',
      ref: useRef(),
      linkRef: useRef(),
      isExclusive: true,
      isExpanded: false,
    },
    ABOUT_THIS_PROPERTY: {
      name: 'about-this-property',
      text: 'About this property',
      id: 'about-property',
      justifyContent: 'center',
      ref: useRef(),
      linkRef: useRef(),
      isExpanded: !isExclusiveOffer,
    },
    LOCATION: {
      name: 'location',
      text: 'Location',
      id: 'location',
      justifyContent: 'flex-end',
      ref: useRef(),
      linkRef: useRef(),
      isExpanded: false,
    },
    PROPERTY_POLICIES: {
      name: 'property-policies',
      text: 'Property policies',
      id: 'property-policies',
      justifyContent: 'flex-end',
      ref: useRef(),
      linkRef: useRef(),
      isExpanded: false,
    },
  });

  const handleMenuChanged = (val) => {
    const key = snakeCase(val.text).toUpperCase();
    const item = menuItems[key];

    setMenuItems({
      ...menuItems,
      [key]: {
        ...item,
        isExpanded: true,
      },
    });
  };

  const handleDisclosureToggle = (isExpanded, title) => {
    const key = snakeCase(title).toUpperCase();
    const item = menuItems[key];

    setMenuItems({
      ...menuItems,
      [key]: {
        ...item,
        isExpanded,
      },
    });
  };

  const handleGalleryOpened = () => {
    emitInteractionEvent({ type: 'Property Summary Gallery', value: 'Gallery Opened' });
  };

  const handleGalleryImageChanged = (id) => {
    emitInteractionEvent({ type: 'Property Summary Gallery', value: `Gallery Scrolled to ${id}` });
  };

  const { standardViewItemEvent } = usePropertyPageStandardGa4Event();
  const { exclusiveViewItemEvent } = usePropertyPageExclusiveGa4Event();

  useEffect(() => {
    isExclusiveOffer ? exclusiveViewItemEvent() : standardViewItemEvent();
  }, [exclusiveViewItemEvent, isExclusiveOffer, standardViewItemEvent]);

  if (!property) return null;

  return (
    <Box position="relative" bg="white">
      <RequestCallbackModal interactionType="Exclusive Offers" />
      <ExclusiveOffersFetcher />
      <Waypoints />
      <PropertyHelmet property={property} />
      {isMobileApp && <UpdateNavigationIconBack />}

      <PageBlock bg="white" display={['none', 'flex', 'none']}>
        <DynamicMessageBox />
      </PageBlock>
      <PageBlock bg="white" gutter={0} display={['none', 'block', 'none']}>
        <WelcomeMessage />
      </PageBlock>

      <CampaignMessaging />

      <MobileAppBoundary>
        <PageBlock
          bg="white"
          pt={4}
          pb={isExclusiveOffer ? 0 : 4}
          borderBottom={isExclusiveOffer ? 0 : 1}
          borderColor="greys.porcelain"
          printable={false}
        >
          {!isFromDeals && <BackToSearchLink />}
          {isFromDeals && <BackToDealsLink dealsRegion={dealsRegion} dealType={dealType} />}
        </PageBlock>
      </MobileAppBoundary>

      <Hide xs>
        <PageBlock bg="white" gutter={[0, 3]}>
          <PropertyHeader property={property} tripAdvisorRating={tripAdvisorRating} />
        </PageBlock>
      </Hide>

      {isExclusiveOffer && (
        <NavMenu
          data-testid="nav-menu"
          menuItems={Object.values(menuItems)}
          menuRefPositionOffset={menuOffsetHeight}
          onMenuChanged={handleMenuChanged}
          mb={!hasValidQuery && isMobile ? -2 : 0}
          display={['block', 'block', 'none', 'none']}
        />
      )}

      <Box position="relative" ref={menuItems.PHOTOS.ref}>
        <div id="photos"></div>
        <StandardImageGallery images={property.images} onClick={handleGalleryOpened} onImageChanged={handleGalleryImageChanged} />
        {isExclusiveOffer && (
          <Hide xs sm>
            <ClickThroughWrapper position="absolute" top="0" left="0" right="0">
              <PageBlock gutter={[0, 3]}>
                <OfferDetails />
              </PageBlock>
            </ClickThroughWrapper>
          </Hide>
        )}
      </Box>

      {!isExclusiveOffer && (
        <NavMenu
          data-testid="nav-menu"
          menuItems={Object.values(menuItems)}
          menuRefPositionOffset={menuOffsetHeight}
          onMenuChanged={handleMenuChanged}
          mb={!hasValidQuery && isMobile ? -2 : 0}
        />
      )}

      {isExclusiveOffer && (
        <NavMenu
          data-testid="nav-menu"
          menuItems={Object.values(menuItems)}
          menuRefPositionOffset={menuOffsetHeight}
          onMenuChanged={handleMenuChanged}
          mb={!hasValidQuery && isMobile ? -2 : 0}
          display={['none', 'none', 'block', 'block']}
        />
      )}
      <Box overflow={isMobile ? 'clip' : 'visible'}>
        {hasValidQuery && isMobile && !isExclusiveOffer && (
          <Box zIndex={'stickyPaymentOptions'} position="sticky" top={windowTop} width="100%" boxShadow="button" height={0}>
            <ButtonBox>
              <ButtonLink as={Link} variant="primary" href="#view-rooms" aria-label={`View rooms`} onClick={handleViewRoomOnClick}>
                View Rooms
              </ButtonLink>
            </ButtonBox>
          </Box>
        )}

        <Hide sm md lg>
          <PageBlock bg="white" gutter={[0, 3]} pl={[4, 0, 0]}>
            <PropertyHeader property={property} tripAdvisorRating={tripAdvisorRating} />
          </PageBlock>
        </Hide>

        {isExclusiveOffer && (
          <Hide md lg>
            <PageBlock gutter={[0, 3]}>
              <OfferDetails />
            </PageBlock>
          </Hide>
        )}
        {isExclusiveOffer && (
          <Box
            pl={[0, 180, 0]}
            pb={[0, 4, 0]}
            borderTop={[1, 1, 0]}
            borderBottom={[1, 1, 0]}
            borderColor={'greys.alto'}
            color="#D9D9D9"
            mt={[6, 10, 0]}
            mb={[2, 8, 0]}
          >
            <Hide md lg xl>
              <ExclusiveContactDetails secondTabletSpot={true} />
            </Hide>
          </Box>
        )}
        <PageBlock bg="white" gutter={[0, 3]} mt={[(3, 0, 0)]}>
          <Flex flex="1 1 auto" flexDirection={['column', 'column', 'row']}>
            <PropertySummaryFacilities property={property} hasPropertyFacilities={hasPropertyFacilities} />
            <PropertySummaryMap property={property} />
          </Flex>
          {property.renovations && property.renovations !== 'undefined' && !isMobile && (
            <Box pb={[0, 0, 4, 4]}>
              <Renovations renovations={property.renovations} />
            </Box>
          )}
        </PageBlock>

        {isExclusiveOffer && valuePropositions && (
          <PageBlock
            bg="white"
            gutter={[0, 3]}
            mt={[3, 3, 0]}
            pt={[0, 3, 6, 6]}
            pb={[0, 0, 6, 6]}
            borderTop={1}
            borderColor="greys.alto"
            data-testid="value-propositions"
            pl={[4, 0, 0]}
          >
            <ValuePropositionsList title="" layout="slimline" valuePropositions={valuePropositions} />
            <Box borderBottom={[1, 0]} borderColor="greys.alto" />
          </PageBlock>
        )}

        <PageBlock pt={[0, 5]} bg="white" pb={[0, 2]} display={['flex', 'none']}>
          <DynamicMessageBox />
        </PageBlock>
        <PageBlock bg="white" gutter={0} display={['block', 'none']}>
          <WelcomeMessage />
        </PageBlock>

        <PageBlock printable={false} id="check-availability" backgroundColor={'greys.porcelain'}>
          <Heading.h2
            fontSize={['md', 'lg']}
            fontWeight={400}
            pl={[4, 6, 0]}
            pt={isExclusiveOffer ? [6, 6, 15] : [6, 6, 8]}
            pb={isExclusiveOffer ? [0, 1, 2] : [0, 1, 0]}
            data-testid="check-availability-text"
          >
            Check availability
          </Heading.h2>

          <div id="main-content"></div>
          <SearchControls smallTablet={smallTablet} />
        </PageBlock>
      </Box>
      <Box ref={menuItems.ROOMS.ref} backgroundColor="greys.porcelain" />
      <PageBlock
        py={smallTablet ? 0 : [2, 0, 0]}
        overflowY={['hidden', 'inherit']}
        ref={menuItems.ROOMS.ref}
        backgroundColor="greys.porcelain"
      >
        <PropertyAvailabilitySummary propertyName={property.name} smallTablet={smallTablet} />
        {isExclusiveOffer && <StandardOfferMessage propertyId={property.id} />}
        <Box px={[3, 0, 0]} pb={4}>
          {EXCLUSIVE_OFFERS_ENABLED && hasExclusiveOffers && !isExclusiveOffer && <ExclusiveOfferCrossSellBanner />}
        </Box>
      </PageBlock>

      {!isMobile && (
        <PageBlock py={smallTablet ? 0 : [0, 2]} overflowY={['hidden', 'inherit']} backgroundColor={'greys.porcelain'}>
          <PropertyAvailability px={0} />
        </PageBlock>
      )}
      {isMobile && <PropertyAvailability px={0} />}

      {exclusiveOffer?.highlights?.description && (
        <DisclosureSection
          id="highlights"
          title="Highlights"
          ref={menuItems.HIGHLIGHTS.ref}
          expand={menuItems.HIGHLIGHTS.isExpanded}
          onToggle={handleDisclosureToggle}
        >
          {exclusiveOffer.highlights?.title && (
            <Heading.h4 fontSize="md" fontWeight="normal">
              {exclusiveOffer.highlights?.title}
            </Heading.h4>
          )}
          <Markdown content={exclusiveOffer.highlights.description} disableParsingRawHTML={false} />
        </DisclosureSection>
      )}

      {exclusiveOffer?.terms?.hotelTerms && (
        <DisclosureSection
          id="offer-terms"
          title="Offer Terms"
          ref={menuItems.OFFER_TERMS.ref}
          expand={menuItems.OFFER_TERMS.isExpanded}
          onToggle={handleDisclosureToggle}
          variant="large"
        >
          <Markdown content={exclusiveOffer.terms.hotelTerms} disableParsingRawHTML={false} />
        </DisclosureSection>
      )}

      <DisclosureSection
        id="about-property"
        title="About this property"
        ref={menuItems.ABOUT_THIS_PROPERTY.ref}
        expand={menuItems.ABOUT_THIS_PROPERTY.isExpanded}
        onToggle={handleDisclosureToggle}
      >
        <PropertyDescription property={property} />
      </DisclosureSection>

      <DisclosureSection id="facilities" title="Facilities" ref={React.createRef()} variant="large" expand={false}>
        <PropertyFacilities facilities={property.propertyFacilities} interactionEventValue="Bottom Link Selected" />
      </DisclosureSection>

      <DisclosureSection
        id="location"
        title="Location"
        ref={menuItems.LOCATION.ref}
        expand={menuItems.LOCATION.isExpanded}
        onToggle={handleDisclosureToggle}
      >
        <PropertyMap property={property} />
      </DisclosureSection>

      <DisclosureSection
        id="property-policies"
        title="Property policies"
        ref={menuItems.PROPERTY_POLICIES.ref}
        expand={menuItems.PROPERTY_POLICIES.isExpanded}
        onToggle={handleDisclosureToggle}
      >
        <PropertyKnowBeforeYouGo
          policyDescription={property.policyDescription}
          checkInInstructions={property.checkInInstructions}
          knowBeforeYouGoDescription={property.knowBeforeYouGoDescription}
          mandatoryFeesDescription={property.mandatoryFeesDescription}
        />
      </DisclosureSection>

      <PageBlock bg="snow">
        <RecommendedProperties />
      </PageBlock>
      <FullscreenImageGallery {...modalProps} images={images} startIndex={startIndex} zIndex={9001} />
      <ExclusiveOfferUnavailableDialog />
    </Box>
  );
};

PropertyLayout.propTypes = {
  isExclusiveOffer: PropTypes.bool,
  previewToken: PropTypes.string,
  menuItems: PropTypes.arrayOf(
    PropTypes.shape({
      name: PropTypes.string.isRequired,
      linkId: PropTypes.string,
      ref: PropTypes.oneOfType([
        PropTypes.func,
        PropTypes.shape({
          current: PropTypes.oneOfType([PropTypes.object, PropTypes.element]),
        }),
      ]).isRequired,
      text: PropTypes.string.isRequired,
    }),
  ),
};

export default PropertyLayout;
