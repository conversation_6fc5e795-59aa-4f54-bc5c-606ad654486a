import React from 'react';
import { Box, Flex, Hide, Heading } from '@qga/roo-ui/components';
import { Flag } from '../../../primitives';
import OfferInclusions from '../../../../../../../../../OfferInclusions/OfferInclusions';
import { useBreakpoints } from 'hooks/useBreakpoints';
import type { Offer } from 'types/property';
import { INCLUSIONS_ICONS_PREVIEW } from 'config/constants';

interface OfferDescriptionProps {
  offerName: Offer['name'];
  inclusions: Offer['inclusions'];
  valueAdds: Offer['valueAdds'];
  isExpanded?: boolean;
  onClick: () => void;
}

export const OfferDescription = ({ offerName, inclusions, valueAdds, onClick }: OfferDescriptionProps) => {
  const { isLessThanBreakpoint } = useBreakpoints();
  const isMobile = isLessThanBreakpoint(0);

  const getValueAddsAndInclusions = () => {
    if (isMobile && valueAdds?.length > 0 && inclusions) {
      return [
        {
          icon: 'welcomeGift',
          description: 'value add',
          name: 'value adds',
          code: 'welcome_gift',
        },
        ...inclusions,
      ];
    }
    return inclusions;
  };

  const mergedInclusions = getValueAddsAndInclusions();

  const previewIconsMobile = mergedInclusions?.slice(0, INCLUSIONS_ICONS_PREVIEW);

  return (
    <Flex mb={[4, 4]} data-testid="extra-flex-inclusions" flexDirection="column" width={['100%', '50%', '50%']} justifyContent="flex-start">
      <Flex justifyContent={isMobile ? 'space-between' : 'flex-start'}>
        <Heading.h3 mb={[0, 0]} fontSize="md" lineHeight="normal" color="greys.charcoal">
          {offerName}
        </Heading.h3>
      </Flex>
      <Box color="greys.charcoal">
        <Flex flexDirection="column" data-testid="inclusions-preview-wrapper-desktop">
          <Hide xs>
            <OfferInclusions inclusions={inclusions} valueAdds={valueAdds} onClick={onClick} isToggleExpansion={true} />
          </Hide>
        </Flex>
        <Flex flexDirection="row" data-testid="inclusions-preview-wrapper-mobile">
          {isMobile &&
            previewIconsMobile?.map(({ code, icon }) => (
              <Box pb={2} key={code}>
                <Flag icon={icon} color="greys.charcoal" />
              </Box>
            ))}
        </Flex>
      </Box>
    </Flex>
  );
};
