import React from 'react';
import PropTypes from 'prop-types';
import styled from '@emotion/styled';
import { themeGet } from 'styled-system';
import { mediaQuery } from 'lib/styledSystem';
import { Flex, Box, Icon, NakedButton, Hide } from '@qga/roo-ui/components';

export const List = styled.ul`
  margin: 0;
  padding: 0;
  margin-top: -${themeGet('space.3')};

  ${mediaQuery.minWidth.sm} {
    display: flex;
    flex-wrap: wrap;
  }
`;

export const ListItem = styled.li`
  list-style-type: none;
  padding: ${themeGet('space.3')} 0;
  margin: 0;
  border-bottom: 1px solid ${themeGet('colors.greys.alto')};

  ${mediaQuery.minWidth.sm} {
    flex-basis: 50%;
  }
`;
export const Flag = ({ icon, children, color }) => (
  <Flex data-testid="flag" pt={2} alignItems={'baseline'}>
    <Box flex="0 1 32px">{icon && <Icon size="20px" name={icon} mr="2" color={color} />}</Box>
    {children}
  </Flex>
);
Flag.propTypes = {
  icon: PropTypes.string,
  children: PropTypes.node,
  color: PropTypes.string,
};

Flag.defaultProps = {
  icon: '',
  children: undefined,
};

export const OfferWrapper = styled(Flex)`
  position: relative;
  background-color: ${themeGet('colors.white')};
  border-radius: ${themeGet('radii.default')};
  border: ${themeGet('borders.1')} ${themeGet('colors.greys.alto')};
  padding: ${themeGet('space.4')};

  ${mediaQuery.minWidth.sm} {
    margin: 0;
    margin-bottom: ${themeGet('space.6')};
  }
`;

export const OfferWrapperMobile = styled(Flex)`
  justify-content: space-between;
  position: relative;
  background-color: ${themeGet('colors.white')};
  flex-direction: column;
  border-bottom: ${(props) => (props.isLastOffer ? 0 : `${themeGet('borders.1')(props)} ${themeGet('colors.greys.dusty')(props)}`)};
  border: ${themeGet('borders.1')} ${themeGet('colors.greys.alto')};
  border-radius: ${themeGet('radii.default')};
  padding: ${themeGet('space.4')};

  ${mediaQuery.minWidth.sm} {
    flex-direction: row;
    border-radius: ${themeGet('radii.default')};
    border: ${themeGet('borders.1')} ${themeGet('colors.greys.alto')};
  }
`;

export const CollapseWrapper = styled(Hide)`
  flex-grow: 1;
  text-align: right;
`;

export const FlexCollapseWrapper = styled(CollapseWrapper)`
  justify-content: flex-end;
`;

export const PriceBoxWrapper = styled(Flex)`
  flex-direction: column;
  padding-right: 0;

  ${mediaQuery.minWidth.sm} {
    padding-right: ${themeGet('space.2')};
  }
`;

export const FullWidthButton = styled(NakedButton)`
  width: 100%;
  display: flex;
  justify-content: flex-end;
`;

export const TooltipWrapper = styled(Box)`
  box-shadow: ${themeGet('shadows.tooltip')};
  color: ${themeGet('colors.greys.steel')};
  background-color: ${themeGet('colors.white')};
  padding: ${themeGet('space.6')} ${themeGet('space.8')};
  border-radius: ${themeGet('radii.default')};
  width: ${themeGet('uiStructure.tooltipWidth')};
  text-align: left;
`;
