import React, { useCallback } from 'react';
import { Flex, Box, Icon, NakedButton } from '@qga/roo-ui/components';
import { useSelector } from 'react-redux';
import get from 'lodash/get';
import OfferPointsEarn from './OfferPointsEarn';
import OfferPriceBox from './OfferPriceBox';
import OfferDetails from './OfferDetails';
import usePointsEarned from 'lib/hooks/usePointsEarned/usePointsEarned';
import { OfferWrapper } from './primitives';
import { useDataLayer } from 'hooks/useDataLayer';
import { getIsPointsPay } from 'store/ui/uiSelectors';
import PromotionalSashNew from 'components/PromotionalSashNew';
import PromotionalSashRedVariant from 'components/PromotionalSashRedVariant';
import { VALUE_ADD_SASH } from 'config/constants';
import { useShowRedSashToggle } from 'hooks/useShowRedSashToggle';
import type { Offer } from 'types/property';
import { AvaPointsEarn } from 'types/ava';

const TypedOfferPointsEarn = OfferPointsEarn as React.FC<{
  pointsEarned: AvaPointsEarn;
  isClassic: boolean;
  isCurrencyCash: boolean;
  isPointsPlusPay: boolean;
}>;

interface ExpandedOfferProps {
  roomTypeId: string | number;
  offer: Offer;
  roomName: string;
  toggleOfferExpanded: (expanded: boolean) => void;
  isLastOffer: boolean;
}

const ExpandedOffer: React.FC<ExpandedOfferProps> = ({ roomTypeId, offer, roomName, toggleOfferExpanded, isLastOffer }) => {
  const {
    allocationsAvailable,
    id: offerId,
    name: offerName,
    charges,
    cancellationPolicy,
    type,
    depositPay,
    pointsTierInstanceId: offerInstanceId,
    valueAdds,
    inclusions,
    description,
  } = offer;

  const { pointsEarned, onCashPaymentAmountChange } = usePointsEarned({ pointsEarned: offer.pointsEarned });

  const promotionName = get(offer, 'promotion.name');
  const isClassic = offer.type === 'classic';
  const isCurrencyCash = charges.total.currency !== 'PTS';
  const { emitInteractionEvent } = useDataLayer();
  const isPointsPlusPay = useSelector(getIsPointsPay);
  const sashPromotionName = promotionName ?? VALUE_ADD_SASH;
  const showPromotionalSash = promotionName || (valueAdds && valueAdds.length > 0);
  const { isReady, showRedSash } = useShowRedSashToggle({ promotionName: sashPromotionName });

  const onCollapseOffer = useCallback(() => {
    toggleOfferExpanded(false);
    emitInteractionEvent({ type: 'Room Offer Details', value: 'Offer Collapsed' });
  }, [toggleOfferExpanded, emitInteractionEvent]);

  return (
    <OfferWrapper data-testid="offer-card-expanded" isLastOffer={isLastOffer} mb={6} p={4} flexDirection="column" position="relative">
      {showPromotionalSash && (
        <Box position="absolute" left={0} top={0}>
          {isReady && showRedSash ? (
            <PromotionalSashRedVariant promotionName={sashPromotionName} type="corner" />
          ) : (
            <PromotionalSashNew promotionName={sashPromotionName} type="corner" />
          )}
        </Box>
      )}
      <Flex data-testid="sash-and-icon" justifyContent="flex-end">
        <NakedButton
          onClick={onCollapseOffer}
          aria-label="Collapse offer details"
          data-expanded-clickable-area-target
          p={0}
          data-testid="collapse-offer-summary"
        >
          <Icon name="expandLess" size={24} />
        </NakedButton>
      </Flex>
      <Flex flexDirection={['column', 'row', 'row']} data-testid="offer-card-body-expanded" alignItems={'flex-start'} width="100%">
        <Flex
          data-testid="extra-flex-expanded"
          justify-content="space-between"
          flexDirection="column"
          flexGrow={1}
          width={['100%', '50%', '50%']}
        >
          <OfferDetails description={description} name={offerName} valueAdds={valueAdds} inclusions={inclusions} roomName={roomName} />

          {!isClassic && offer.pointsEarned.qffPoints.total !== 0 && pointsEarned && (
            <TypedOfferPointsEarn
              pointsEarned={pointsEarned}
              isClassic={isClassic}
              isCurrencyCash={isCurrencyCash}
              isPointsPlusPay={isPointsPlusPay}
            />
          )}
        </Flex>
        <Flex data-testid="price-and-btn" pl={[0, 6, 0]} alignItems={['flex-start', 'flex-end', 'flex-end']} width={['100%', '50%', '50%']}>
          <OfferPriceBox
            allocationsAvailable={allocationsAvailable}
            charges={charges}
            offerType={type}
            offerName={offerName}
            offerId={offerId}
            roomTypeId={roomTypeId}
            roomName={roomName}
            onCashPaymentAmountChange={onCashPaymentAmountChange}
            offerInstanceId={offerInstanceId}
            cancellationPolicy={cancellationPolicy}
            depositPay={depositPay}
          />
        </Flex>
      </Flex>
    </OfferWrapper>
  );
};

export default ExpandedOffer;
