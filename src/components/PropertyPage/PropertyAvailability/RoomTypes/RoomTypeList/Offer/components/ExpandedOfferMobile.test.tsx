import React from 'react';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import ExpandedOfferMobile from './ExpandedOfferMobile';
import usePointsEarned from 'lib/hooks/usePointsEarned/usePointsEarned';
import { useDataLayer } from 'hooks/useDataLayer';
import { getIsPointsPay } from 'store/ui/uiSelectors';
import { getPropertyId } from 'store/property/propertySelectors';
import { VALUE_ADD_SASH } from 'config/constants';
import { renderWithProviders } from 'test-utils/reactUtils/renderWithProviders';
import { useBreakpoints } from 'hooks/useBreakpoints';
import { useShowRedSashToggle } from 'hooks/useShowRedSashToggle';
import { offerComplete as offer } from './offer.fixture';

jest.mock('hooks/useDataLayer');
jest.mock('hooks/useBreakpoints');
jest.mock('hooks/useShowRedSashToggle');
jest.mock('store/ui/uiSelectors');
jest.mock('store/property/propertySelectors');
jest.mock('lib/hooks/usePointsEarned/usePointsEarned');

let mockOfferDetailsProps;
let mockOfferPointsEarnProps;
let mockOfferPriceBoxProps;
let mockOfferCheckoutLinkProps;
let mockPromotionalSashProps;

jest.mock('./OfferDetails', () => {
  return function MockOfferDetails(props) {
    mockOfferDetailsProps = props;
    return <div data-testid="offer-details" />;
  };
});

jest.mock('components/PropertyPage/PropertyAvailability/RoomTypes/RoomTypeList/Offer/components/OfferPointsEarn', () => {
  return function MockOfferPointsEarn(props) {
    mockOfferPointsEarnProps = props;
    return <div data-testid="offer-points-earn" />;
  };
});

jest.mock('components/PropertyPage/PropertyAvailability/RoomTypes/RoomTypeList/Offer/components/OfferPriceBox', () => {
  return function MockOfferPriceBox(props) {
    mockOfferPriceBoxProps = props;
    return <div data-testid="offer-price-box" />;
  };
});

jest.mock('components/PropertyPage/PropertyAvailability/RoomTypes/RoomTypeList/Offer/components/OfferCheckoutLink', () => {
  return function MockOfferCheckoutLink(props) {
    mockOfferCheckoutLinkProps = props;
    return <div data-testid="offer-checkout-link" />;
  };
});

jest.mock('components/PromotionalSashNew', () => {
  return function MockPromotionalSashNew(props) {
    mockPromotionalSashProps = props;
    return <div data-testid="promotional-sash-new" />;
  };
});

jest.mock('components/PromotionalSashRedVariant', () => {
  return function MockPromotionalSashRedVariant(props) {
    mockPromotionalSashProps = props;
    return <div data-testid="promotional-sash-red-variant" />;
  };
});

const toggleOfferExpanded = jest.fn();
const emitInteractionEvent = jest.fn();
const onCashPaymentAmountChange = jest.fn();
const isPointsPay = false;
const propertyId = '1234';
const defaultProps = {
  roomTypeId: '2',
  offer: offer,
  roomName: 'Standard King',
  toggleOfferExpanded,
  isLastOffer: false,
};

const render = (props = {}) => renderWithProviders(<ExpandedOfferMobile {...defaultProps} {...props} />);

const expectOfferCheckoutLinkProps = () => {
  expect(mockOfferCheckoutLinkProps.propertyId).toBe(propertyId);
  expect(mockOfferCheckoutLinkProps.roomTypeId).toBe('2');
  expect(mockOfferCheckoutLinkProps.offerId).toBe(offer.id);
  expect(mockOfferCheckoutLinkProps.offerName).toBe(offer.name);
  expect(mockOfferCheckoutLinkProps.roomName).toBe('Standard King');
  expect(mockOfferCheckoutLinkProps.mb).toBe(0);
  expect(mockOfferCheckoutLinkProps.exclusiveOffer).toBe(false);
};

const expectPromotionalSashToRender = (expectedPromotionName) => {
  const promotionalSash = screen.getByTestId('promotional-sash-new') || screen.getByTestId('promotional-sash-red-variant');
  expect(promotionalSash).toBeInTheDocument();
  expect(mockPromotionalSashProps.promotionName).toBe(expectedPromotionName);
  expect(mockPromotionalSashProps.type).toBe('corner');
};

beforeEach(() => {
  jest.resetAllMocks();
  mockOfferDetailsProps = {};
  mockOfferPointsEarnProps = {};
  mockOfferPriceBoxProps = {};
  mockOfferCheckoutLinkProps = {};
  mockPromotionalSashProps = {};

  (useDataLayer as jest.Mock).mockReturnValue({ emitInteractionEvent });
  (getIsPointsPay as jest.Mock).mockReturnValue(isPointsPay);
  (getPropertyId as jest.Mock).mockReturnValue(propertyId);
  (usePointsEarned as jest.Mock).mockReturnValue({
    pointsEarned: offer.pointsEarned,
    onCashPaymentAmountChange,
  });

  (useBreakpoints as jest.Mock).mockReturnValue({
    isLessThanBreakpoint: jest.fn().mockReturnValue(true),
  });

  (useShowRedSashToggle as jest.Mock).mockReturnValue({
    isReady: true,
    showRedSash: false,
  });
});

describe('Component rendering and structure', () => {
  it('renders main components with correct accessibility', () => {
    render();

    expect(screen.getByTestId('offer-card-expanded-mobile')).toBeInTheDocument();
    expect(screen.getByTestId('offer-details')).toBeInTheDocument();
    expect(screen.getByTestId('offer-price-box')).toBeInTheDocument();

    const collapseButton = screen.getByTestId('collapse-offer-summary-mobile');
    expect(collapseButton).toHaveAttribute('aria-label', 'Collapse offer details');
  });
});

describe('OfferDetails', () => {
  it('passes the correct props', () => {
    render();

    const expectedProps = {
      description: offer.description,
      name: offer.name,
      roomName: defaultProps.roomName,
      inclusions: offer.inclusions,
      valueAdds: offer.valueAdds,
    };

    Object.entries(expectedProps).forEach(([key, value]) => {
      expect(mockOfferDetailsProps[key]).toEqual(value);
    });
  });
});

describe('OfferPriceBox', () => {
  it('passes all required props correctly', () => {
    render();
    expect(mockOfferPriceBoxProps).toEqual(
      expect.objectContaining({
        charges: offer.charges,
        offerType: offer.type,
        roomTypeId: defaultProps.roomTypeId,
        offerId: offer.id,
        onCashPaymentAmountChange,
        allocationsAvailable: offer.allocationsAvailable,
        offerName: offer.name,
        roomName: defaultProps.roomName,
        offerInstanceId: offer.pointsTierInstanceId,
        cancellationPolicy: offer.cancellationPolicy,
        depositPay: offer.depositPay,
      }),
    );
  });
});

describe('OfferCheckoutLink conditional rendering', () => {
  describe('when not Points Plus Pay', () => {
    beforeEach(() => {
      (getIsPointsPay as jest.Mock).mockReturnValue(false);
    });

    it('renders and passes correct props', () => {
      render();
      expect(screen.getByTestId('offer-checkout-link')).toBeInTheDocument();
      expectOfferCheckoutLinkProps();
    });
  });

  describe('when Points Plus Pay and classic offer', () => {
    beforeEach(() => {
      (getIsPointsPay as jest.Mock).mockReturnValue(true);
    });

    it('renders and passes correct props', () => {
      render({ offer: { ...offer, type: 'classic' } });
      expect(screen.getByTestId('offer-checkout-link')).toBeInTheDocument();
      expectOfferCheckoutLinkProps();
    });
  });

  describe('when Points Plus Pay and non-classic offer', () => {
    beforeEach(() => {
      (getIsPointsPay as jest.Mock).mockReturnValue(true);
    });

    it('does not render', () => {
      render();
      expect(screen.queryByTestId('offer-checkout-link')).not.toBeInTheDocument();
    });
  });
});

describe('PromotionalSash conditional rendering', () => {
  it('does not render when no promotion and no value adds', () => {
    render({ offer: { ...offer, promotion: {}, valueAdds: [] } });
    expect(screen.queryByTestId('promotional-sash-new')).not.toBeInTheDocument();
    expect(screen.queryByTestId('promotional-sash-red-variant')).not.toBeInTheDocument();
  });

  it('renders with promotion name when promotion exists', () => {
    render();
    expectPromotionalSashToRender(offer.promotion.name);
  });

  it('renders with VALUE_ADD_SASH when only value adds exist', () => {
    render({ offer: { ...offer, promotion: {} } });
    expectPromotionalSashToRender(VALUE_ADD_SASH);
  });

  describe('sash variants', () => {
    it('renders red variant when showRedSash is true and ready', () => {
      (useShowRedSashToggle as jest.Mock).mockReturnValue({
        isReady: true,
        showRedSash: true,
      });

      render();
      expect(screen.getByTestId('promotional-sash-red-variant')).toBeInTheDocument();
      expect(screen.queryByTestId('promotional-sash-new')).not.toBeInTheDocument();
    });

    it('renders new variant when showRedSash is false or not ready', () => {
      render();
      expect(screen.getByTestId('promotional-sash-new')).toBeInTheDocument();
      expect(screen.queryByTestId('promotional-sash-red-variant')).not.toBeInTheDocument();
    });
  });
});

describe('OfferPointsEarn conditional rendering and props', () => {
  it('renders with correct props for non-classic offers with points', () => {
    render();

    expect(screen.getByTestId('offer-points-earn')).toBeInTheDocument();
    expect(mockOfferPointsEarnProps).toEqual(
      expect.objectContaining({
        pointsEarned: offer.pointsEarned,
        isClassic: false,
        isCurrencyCash: true,
        isPointsPlusPay: false,
      }),
    );
  });

  it('does not render for classic offers', () => {
    render({ offer: { ...offer, type: 'classic' } });
    expect(screen.queryByTestId('offer-points-earn')).not.toBeInTheDocument();
  });

  it('does not render when qffPoints total is zero', () => {
    render({
      offer: {
        ...offer,
        pointsEarned: {
          qffPoints: { base: 0, total: 0 },
          qbrPoints: { total: 0 },
        },
      },
    });
    expect(screen.queryByTestId('offer-points-earn')).not.toBeInTheDocument();
  });

  it('handles different currency types correctly', () => {
    render({ offer: { ...offer, charges: { total: { amount: '50000', currency: 'PTS' } } } });
    expect(mockOfferPointsEarnProps.isCurrencyCash).toBe(false);
  });

  it('handles Points Plus Pay correctly', () => {
    (getIsPointsPay as jest.Mock).mockReturnValue(true);

    render({ offer: { ...offer, charges: { total: { amount: '50000', currency: 'PTS' } } } });
    expect(mockOfferPointsEarnProps.isPointsPlusPay).toBe(true);
  });
});

describe('Hook integrations', () => {
  it('calls hooks with correct parameters', () => {
    render();

    expect(usePointsEarned).toHaveBeenCalledWith({ pointsEarned: offer.pointsEarned });
    expect(useShowRedSashToggle).toHaveBeenCalledWith({ promotionName: offer.promotion.name });
  });

  it('calls useShowRedSashToggle with VALUE_ADD_SASH when no promotion but has value adds', () => {
    render({ offer: { ...offer, promotion: {} } });
    expect(useShowRedSashToggle).toHaveBeenCalledWith({ promotionName: VALUE_ADD_SASH });
  });
});

describe('User interactions', () => {
  it('handles collapse button click correctly', async () => {
    render();

    const collapseButton = screen.getByTestId('collapse-offer-summary-mobile');
    await userEvent.click(collapseButton);

    expect(toggleOfferExpanded).toHaveBeenCalledWith(false);
    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Room Offer Details',
      value: 'Offer Collapsed',
    });
  });
});

describe('Edge cases', () => {
  it('handles missing properties gracefully', () => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { promotion, allocationsAvailable, ...incompleteOffer } = offer;

    render({ offer: incompleteOffer });

    expect(screen.getByTestId('offer-details')).toBeInTheDocument();
    expect(screen.getByTestId('offer-price-box')).toBeInTheDocument();
    expect(mockOfferPriceBoxProps.allocationsAvailable).toBeUndefined();
  });
});
