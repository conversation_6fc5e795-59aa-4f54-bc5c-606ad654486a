import React, { useCallback } from 'react';
import { Flex, Box, Icon, NakedButton } from '@qga/roo-ui/components';
import { useSelector } from 'react-redux';
import get from 'lodash/get';
import OfferPointsEarn from './OfferPointsEarn';
import OfferPriceBox from './OfferPriceBox';
import OfferDetails from './OfferDetails';
import usePointsEarned from 'lib/hooks/usePointsEarned/usePointsEarned';
import { CollapseWrapper, OfferWrapperMobile, PriceBoxWrapper } from './primitives';
import { useDataLayer } from 'hooks/useDataLayer';
import PromotionalSashNew from 'components/PromotionalSashNew';
import PromotionalSashRedVariant from 'components/PromotionalSashRedVariant';
import { getIsPointsPay } from 'store/ui/uiSelectors';
import { useBreakpoints } from 'hooks/useBreakpoints';
import OfferCheckoutLink from './OfferCheckoutLink';
import { getPropertyId } from 'store/property/propertySelectors';
import { useShowRedSashToggle } from 'hooks/useShowRedSashToggle';
import { VALUE_ADD_SASH } from 'config/constants';
import { Offer } from 'types/property';
import type { AvaPointsEarn } from 'types/ava';

const TypedOfferPointsEarn = OfferPointsEarn as React.FC<{
  pointsEarned: AvaPointsEarn | null;
  isClassic: boolean;
  isCurrencyCash: boolean;
  isPointsPlusPay: boolean;
  isMobile?: boolean;
}>;

interface ExpandedOfferMobileProps {
  roomTypeId: string | number;
  offer: Offer;
  roomName: string;
  toggleOfferExpanded: (expanded: boolean) => void;
  isLastOffer: boolean;
}

const ExpandedOfferMobile: React.FC<ExpandedOfferMobileProps> = ({ roomTypeId, offer, roomName, toggleOfferExpanded, isLastOffer }) => {
  const {
    allocationsAvailable,
    id: offerId,
    name: offerName,
    charges,
    cancellationPolicy,
    type,
    depositPay,
    pointsTierInstanceId: offerInstanceId,
    valueAdds = [],
    description,
    inclusions = [],
  } = offer;

  const { pointsEarned, onCashPaymentAmountChange } = usePointsEarned({ pointsEarned: offer.pointsEarned });
  const promotionName = get(offer, 'promotion.name');
  const hasPromo = !!promotionName;
  const isClassic = offer.type === 'classic';
  const isCurrencyCash = charges.total.currency !== 'PTS';
  const { emitInteractionEvent } = useDataLayer();
  const isPointsPlusPay = useSelector(getIsPointsPay);
  const { isLessThanBreakpoint } = useBreakpoints();
  const isMobile = isLessThanBreakpoint(0);
  const propertyId = useSelector(getPropertyId);

  const windowWidth = typeof window !== 'undefined' ? window.innerWidth : 1920;
  const smallTablet = windowWidth > 780 && windowWidth < 1020;
  const showOfferCheckoutLink = !isPointsPlusPay || (isPointsPlusPay && isClassic);
  const sashPromotionName = promotionName ?? VALUE_ADD_SASH;
  const showPromotionalSash = promotionName || valueAdds?.length > 0;
  const { isReady, showRedSash } = useShowRedSashToggle({ promotionName: sashPromotionName });

  const onCollapseOffer = useCallback(() => {
    toggleOfferExpanded(false);
    emitInteractionEvent({ type: 'Room Offer Details', value: 'Offer Collapsed' });
  }, [toggleOfferExpanded, emitInteractionEvent]);

  return (
    <Box width="100%">
      <OfferWrapperMobile
        data-testid="offer-card-expanded-mobile"
        isLastOffer={isLastOffer}
        mb={!isLastOffer ? 4 : 0}
        p={!isMobile ? 8 : 4}
      >
        <CollapseWrapper>
          <NakedButton
            onClick={onCollapseOffer}
            aria-label="Collapse offer details"
            data-testid="collapse-offer-summary-mobile"
            data-expanded-clickable-area-target
          >
            <Icon name="expandLess" size={24} />
          </NakedButton>
        </CollapseWrapper>
        <Flex flexDirection={'column'} width="100%">
          <Flex mr={4} data-testid="extra-flex-expanded-mobile" justify-content="space-between" flexDirection="column">
            <OfferDetails description={description} name={offerName} valueAdds={valueAdds} inclusions={inclusions} roomName={roomName} />
          </Flex>
          {!isClassic && offer.pointsEarned.qffPoints.total !== 0 && (
            <TypedOfferPointsEarn
              pointsEarned={pointsEarned}
              isClassic={isClassic}
              isCurrencyCash={isCurrencyCash}
              isPointsPlusPay={isPointsPlusPay}
            />
          )}
          <PriceBoxWrapper mt={isMobile ? 0 : hasPromo ? 4 : '35px'} smallTablet={smallTablet}>
            {showPromotionalSash && (
              <Box position="absolute" left={0} top="0px">
                {isReady && showRedSash ? (
                  <PromotionalSashRedVariant promotionName={sashPromotionName} type="corner" />
                ) : (
                  <PromotionalSashNew promotionName={sashPromotionName} type="corner" />
                )}
              </Box>
            )}
            <OfferPriceBox
              allocationsAvailable={allocationsAvailable}
              charges={charges}
              offerType={type}
              offerName={offerName}
              offerId={offerId}
              roomTypeId={roomTypeId}
              roomName={roomName}
              onCashPaymentAmountChange={onCashPaymentAmountChange}
              offerInstanceId={offerInstanceId}
              cancellationPolicy={cancellationPolicy}
              depositPay={depositPay}
            />
          </PriceBoxWrapper>
        </Flex>

        {showOfferCheckoutLink && (
          <OfferCheckoutLink
            propertyId={propertyId}
            roomTypeId={roomTypeId}
            offerId={offerId}
            offerName={offerName}
            roomName={roomName}
            mb={0}
            data-testid="select-button"
            exclusiveOffer={false}
          >
            Select
          </OfferCheckoutLink>
        )}
      </OfferWrapperMobile>
    </Box>
  );
};

export default ExpandedOfferMobile;
