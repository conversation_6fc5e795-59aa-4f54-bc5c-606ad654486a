export const offerComplete = {
  name: 'A great offer',
  description: 'Standard Twin Room',
  inclusions: [
    { code: 'internet', name: 'internet', description: 'Free wifi', icon: 'incQfWifi' },
    { code: 'dinner', name: 'dinner', description: 'One free meal', icon: 'incQfMeal' },
  ],
  valueAdds: ['Free breakfast buffet for 2 for day'],
  type: 'standard',
  id: 'offer-id',
  cancellationPolicy: {
    isNonrefundable: false,
    description: '',
    cancellationWindows: [],
  },
  charges: {
    total: { amount: '0', currency: 'AUD' },
    totalBeforeDiscount: { amount: '0', currency: 'AUD' },
    totalDiscount: { amount: 0, currency: 'AUD' },
    totalCash: { amount: '0', currency: 'AUD' },
    payableAtBooking: { total: { amount: '0', currency: 'AUD' } },
    payableAtProperty: { total: { amount: '0', currency: 'AUD' } },
    strikethrough: {
      price: { currency: null, amount: null },
      discount: { currency: null, amount: null },
      percentage: null,
    },
  },
  pointsEarned: {
    maxQffEarnPpd: 0,
    maxQbrEarnPpd: 0,
    promotionMultiplier: 1,
    propertyPpd: 0,
    qffPoints: {
      qffPointsClub: 0,
      total: 891,
      bonus: 0,
      base: 891,
    },
    qbrPoints: {
      total: 0,
    },
  },
  depositPay: { depositPayable: false },
  pointsTierInstanceId: 'mock-id',
  promotion: {
    name: 'Best deals',
  },
  allocationsAvailable: 5,
};

export const offerWithoutValueAdds = {
  ...offerComplete,
  valueAdds: [],
};
