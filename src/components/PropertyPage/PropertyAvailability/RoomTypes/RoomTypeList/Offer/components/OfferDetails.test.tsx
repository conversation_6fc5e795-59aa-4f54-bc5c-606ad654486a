/* eslint-disable @typescript-eslint/no-unused-vars */
import React from 'react';
import { screen, within } from '@testing-library/react';
import OfferDetails from './OfferDetails';
import { renderWithProviders } from 'test-utils/reactUtils/renderWithProviders';
import { offerComplete as offer } from './offer.fixture';
const filterStylingProps = (props) => {
  const {
    display,
    flexDirection,
    alignItems,
    justifyContent,
    flexWrap,
    flexBasis,
    flexGrow,
    flexShrink,
    pt,
    pb,
    pl,
    pr,
    mt,
    mb,
    ml,
    mr,
    p,
    m,
    px,
    py,
    mx,
    my,
    height,
    width,
    minHeight,
    minWidth,
    maxHeight,
    maxWidth,
    color,
    bg,
    backgroundColor,
    fontSize,
    fontWeight,
    lineHeight,
    textDecoration,
    border,
    borderRadius,
    borderColor,
    borderWidth,
    xs,
    sm,
    md,
    lg,
    xl,
    size,
    position,
    top,
    left,
    right,
    bottom,
    zIndex,
    overflow,
    boxShadow,
    opacity,
    ...domProps
  } = props;

  return domProps;
};

const createMockComponent = (testId, renderContent) => (props) => <div data-testid={testId}>{renderContent(props)}</div>;

jest.mock('@qga/roo-ui/components', () => ({
  Flex: ({ children, ...props }) => (
    <div data-testid="flex" {...filterStylingProps(props)}>
      {children}
    </div>
  ),
  Box: ({ children, ...props }) => (
    <div data-testid="box" {...filterStylingProps(props)}>
      {children}
    </div>
  ),
  Heading: {
    h3: ({ children, ...props }) => (
      <h3 data-testid="heading-h3" {...filterStylingProps(props)}>
        {children}
      </h3>
    ),
  },
  Text: ({ children, ...props }) => (
    <span data-testid="text" {...filterStylingProps(props)}>
      {children}
    </span>
  ),
  Hide: ({ children, ...props }) => (
    <div data-testid="hide" {...filterStylingProps(props)}>
      {children}
    </div>
  ),
  Icon: ({ name, ...props }) => <span data-testid={`icon-${name}`} {...filterStylingProps(props)} />,
  NakedButton: ({ children, ...props }) => (
    <button data-testid="naked-button" {...filterStylingProps(props)}>
      {children}
    </button>
  ),
}));

jest.mock('components/OfferInclusions', () =>
  createMockComponent('offer-inclusions', ({ inclusions, isToggleExpansion, viewAll }) => (
    <div>
      <div>
        Inclusions:
        {inclusions?.map((item, index) => (
          <div key={index}>{item.name}</div>
        ))}
      </div>
      <div>isToggleExpansion: {isToggleExpansion ? 'true' : 'false'}</div>
      <div>viewAll: {viewAll ? 'true' : 'false'}</div>
    </div>
  )),
);

jest.mock('components/OfferDetailsModal', () =>
  createMockComponent('offer-details-modal', ({ offerName, offerDescription, inclusions, roomName }) => (
    <>
      <div>
        Inclusions:
        {inclusions?.map((item, index) => (
          <div key={index}>{item.name}</div>
        ))}
      </div>
      <div>Modal Name: {offerName}</div>
      <div>Modal Description: {offerDescription}</div>
      <div>Room Name: {roomName}</div>
    </>
  )),
);

const defaultProps = {
  counter: 1,
  roomName: offer.name,
  description: offer.description,
  name: offer.name,
  valueAdds: offer.valueAdds,
  inclusions: offer.inclusions,
};

const render = (props = {}) => renderWithProviders(<OfferDetails {...defaultProps} {...props} />);

describe('The Offer details', () => {
  it('displays the offer name', () => {
    render();
    expect(screen.getByText(defaultProps.name)).toBeInTheDocument();
  });

  describe('OfferInclusions', () => {
    it('renders to the component', () => {
      render();
      expect(screen.getByTestId('offer-inclusions')).toBeInTheDocument();
    });

    it('sends the inclusions to the component', () => {
      render();
      defaultProps.inclusions.forEach((inclusion) => {
        const offerInclusionsComponent = screen.getByTestId('offer-inclusions');
        expect(within(offerInclusionsComponent).getByText(inclusion.name)).toBeInTheDocument();
      });
    });

    it('passes the isToggleExpansion prop to OfferInclusions component', () => {
      render();
      expect(screen.getByText('isToggleExpansion: true')).toBeInTheDocument();
    });

    it('passes the viewAll prop to OfferInclusions component', () => {
      render();
      expect(screen.getByText('viewAll: true')).toBeInTheDocument();
    });
  });

  describe('OfferDetailsModal', () => {
    it('displays the OfferDetailsModal component', () => {
      render();
      expect(screen.getByText(`Modal Name: ${defaultProps.name}`)).toBeInTheDocument();
    });
    it('sends the inclusions to the component', () => {
      render();
      defaultProps.inclusions.forEach((inclusion) => {
        const offerDetailsModalComponent = screen.getByTestId('offer-details-modal');
        expect(within(offerDetailsModalComponent).getByText(inclusion.name)).toBeInTheDocument();
      });
    });
    it('sends the offer description to the component', () => {
      render();
      expect(screen.getByText(`Modal Description: ${defaultProps.description}`)).toBeInTheDocument();
    });
    it('sends the offer name to the component', () => {
      render();
      expect(screen.getByText(`Modal Name: ${defaultProps.name}`)).toBeInTheDocument();
    });
    it('sends the room name to the component', () => {
      render();
      expect(screen.getByText(`Room Name: ${defaultProps.roomName}`)).toBeInTheDocument();
    });
  });
});
