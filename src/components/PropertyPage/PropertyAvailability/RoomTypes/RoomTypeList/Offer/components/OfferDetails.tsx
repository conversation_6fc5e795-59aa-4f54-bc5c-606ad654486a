/* eslint-disable react/prop-types */
import React, { memo } from 'react';
import { Flex, Heading } from '@qga/roo-ui/components';
import { Offer } from 'types/property';
import OfferInclusions from 'components/OfferInclusions';
import OfferDetailsModal from 'components/OfferDetailsModal';

interface OfferDetailsProps {
  roomName: string;
  description: Offer['description'];
  name: Offer['name'];
  valueAdds?: Offer['valueAdds'];
  inclusions?: Offer['inclusions'];
}

const OfferDetails: React.FC<OfferDetailsProps> = memo(({ description, name, valueAdds = [], inclusions = [], roomName }) => (
  <Flex flexDirection="column" data-testid="offer-details">
    <Heading.h3 display="block" mb={0} fontSize="md" lineHeight="normal">
      {name}
    </Heading.h3>
    <OfferInclusions inclusions={inclusions} valueAdds={valueAdds} isToggleExpansion={true} viewAll={true} />
    <OfferDetailsModal inclusions={inclusions} valueAdds={valueAdds} offerDescription={description} offerName={name} roomName={roomName} />
  </Flex>
));

export default OfferDetails;
