import React from 'react';
import { screen, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { mocked } from 'test-utils';
import { renderWithProviders } from 'test-utils/reactUtils/renderWithProviders';
import ExpandedOffer from './ExpandedOffer';
import usePointsEarned from 'lib/hooks/usePointsEarned/usePointsEarned';
import { useDataLayer } from 'hooks/useDataLayer';
import { VALUE_ADD_SASH } from 'config/constants';
import { useShowRedSashToggle } from 'hooks/useShowRedSashToggle';
import * as uiSelectors from 'store/ui/uiSelectors';
import { offerComplete as offer } from './offer.fixture';
import type { Inclusion } from 'types/property';

jest.mock('hooks/useDataLayer');
jest.mock('store/ui/uiSelectors');
jest.mock('lib/hooks/usePointsEarned/usePointsEarned');
jest.mock('hooks/useShowRedSashToggle');
jest.mock('./OfferPointsEarn', () => ({ pointsEarned, isClassic, isCurrencyCash, isPointsPlusPay }) => (
  <div
    data-testid="OfferPointsEarn"
    data-points-earned={JSON.stringify(pointsEarned)}
    data-is-classic={String(isClassic)}
    data-is-currency-cash={String(isCurrencyCash)}
    data-is-points-plus-pay={String(isPointsPlusPay)}
  />
));
jest.mock(
  './OfferPriceBox',
  () =>
    ({
      charges,
      offerType,
      roomTypeId,
      offerId,
      onCashPaymentAmountChange,
      allocationsAvailable,
      offerName,
      roomName,
      offerInstanceId,
      cancellationPolicy,
      depositPay,
    }) => (
      <div
        data-testid="OfferPriceBox"
        data-charges={JSON.stringify(charges)}
        data-offer-type={offerType}
        data-room-type-id={roomTypeId}
        data-offer-id={offerId}
        data-on-cash-payment-amount-change={onCashPaymentAmountChange ? 'function' : 'undefined'}
        data-allocations-available={allocationsAvailable}
        data-offer-name={offerName}
        data-room-name={roomName}
        data-offer-instance-id={offerInstanceId}
        data-cancellation-policy={JSON.stringify(cancellationPolicy)}
        data-deposit-pay={JSON.stringify(depositPay)}
      />
    ),
);

jest.mock('./OfferDetails', () => ({ description, name, valueAdds, inclusions, roomName }) => (
  <div data-testid="offer-details">
    <div>
      {inclusions?.map((item: Inclusion, index: number) => (
        <div key={index}>{item.name}</div>
      ))}
    </div>
    <div>description: {description}</div>
    <div>name: {name}</div>
    <div>valueAdds: {valueAdds}</div>
    <div>roomName: {roomName}</div>
  </div>
));
jest.mock('components/PromotionalSashNew', () => ({ promotionName, type }) => (
  <div data-testid="PromotionalSash" data-promotion-name={promotionName} data-type={type} />
));
jest.mock('components/PromotionalSashRedVariant', () => ({ promotionName, type }) => (
  <div data-testid="PromotionalSashRedVariant" data-promotion-name={promotionName} data-type={type} />
));

const toggleOfferExpanded = jest.fn();
const emitInteractionEvent = jest.fn();
const onCashPaymentAmountChange = jest.fn();
const defaultProps = {
  offer,
  propertyId: '1',
  roomTypeId: '2',
  roomName: 'Standard King',
  toggleOfferExpanded,
  isLastOffer: false,
};

const render = (props = {}) => renderWithProviders(<ExpandedOffer {...defaultProps} {...props} />);

const expectPromotionalSash = (promotionName, shouldExist = true) => {
  const sash = screen.queryByTestId('PromotionalSash');
  if (shouldExist) {
    expect(sash).toBeInTheDocument();
    expect(sash?.getAttribute('data-promotion-name')).toBe(promotionName);
    expect(sash?.getAttribute('data-type')).toBe('corner');
  } else {
    expect(sash).not.toBeInTheDocument();
  }
};

beforeEach(() => {
  jest.resetAllMocks();
  (useDataLayer as jest.Mock).mockReturnValue({ emitInteractionEvent });
  (usePointsEarned as jest.Mock).mockReturnValue({
    pointsEarned: offer.pointsEarned,
    onCashPaymentAmountChange,
  });
  mocked(useShowRedSashToggle).mockReturnValue({ showRedSash: false, isReady: true });
  mocked(uiSelectors.getIsPointsPay).mockReturnValue(false);
});

it('passes the correct props to <OfferDetails>', () => {
  render();
  const offerDetails = screen.getByTestId('offer-details');
  expect(within(offerDetails).getByText(`name: ${defaultProps.offer.name}`)).toBeInTheDocument();
  expect(within(offerDetails).getByText(`valueAdds: ${defaultProps.offer.valueAdds}`)).toBeInTheDocument();
  expect(within(offerDetails).getByText(`roomName: ${defaultProps.roomName}`)).toBeInTheDocument();
});

it('passes the correct details to <OfferPriceBox>', () => {
  render();
  const offerPriceBox = screen.getByTestId('OfferPriceBox');
  const chargesData = offerPriceBox.getAttribute('data-charges');
  expect(JSON.parse(chargesData ?? '{}')).toEqual(offer.charges);
  expect(offerPriceBox.getAttribute('data-offer-type')).toBe(offer.type);
  expect(offerPriceBox.getAttribute('data-room-type-id')).toBe(defaultProps.roomTypeId);
  expect(offerPriceBox.getAttribute('data-offer-id')).toBe(offer.id);
  expect(offerPriceBox.getAttribute('data-on-cash-payment-amount-change')).toBe('function');
  expect(offerPriceBox.getAttribute('data-allocations-available')).toBe(String(offer.allocationsAvailable));
  expect(offerPriceBox.getAttribute('data-offer-name')).toBe(offer.name);
  expect(offerPriceBox.getAttribute('data-room-name')).toBe(defaultProps.roomName);
  expect(offerPriceBox.getAttribute('data-offer-instance-id')).toBe(offer.pointsTierInstanceId);
  expect(JSON.parse(offerPriceBox.getAttribute('data-cancellation-policy') ?? '')).toEqual(offer.cancellationPolicy);
  expect(JSON.parse(offerPriceBox.getAttribute('data-deposit-pay') ?? '')).toEqual(offer.depositPay);
});

it('fires the toggleOfferExpanded prop when the CTA is actioned', async () => {
  render();
  const collapseButton = screen.getByTestId('collapse-offer-summary');
  await userEvent.click(collapseButton);
  expect(toggleOfferExpanded).toHaveBeenCalledWith(false);
});

it('renders with correct test ids and structure', () => {
  render();
  expect(screen.getByTestId('offer-card-expanded')).toBeInTheDocument();
  expect(screen.getByTestId('sash-and-icon')).toBeInTheDocument();
  expect(screen.getByTestId('offer-card-body-expanded')).toBeInTheDocument();
  expect(screen.getByTestId('extra-flex-expanded')).toBeInTheDocument();
  expect(screen.getByTestId('price-and-btn')).toBeInTheDocument();
});

it('renders collapse button with correct accessibility attributes', () => {
  render();
  const collapseButton = screen.getByTestId('collapse-offer-summary');
  expect(collapseButton).toHaveAttribute('aria-label', 'Collapse offer details');
});

describe('PromotionalSash', () => {
  it('does not show sash when there is no promotion and no valueAdds', () => {
    render({ offer: { ...offer, promotion: {}, valueAdds: [] } });
    expectPromotionalSash('', false);
  });

  it('shows promotion name when promotion exists', () => {
    render();
    expectPromotionalSash(offer.promotion.name);
  });

  it('shows promotion name when both promotion and valueAdds exist', () => {
    render();
    expectPromotionalSash(offer.promotion.name);
  });

  it('shows value deal sash when only valueAdds exist', () => {
    render({ offer: { ...offer, promotion: {} } });
    expectPromotionalSash(VALUE_ADD_SASH);
  });

  describe('red sash variant', () => {
    it('renders red sash when showRedSash and isReady are both true', () => {
      mocked(useShowRedSashToggle).mockReturnValue({ showRedSash: true, isReady: true });
      render();

      const redSash = screen.getByTestId('PromotionalSashRedVariant');
      expect(redSash).toBeInTheDocument();
      expect(redSash.getAttribute('data-promotion-name')).toBe(offer.promotion.name);
      expect(redSash.getAttribute('data-type')).toBe('corner');
      expect(screen.queryByTestId('PromotionalSash')).not.toBeInTheDocument();
    });

    it('renders normal sash when showRedSash is true but isReady is false', () => {
      mocked(useShowRedSashToggle).mockReturnValue({ showRedSash: true, isReady: false });
      render();
      expectPromotionalSash(offer.promotion.name);
      expect(screen.queryByTestId('PromotionalSashRedVariant')).not.toBeInTheDocument();
    });
  });

  it('handles edge cases with missing or empty promotion data', () => {
    const testCases = [
      { offer: { ...offer, valueAdds: [], promotion: {} }, description: 'empty valueAdds and promotion' },
      { offer: { ...offer, promotion: { id: '123' }, valueAdds: null }, description: 'promotion without name' },
    ];

    testCases.forEach(({ offer: testOffer }) => {
      render({ offer: testOffer });
      expectPromotionalSash('', false);
    });
  });
});

describe('OfferPointsEarn', () => {
  it('renders with correct props when offer is standard and has points', () => {
    render();
    const offerPointsEarn = screen.getByTestId('OfferPointsEarn');
    expect(JSON.parse(offerPointsEarn.getAttribute('data-points-earned') ?? '')).toEqual(offer.pointsEarned);
    expect(offerPointsEarn.getAttribute('data-is-classic')).toBe('false');
    expect(offerPointsEarn.getAttribute('data-is-currency-cash')).toBe('true');
    expect(offerPointsEarn.getAttribute('data-is-points-plus-pay')).toBe('false');
  });

  it('passes correct currency type when currency is PTS', () => {
    const ptsOffer = {
      ...offer,
      charges: { ...offer.charges, total: { amount: '1000', currency: 'PTS' } },
    };
    render({ offer: ptsOffer });
    const offerPointsEarn = screen.getByTestId('OfferPointsEarn');
    expect(offerPointsEarn.getAttribute('data-is-currency-cash')).toBe('false');
  });

  it('passes correct isPointsPlusPay when selector returns true', () => {
    mocked(uiSelectors.getIsPointsPay).mockReturnValue(true);
    render();
    const offerPointsEarn = screen.getByTestId('OfferPointsEarn');
    expect(offerPointsEarn.getAttribute('data-is-points-plus-pay')).toBe('true');
  });

  describe('does not render when', () => {
    const testCases = [
      {
        condition: 'offer type is classic',
        offer: { ...offer, type: 'classic' },
      },
      {
        condition: 'qffPoints total is zero',
        offer: {
          ...offer,
          pointsEarned: { qffPoints: { base: 0, total: 0 }, qbrPoints: { total: 0 } },
        },
      },
    ];

    testCases.forEach(({ condition, offer: testOffer }) => {
      it(condition, () => {
        render({ offer: testOffer });
        expect(screen.queryByTestId('OfferPointsEarn')).not.toBeInTheDocument();
      });
    });

    it('pointsEarned is null from hook', () => {
      (usePointsEarned as jest.Mock).mockReturnValue({
        pointsEarned: null,
        onCashPaymentAmountChange,
      });
      render();
      expect(screen.queryByTestId('OfferPointsEarn')).not.toBeInTheDocument();
    });
  });
});

it('sends an event to the dataLayer when collapsed', async () => {
  render();
  const collapseButton = screen.getByTestId('collapse-offer-summary');
  await userEvent.click(collapseButton);

  expect(emitInteractionEvent).toHaveBeenCalledWith({
    type: 'Room Offer Details',
    value: 'Offer Collapsed',
  });
});
