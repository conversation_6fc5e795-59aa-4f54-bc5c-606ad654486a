import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { useDispatch, useSelector } from 'react-redux';
import * as dealSelectors from 'store/deal/dealSelectors';
import * as sessionStorage from 'components/HomePage/sessionStorage';
import { useRouter } from 'next/router';
import * as routerSelectors from 'store/router/routerSelectors';
import * as reactUse from 'react-use';
import useDealsGa4Event from 'hooks/useDealsGa4Event';

import DealsLayout from './DealsLayout';

jest.mock('./DealsHelmet', () => () => <div data-testid="deals-helmet" />);
jest.mock('./DealsFetcher', () => () => <div data-testid="deals-fetcher" />);
jest.mock('components/CampaignMessaging', () => () => <div data-testid="campaign-messaging"></div>);
jest.mock('./DealsHeader', () => () => <div data-testid="deals-header" />);
jest.mock('./DealTypes', () => () => <div data-testid="deal-types" />);
jest.mock('components/PageBlock', () => ({ children, py }) => (
  <div data-testid="page-block" data-py={JSON.stringify(py)}>
    {children}
  </div>
));
jest.mock('./GoToSearchBanner', () => () => <div data-testid="go-to-search-banner" />);
jest.mock('components/Content/ListOfRegions', () => (props) => <div data-testid="list-of-regions" data-config={JSON.stringify(props)} />);
jest.mock('components/PopularDestinationFooter', () => ({ links }) => (
  <div data-testid="popular-destination-footer" data-links={JSON.stringify(links)} />
));

jest.mock('react-redux', () => ({
  useDispatch: jest.fn(),
  useSelector: jest.fn(),
}));
jest.mock('store/search/searchActions', () => ({
  updateQuery: jest.fn(() => ({ type: 'UPDATE_QUERY_MOCKED' })),
}));
jest.mock('store/campaign/campaignActions', () => ({
  fetchCampaign: jest.fn(() => ({ type: 'FETCH_CAMPAIGN_MOCKED' })),
}));

jest.mock('store/deal/dealSelectors', () => ({
  getHasValidQuery: jest.fn(),
  getPageContent: jest.fn(),
  getFooterLinksByCategory: jest.fn(),
}));
jest.mock('store/router/routerSelectors', () => ({
  getQueryPayWith: jest.fn(),
}));

jest.mock('components/HomePage/sessionStorage', () => ({
  getGclid: jest.fn(),
  clearGclid: jest.fn(),
}));
jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));
jest.mock('react-use', () => ({
  useMount: jest.fn(),
}));
jest.mock('hooks/useDealsGa4Event', () => jest.fn());

const mockDispatch = jest.fn();

let mockGetHasValidQuery;
let mockGetPageContent;
let mockGetFooterLinksByCategory;
let mockUpdateQuery;
let mockFetchCampaign;
let mockGetGclid;
let mockClearGclid;
let mockUseRouter;
let mockGetQueryPayWith;
let mockUseMount;
let mockUseDealsGa4Event;

beforeAll(() => {
  mockGetHasValidQuery = dealSelectors.getHasValidQuery;
  mockGetPageContent = dealSelectors.getPageContent;
  mockGetFooterLinksByCategory = dealSelectors.getFooterLinksByCategory;
  // Jest fails if imported at top of file
  /* eslint-disable @typescript-eslint/no-var-requires */
  mockUpdateQuery = require('store/search/searchActions').updateQuery;
  mockFetchCampaign = require('store/campaign/campaignActions').fetchCampaign;
  /* eslint-enable @typescript-eslint/no-var-requires */
  mockGetGclid = sessionStorage.getGclid;
  mockClearGclid = sessionStorage.clearGclid;
  mockUseRouter = useRouter;
  mockGetQueryPayWith = routerSelectors.getQueryPayWith;
  mockUseMount = reactUse.useMount;
  mockUseDealsGa4Event = useDealsGa4Event;
});

beforeEach(() => {
  jest.clearAllMocks();

  (useDispatch as jest.Mock).mockReturnValue(mockDispatch);
  (useSelector as jest.Mock).mockImplementation((selectorFn) => {
    if (selectorFn === mockGetHasValidQuery) return true;
    if (selectorFn === mockGetPageContent) return { regionLinks: [] };
    if (selectorFn === mockGetFooterLinksByCategory) return [];
    if (selectorFn === mockGetQueryPayWith) return 'cash';
    return undefined;
  });

  mockUpdateQuery.mockReturnValue({ type: 'UPDATE_QUERY_MOCKED' });
  mockFetchCampaign.mockReturnValue({ type: 'FETCH_CAMPAIGN_MOCKED' });
  mockGetGclid.mockReturnValue(null);
  mockClearGclid.mockImplementation(() => {});
  mockUseRouter.mockReturnValue({ query: {} });
  mockUseMount.mockImplementation((cb) => cb());
  mockUseDealsGa4Event.mockImplementation(() => {});
});

describe('DealsLayout', () => {
  it('should render all child components and dispatch initial actions correctly on mount', () => {
    render(<DealsLayout />);

    expect(screen.getByTestId('deals-helmet')).toBeInTheDocument();
    expect(screen.getByTestId('deals-fetcher')).toBeInTheDocument();
    expect(screen.getByTestId('campaign-messaging')).toBeInTheDocument();
    expect(screen.getByTestId('deals-header')).toBeInTheDocument();
    expect(screen.getByTestId('deal-types')).toBeInTheDocument();
    expect(screen.getByTestId('go-to-search-banner')).toBeInTheDocument();

    expect(mockFetchCampaign).toHaveBeenCalledTimes(1);
    expect(mockDispatch).toHaveBeenCalledWith({ type: 'FETCH_CAMPAIGN_MOCKED' });

    expect(mockUpdateQuery).not.toHaveBeenCalled();

    expect(mockClearGclid).toHaveBeenCalledTimes(1);

    expect(mockUseDealsGa4Event).toHaveBeenCalledTimes(1);
  });

  it('should dispatch updateQuery with DEFAULT_QUERY if isQueryValid is false on initial render', async () => {
    (useSelector as jest.Mock).mockImplementation((selectorFn) => {
      if (selectorFn === mockGetHasValidQuery) return false;
      if (selectorFn === mockGetPageContent) return { regionLinks: [] };
      if (selectorFn === mockGetFooterLinksByCategory) return [];
      if (selectorFn === mockGetQueryPayWith) return 'points';
      return undefined;
    });
    mockGetGclid.mockReturnValue('test-gclid-123');

    render(<DealsLayout />);

    await waitFor(() => {
      expect(mockUpdateQuery).toHaveBeenCalledTimes(1);
    });

    expect(mockClearGclid).toHaveBeenCalledTimes(1);
  });

  it('should render ListOfRegions components based on regionLinks', () => {
    const mockRegionLinks = [
      { title: 'Region A', links: [{ text: 'Link 1' }] },
      { title: 'Region B', links: [{ text: 'Link 2' }] },
    ];
    (useSelector as jest.Mock).mockImplementation((selectorFn) => {
      if (selectorFn === mockGetPageContent) return { regionLinks: mockRegionLinks };
      if (selectorFn === mockGetHasValidQuery) return true;
      if (selectorFn === mockGetFooterLinksByCategory) return [];
      if (selectorFn === mockGetQueryPayWith) return 'cash';
      return undefined;
    });

    render(<DealsLayout />);

    const listOfRegionsComponents = screen.getAllByTestId('list-of-regions');
    expect(listOfRegionsComponents).toHaveLength(mockRegionLinks.length);
    expect(listOfRegionsComponents[0]).toHaveAttribute('data-config', JSON.stringify(mockRegionLinks[0]));
    expect(listOfRegionsComponents[1]).toHaveAttribute('data-config', JSON.stringify(mockRegionLinks[1]));
    expect(screen.getAllByTestId('page-block')).toHaveLength(3);
  });

  it('should render PopularDestinationFooter if footerLinks are present', () => {
    const mockFooterLinks = [{ title: 'Footer Link 1' }, { title: 'Footer Link 2' }];
    (useSelector as jest.Mock).mockImplementation((selectorFn) => {
      if (selectorFn === mockGetFooterLinksByCategory) return mockFooterLinks;
      if (selectorFn === mockGetHasValidQuery) return true;
      if (selectorFn === mockGetPageContent) return { regionLinks: [] };
      if (selectorFn === mockGetQueryPayWith) return 'cash';
      return undefined;
    });

    render(<DealsLayout />);

    const footer = screen.getByTestId('popular-destination-footer');
    expect(footer).toBeInTheDocument();
    expect(footer).toHaveAttribute('data-links', JSON.stringify(mockFooterLinks));
    expect(screen.getAllByTestId('page-block')).toHaveLength(2);
  });

  it('should not render PopularDestinationFooter if footerLinks are empty', () => {
    render(<DealsLayout />);
    expect(screen.queryByTestId('popular-destination-footer')).not.toBeInTheDocument();
  });

  it('should not dispatch CLASSIC_REWARDS_QUERY if slug does not include "classic-rewards"', async () => {
    mockUseRouter.mockReturnValue({ query: { slug: ['some-other-path', 'not-classic'] } });
    render(<DealsLayout />);

    await new Promise((resolve) => setTimeout(resolve, 0));

    expect(mockUpdateQuery).not.toHaveBeenCalled();
  });

  it('should prioritize CLASSIC_REWARDS_QUERY if both isQueryValid is false and slug is "classic-rewards"', async () => {
    (useSelector as jest.Mock).mockImplementation((selectorFn) => {
      if (selectorFn === mockGetHasValidQuery) return false;
      if (selectorFn === mockGetPageContent) return { regionLinks: [] };
      if (selectorFn === mockGetFooterLinksByCategory) return [];
      if (selectorFn === mockGetQueryPayWith) return 'cash';
      return undefined;
    });
    mockGetGclid.mockReturnValue('gclid-combo');
    mockUseRouter.mockReturnValue({ query: { slug: ['classic-rewards'] } });

    render(<DealsLayout />);

    await waitFor(() => {
      const calls = mockUpdateQuery.mock.calls;
      expect(calls.length).toBeGreaterThanOrEqual(1);
    });
    await waitFor(() => {
      const calls = mockUpdateQuery.mock.calls;
      expect(calls[calls.length - 1][0]).toEqual({ payWith: 'points', page: 1, gclid: 'gclid-combo' });
    });
    expect(mockClearGclid).toHaveBeenCalledTimes(1);
  });

  it('should call clearGclid every time component renders (due to useEffect dependency on isQueryValid)', () => {
    const { rerender } = render(<DealsLayout />);
    expect(mockClearGclid).toHaveBeenCalledTimes(1);

    (useSelector as jest.Mock).mockImplementation((selectorFn) => {
      if (selectorFn === mockGetHasValidQuery) return true;
      if (selectorFn === mockGetPageContent) return { regionLinks: [] };
      if (selectorFn === mockGetFooterLinksByCategory) return [];
      if (selectorFn === mockGetQueryPayWith) return 'cash';
      return undefined;
    });
    rerender(<DealsLayout />);
    expect(mockClearGclid).toHaveBeenCalledTimes(1);

    (useSelector as jest.Mock).mockImplementation((selectorFn) => {
      if (selectorFn === mockGetHasValidQuery) return false;
      if (selectorFn === mockGetPageContent) return { regionLinks: [] };
      if (selectorFn === mockGetFooterLinksByCategory) return [];
      if (selectorFn === mockGetQueryPayWith) return 'cash';
      return undefined;
    });
    rerender(<DealsLayout />);
    expect(mockClearGclid).toHaveBeenCalledTimes(2);
  });
});
