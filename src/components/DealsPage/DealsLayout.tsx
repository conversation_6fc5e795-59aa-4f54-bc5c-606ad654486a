import CampaignMessaging from 'components/CampaignMessaging';
import PageBlock from 'components/PageBlock';
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { getHasValidQuery, getPageContent, getFooterLinksByCategory } from 'store/deal/dealSelectors';
import { updateQuery } from 'store/search/searchActions';
import DealsFetcher from './DealsFetcher';
import DealsHelmet from './DealsHelmet';
import DealsHeader from './DealsHeader';
import GoToSearchBanner from './GoToSearchBanner';
import ListOfRegions from 'components/Content/ListOfRegions';
import DealTypes from './DealTypes';
import PopularDestinationFooter from 'components/PopularDestinationFooter';
import { useMount } from 'react-use';
import { fetchCampaign } from 'store/campaign/campaignActions';
import { getGclid, clearGclid } from 'components/HomePage/sessionStorage';
import { useRouter } from 'next/router';
import { getQueryPayWith } from 'store/router/routerSelectors';
import useDealsGa4Event from 'hooks/useDealsGa4Event';

const DealsLayout = () => {
  const dispatch = useDispatch();
  const isQueryValid = useSelector(getHasValidQuery);
  const { regionLinks = [] } = useSelector(getPageContent);
  const footerLinks = useSelector(getFooterLinksByCategory);
  const gclid = getGclid();
  const router = useRouter();
  const payWith = useSelector(getQueryPayWith);
  const DEFAULT_QUERY = {
    payWith: payWith,
    page: 1,
    ...(gclid && { gclid: gclid }),
  };

  const CLASSIC_REWARDS_QUERY = {
    payWith: 'points',
    page: 1,
    ...(gclid && { gclid: gclid }),
  };

  useMount(() => {
    dispatch(fetchCampaign({}));
  });

  useEffect(() => {
    if (!isQueryValid) {
      dispatch(updateQuery(DEFAULT_QUERY));
    }
    clearGclid();
  }, [isQueryValid]); // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    const isClassicRewards = router.query.slug?.includes('classic-rewards');
    if (isClassicRewards) {
      dispatch(updateQuery(CLASSIC_REWARDS_QUERY));
    }
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  useDealsGa4Event();

  return (
    <>
      <DealsHelmet />
      <DealsFetcher />
      <CampaignMessaging />
      <DealsHeader />

      <DealTypes />

      <PageBlock py={[4, 6]}>
        <GoToSearchBanner />
      </PageBlock>

      {regionLinks?.map((config, index) => (
        <PageBlock py={[4, 6]} key={`region-link-${index}`}>
          <ListOfRegions {...config} />
        </PageBlock>
      ))}

      {!!footerLinks?.length && (
        <PageBlock>
          <PopularDestinationFooter links={footerLinks} />
        </PageBlock>
      )}
    </>
  );
};

export default DealsLayout;
