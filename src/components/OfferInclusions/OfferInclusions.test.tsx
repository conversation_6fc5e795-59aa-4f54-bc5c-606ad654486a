import React from 'react';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithProviders } from 'test-utils/reactUtils';
import { mocked } from 'test-utils';
import OfferInclusions from './OfferInclusions';
import { inclusionsData, valueAdds } from '../PropertyPage/PropertyAvailability/RoomTypes/RoomTypeList/Offer/fixtures';
import useCustomInclusions from 'hooks/optimizely/useCustomInclusions';

jest.mock('hooks/optimizely/useCustomInclusions');
jest.mock('store/ui/uiSelectors');

const mockOnClick = jest.fn();

const defaultProps = {
  onClick: mockOnClick,
};

const renderComponent = (props = {}) => renderWithProviders(<OfferInclusions {...props} {...defaultProps} />);

describe('OfferInclusions with custom inclusions', () => {
  beforeEach(() => {
    mocked(useCustomInclusions).mockReturnValue({
      isReady: true,
      isCustomInclusions: true,
    });
  });
  describe('inclusions list', () => {
    describe('when there are no inclusions', () => {
      it('does not renders the inclusions list', () => {
        renderComponent({ inclusions: [] });
        const inclusionItems = screen.queryAllByTestId('flag');
        expect(inclusionItems).toHaveLength(0);
      });
      it('does not render an inclusions if has no name', () => {
        renderComponent({ inclusions: [{ icon: 'welcomeGift', name: '', description: 'A welcome gift' }] });
        const inclusionItems = screen.queryAllByTestId('flag');
        expect(inclusionItems).toHaveLength(0);
      });
    });
    describe('when view all is true', () => {
      it('renders the full inclusions list', () => {
        renderComponent({ inclusions: inclusionsData.moreThanThree, viewAll: true });
        const inclusionItems = screen.getAllByTestId('flag');
        expect(inclusionItems).toHaveLength(7);
      });

      it('renders valueAdds before inclusions', () => {
        renderComponent({ inclusions: inclusionsData.lessThanThree, valueAdds: valueAdds });
        const name = screen.getAllByTestId('inclusion-name');
        expect(name[0]).toHaveTextContent('Dinner for two');
        expect(name[1]).toHaveTextContent('Guaranteed Room Upgrade');
        expect(name[2]).toHaveTextContent('All Inclusive');
      });

      it('renders unique valueAdds, remove duplicates', () => {
        renderComponent({ inclusions: inclusionsData.lessThanThree, valueAdds: ['Dinner for two', 'Dinner for two'] });
        const name = screen.getAllByTestId('inclusion-name');
        expect(name[0]).toHaveTextContent('Dinner for two');
        expect(name[1]).not.toHaveTextContent('Dinner for two');
        expect(name[1]).toHaveTextContent('Guaranteed Room Upgrade');
        expect(name[2]).toHaveTextContent('All Inclusive');
      });

      it('renders name and description if available', () => {
        renderComponent({ inclusions: inclusionsData.lessThanThree });
        const name = screen.getAllByTestId('inclusion-name');
        const description = screen.getAllByTestId('inclusion-description');
        expect(name[0]).toHaveTextContent('Guaranteed Room Upgrade');
        expect(description[0]).toHaveTextContent('Enjoy a room upgrade with this offer!');
      });

      it('renders name and no description if are the same value', () => {
        renderComponent({
          inclusions: [{ name: 'All Inclusive', description: 'All Inclusive' }],
        });
        const name = screen.getAllByTestId('inclusion-name');
        const description = screen.queryAllByTestId('inclusion-description');
        expect(name[0]).toHaveTextContent('All Inclusive');
        expect(description).toHaveLength(0);
      });

      it('renders name and no description if are the same value with extra symbols', () => {
        renderComponent({
          inclusions: [{ name: 'Late Checkout', description: 'Late check-out' }],
        });
        const name = screen.getAllByTestId('inclusion-name');
        const description = screen.queryAllByTestId('inclusion-description');
        expect(name[0]).toHaveTextContent('Late Checkout');
        expect(description).toHaveLength(0);
      });
    });

    describe('when view all is false', () => {
      describe('and the inclusions are more than 3', () => {
        it('renders only the preview amount of inclusions', () => {
          renderComponent({ inclusions: inclusionsData.moreThanThree, viewAll: false });
          const inclusionItems = screen.getAllByTestId('flag');
          expect(inclusionItems).toHaveLength(3);
        });
      });
      describe('and the inclusions are less than 3', () => {
        it('renders all the of inclusions', () => {
          renderComponent({ inclusions: inclusionsData.lessThanThree, viewAll: false });

          const inclusionItems = screen.getAllByTestId('flag');
          expect(inclusionItems).toHaveLength(2);
        });
      });
    });
  });
  describe('when isToggleExpansion', () => {
    describe('is true', () => {
      describe('the inclusions are more than 3 and viewAll is false', () => {
        it('displays the toggle inclusions message', () => {
          renderComponent({ inclusions: inclusionsData.moreThanThree, isToggleExpansion: true, viewAll: false });
          expect(screen.getByTestId('toggle-inclusions-message')).toHaveTextContent('+ 4 more inclusions');
        });
      });
      describe('the inclusions are more than 3 and viewAll is true', () => {
        it('does NOT display the toggle inclusions message', () => {
          renderComponent({ inclusions: inclusionsData.moreThanThree, isToggleExpansion: true, viewAll: true });
          expect(screen.queryByTestId('toggle-inclusions-message')).not.toBeInTheDocument();
        });
      });
      describe('the inclusions are less than 3 and viewAll is false', () => {
        it('does NOT display the toggle inclusions message', () => {
          renderComponent({ inclusions: inclusionsData.lessThanThree, isToggleExpansion: true, viewAll: false });
          expect(screen.queryByTestId('toggle-inclusions-message')).not.toBeInTheDocument();
        });
      });
    });
    describe('is false', () => {
      describe('the inclusions are more than 3 and viewAll is false', () => {
        it('does NOT display the toggle inclusions message', () => {
          renderComponent({ inclusions: inclusionsData.moreThanThree, viewAll: false });
          expect(screen.queryByTestId('toggle-inclusions-message')).not.toBeInTheDocument();
        });
      });
    });
  });
  describe('when isExpandText', () => {
    describe('is true', () => {
      describe('and the inclusions are more than 3', () => {
        it('displays the toggle inclusions message', () => {
          renderComponent({ inclusions: inclusionsData.moreThanThree, isExpandText: true });
          expect(screen.getByTestId('view-all-inclusions-message')).toHaveTextContent('View all 7 inclusions');
        });
      });
      describe('the inclusions are less than 3', () => {
        it('does NOT display the toggle inclusions message', () => {
          renderComponent({ inclusions: inclusionsData.lessThanThree, isExpandText: true });
          expect(screen.queryByTestId('view-all-inclusions-message')).not.toBeInTheDocument();
        });
      });
      describe('clicking the view all message', () => {
        it('modify the message and the icon', async () => {
          renderComponent({ inclusions: inclusionsData.moreThanThree, isExpandText: true });
          const viewAllButton = screen.getByTestId('view-all-inclusions-message');

          expect(screen.getByTestId('view-all-inclusions-message')).toHaveTextContent('View all 7 inclusions');
          expect(screen.getByTitle('expandMore')).toBeInTheDocument();

          await userEvent.click(viewAllButton);

          expect(screen.getByTestId('view-all-inclusions-message')).toHaveTextContent('View less');
          expect(screen.getByTitle('expandLess')).toBeInTheDocument();
        });
      });
    });
    describe('is false', () => {
      describe('the inclusions are more than 3 and viewAll is false', () => {
        it('does NOT display the toggle inclusions message', () => {
          renderComponent({ inclusions: inclusionsData.moreThanThree, isExpandText: false });
          expect(screen.queryByTestId('view-all-inclusions-message')).not.toBeInTheDocument();
        });
      });
    });
  });
  describe('when custom Inclusions is false', () => {
    beforeEach(() => {
      mocked(useCustomInclusions).mockReturnValue({
        isReady: true,
        isCustomInclusions: false,
      });
    });
    it('renders only the inclusion description as name', () => {
      renderComponent({ inclusions: [{ name: 'Inclusion name', description: 'Inclusion description' }] });
      expect(screen.getByTestId('inclusion-name')).toHaveTextContent('Inclusion description');
      expect(screen.queryByTestId('inclusion-description')).not.toBeInTheDocument();
    });
  });
});
