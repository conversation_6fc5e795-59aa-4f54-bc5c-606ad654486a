import kebabCase from 'lodash/kebabCase';
import deburr from 'lodash/deburr';

function normalizeString(str: string = '') {
  return kebabCase(deburr(str)).replace(/-/g, '');
}

export function isDescriptionDifferent(description: string | undefined, name: string) {
  return !!description && normalizeString(name) !== normalizeString(description);
}

function removeDuplicateStrings(arr: string[] = []) {
  const seen = new Set<string>();
  return arr.filter((item) => {
    const normalized = normalizeString(item);
    if (seen.has(normalized)) return false;
    seen.add(normalized);
    return true;
  });
}

const createValueAddsObj = (valueAddArray) => {
  return valueAddArray.map((value) => ({
    description: value,
    name: 'value adds',
    code: 'welcome_gift',
    icon: 'welcomeGift',
  }));
};

export function getUniqueValueAddsObj(valueAdds: string[] = []) {
  const uniqueValueAdds = removeDuplicateStrings(valueAdds);
  return createValueAddsObj(uniqueValueAdds);
}
