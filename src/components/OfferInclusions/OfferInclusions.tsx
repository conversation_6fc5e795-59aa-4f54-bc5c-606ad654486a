import React, { useState, useEffect } from 'react';
import { Box, Text, Flex, Icon } from '@qga/roo-ui/components';
import { Flag } from '../PropertyPage/PropertyAvailability/RoomTypes/RoomTypeList/Offer/components/primitives';
import TextButton from 'components/TextButton';
import type { Inclusion } from 'types/property';
import useCustomInclusions from 'hooks/optimizely/useCustomInclusions';
import { INCLUSIONS_PREVIEW_AMOUNT } from 'config/constants';
import { isDescriptionDifferent, getUniqueValueAddsObj } from './offerInclusions.helper';

interface OfferInclusionsProps {
  inclusions?: Inclusion[];
  valueAdds?: string[];
  isToggleExpansion?: boolean;
  isExpandText?: boolean;
  viewAll?: boolean;
  onClick?: () => void;
}

const OfferInclusions: React.FC<OfferInclusionsProps> = ({
  inclusions = [],
  valueAdds = [],
  isToggleExpansion = false,
  isExpandText = false,
  viewAll = false,
  onClick,
}) => {
  const { isCustomInclusions } = useCustomInclusions();
  const [showAll, setShowAll] = useState(viewAll);

  useEffect(() => {
    setShowAll(viewAll);
  }, [viewAll]);

  const valueAddsObj = getUniqueValueAddsObj(valueAdds);
  const allInclusions = [...valueAddsObj, ...inclusions];

  const previewInclusions = allInclusions.slice(0, INCLUSIONS_PREVIEW_AMOUNT);
  const hasPreview = allInclusions.length > INCLUSIONS_PREVIEW_AMOUNT;
  const inclusionsToRender = showAll ? allInclusions : previewInclusions;

  const remainingCount = allInclusions.length - INCLUSIONS_PREVIEW_AMOUNT;

  return (
    <Box color="greys.charcoal" mb={2}>
      <Flex flexDirection="column" data-testid="inclusions-list" id="inclusions-list">
        {inclusionsToRender.map(({ icon, name, description }, i) => {
          const isValueAdd = name === 'value adds';
          const showDescription = isDescriptionDifferent(description, name) && !isValueAdd && isCustomInclusions;
          const nameToDisplay = isValueAdd || !isCustomInclusions ? description : name;

          if (!nameToDisplay) return null;

          return (
            <Flag key={i} icon={icon}>
              <Flex flexDirection="column">
                <Text fontSize="16px" fontWeight={icon === 'welcomeGift' ? 'bold' : 'normal'} data-testid="inclusion-name">
                  {nameToDisplay}
                </Text>
                {showDescription && (
                  <Text fontSize="14px" color="greys.steel" data-testid="inclusion-description">
                    {description}
                  </Text>
                )}
              </Flex>
            </Flag>
          );
        })}
      </Flex>

      {isToggleExpansion && !showAll && hasPreview && (
        <TextButton onClick={onClick} fontSize="16px" mt="3" data-testid="toggle-inclusions-message">
          {`+ ${remainingCount} more ${remainingCount === 1 ? 'inclusion' : 'inclusions'}`}
        </TextButton>
      )}

      {isExpandText && hasPreview && (
        <TextButton
          onClick={() => setShowAll(!showAll)}
          aria-expanded={showAll}
          aria-controls="inclusions-list"
          data-testid="view-all-inclusions-message"
          textDecoration="none"
          mt={2}
        >
          <Text color="brand.primary">{showAll ? 'View less' : `View all ${allInclusions.length} inclusions`}</Text>
          <Icon name={showAll ? 'expandLess' : 'expandMore'} ml={2} />
        </TextButton>
      )}
    </Box>
  );
};

export default React.memo(OfferInclusions);
