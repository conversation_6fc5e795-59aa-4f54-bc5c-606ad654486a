import React, { useMemo } from 'react';
import { Box, Container, Flex, Heading, Icon } from '@qga/roo-ui/components';
import PageBlock from 'components/PageBlock';
import { rem } from 'polished';
import { useSelector } from 'react-redux';
import { getPathName } from 'store/router/routerSelectors';
import { qantasBusinessRewards, qantasQff } from '@qga/roo-ui/logos';
import { DisclaimerItemLogo, DisclaimerText, DisclaimerLink, TripAdvisorLogo } from 'components/Footer/DisclaimerItems';
import { useDataLayer } from 'hooks/useDataLayer';
import { getCampaignTermsAndConditions } from 'store/campaign/campaignSelectors';
import TravelInsuranceDisclaimer from './TravelInsuranceDisclaimer';
import { themeGet } from 'styled-system';
import {
  TRIP_ADVISOR_IMG_BRANDING_URL,
  QCOM_TERMS_AND_CONDITIONS_URL,
  POINTS_EARN_ENABLED,
  TRAVEL_INSURANCE_CROSS_SELL_ENABLED,
  AIRBNB_LANDING_PAGE_URL,
  HOTELS_URL,
  HOTELS_BASE_URL,
  HOTEL_FAQ_URL,
} from 'config';
import Markup from 'components/Markup';
import useCampaignTerms from 'hooks/optimizely/useCampaignTerms';
import useYieldifyCampaign from 'hooks/optimizely/useYieldifyCampaign';
import useAirlineDisclaimer from 'hooks/optimizely/useAirlineDisclaimer';
import useStandardCampaign from 'hooks/optimizely/useStandardCampaign';

interface DisclaimerSection {
  id: string;
  logo?: JSX.Element;
  items: (JSX.Element | null | boolean)[];
}

const Disclaimer = () => {
  const { emitInteractionEvent } = useDataLayer();
  const campaignTermsAndConditions = useSelector(getCampaignTermsAndConditions);
  const pathName = useSelector(getPathName);
  const isConfirmationPage = pathName?.startsWith('/bookings');
  const { isCampaignTermsEnabled, disclaimer: standardCampaignDisclaimerText } = useCampaignTerms();
  const { isYieldifyCampaignEnabled, disclaimer: yieldifyCampaignDisclaimerText } = useYieldifyCampaign();
  const {
    isStandardCampaignEnabled,
    termsAndConditions: blueBannerCampaignDisclaimer,
    additionalTermsAndConditions: blueBannerAdditionalDisclaimer,
    replaceQffFinalTerms: showBlueBannerTermsInQffFinalTerms,
  } = useStandardCampaign();
  const showBlueBannerDisclaimer = isStandardCampaignEnabled && !showBlueBannerTermsInQffFinalTerms;
  const { isAirlineDisclaimer } = useAirlineDisclaimer();

  const sections = useMemo(() => {
    const qffDisclaimer = (
      <DisclaimerText data-testid="qff-disclaimer-text">
        You must be a Qantas Frequent Flyer member to earn and redeem points. Membership and points are subject to the Qantas Frequent Flyer
        program terms and conditions.
      </DisclaimerText>
    );

    const qffTerms = (
      <DisclaimerText data-testid="qff-terms-text">
        Qantas Frequent Flyer members will earn 3 Qantas Points per A$1 spent unless otherwise specified, for hotel stays booked through
        qantas.com/hotels, except Classic Hotel Rewards and Airbnb bookings. Points Club members will earn 25% more Qantas Points, and
        Points Club Plus members will earn 50% more Qantas Points. Qantas Points will be credited to your account at least 8 weeks after
        check-out. Qantas Points can be earned on cash only purchases and the cash component of Points Plus Pay purchases on eligible
        bookings and will not be earned on cancelled or refunded bookings. Qantas Points may be earned by the member in whose name the
        booking is made. Members will not be able to earn points on additional charges paid to the accommodation provider for extras
        (including cots, breakfasts and other incidentals) on check-in or check-out (as applicable).
      </DisclaimerText>
    );

    const qffDepositPay = (
      <DisclaimerText data-testid="qff-deposit-pay-text">
        Deposit Pay is available on selected properties that offer a free cancellation window that is 21 days or more in the future from the
        date of booking.
      </DisclaimerText>
    );

    const qffFaqs = (
      <DisclaimerText data-testid="qff-faqs-text">
        Visit our{' '}
        <DisclaimerLink
          data-testid="qff-faqs-link"
          href={HOTEL_FAQ_URL}
          onClick={() => emitInteractionEvent({ type: 'FAQ', value: 'View FAQ Page Link Selected' })}
        >
          {' '}
          FAQ page{' '}
        </DisclaimerLink>{' '}
        for more.
      </DisclaimerText>
    );

    const qffPointsPay = (
      <DisclaimerText data-testid="qff-points-pay-text">
        * Qantas Frequent Flyer members can redeem Qantas Points when booking hotel accommodation through qantas.com/hotels or holiday
        packages through qantas.com/holidays, using Points Plus Pay. Members cannot redeem points for additional charges paid to the hotel
        for extras (including cots, breakfasts and other incidentals) on check-in or check-out (as applicable). Points Plus Pay allows you
        to choose the number of Qantas Points you redeem above the specified minimum level of 5,000 and pay for the remainder of the booking
        value with an Accepted Payment Card (including VISA, MasterCard or American Express). Points Plus Pay is not available for Classic
        Hotel Rewards.{' '}
        <DisclaimerLink
          data-testid="pointsPlusPayViewFullTermsAndConditionsLink"
          href={`${HOTELS_URL}/hotels-and-airbnb-terms-and-conditions`}
          onClick={() =>
            emitInteractionEvent({ type: 'Points Plus Pay Conditions', value: 'View Full Terms and Conditions Link Selected' })
          }
        >
          View full terms and conditions here
        </DisclaimerLink>
      </DisclaimerText>
    );

    const qffBonusPoints = (
      <DisclaimerText data-testid="qff-bonus-points-text">
        ++ 500 bonus Qantas Points will be awarded to Qantas Frequent Flyer members who make their first Airbnb booking. Qantas Frequent
        Flyer members will earn 1 Qantas Point per A$1 value for all Airbnb stays booked through{' '}
        <DisclaimerLink
          data-testid="airbnbHomepageLink"
          href={AIRBNB_LANDING_PAGE_URL}
          onClick={() => emitInteractionEvent({ type: 'Terms and Conditions', value: 'AirBnb Selected' })}
        >
          qantas.com/airbnb
        </DisclaimerLink>
        .
      </DisclaimerText>
    );

    const savingsDisclaimer = (
      <DisclaimerText data-testid="savings-disclaimer-text">
        ~ Saving is off the hotel&apos;s generally available rate for the same property, room type, days, inclusions and conditions.
      </DisclaimerText>
    );

    // additional disclaimer for out of the box campaigns such as the WA Triple Points Campaign
    const standardCampaignDisclaimer = (
      <>
        {isCampaignTermsEnabled && (
          <DisclaimerText
            data-testid="local-campaign-disclaimer-alternative-text"
            dangerouslySetInnerHTML={{ __html: standardCampaignDisclaimerText }}
            color={themeGet('colors.greys.steel')}
          />
        )}
      </>
    );

    const yieldifyCampaignDisclaimer = (
      <>
        {isYieldifyCampaignEnabled && (
          <DisclaimerText
            data-testid="yieldify-campaign-disclaimer-text"
            dangerouslySetInnerHTML={{ __html: yieldifyCampaignDisclaimerText }}
            color={themeGet('colors.greys.steel')}
          />
        )}
      </>
    );

    const qbrDisclaimer = (
      <DisclaimerText data-testid="qbr-disclaimer-text">
        ** A business must be a Qantas Business Rewards Member to earn Qantas Points for business. Businesses earn 1 Qantas Point per AU$1
        spent on eligible Qantas Hotels worldwide booked through &nbsp;
        <DisclaimerLink data-testid="qantasHotelLink" href={`${HOTELS_BASE_URL}/hotels`}>
          qantas.com/hotels
        </DisclaimerLink>
        . Eligible Qantas hotels excludes Classic Hotel Rewards and Airbnb bookings. The Qantas Business Rewards Member&apos;s ABN must be
        included at the time of booking. Qantas Points are not earned on any amounts payable directly to the hotel. &nbsp;
        <DisclaimerLink
          data-testid="businessRewardConditionApplyLink"
          href={QCOM_TERMS_AND_CONDITIONS_URL}
          onClick={() => emitInteractionEvent({ type: 'Business Rewards Conditions', value: 'Conditions Apply Link Selected' })}
        >
          Conditions apply
        </DisclaimerLink>
      </DisclaimerText>
    );

    const airlineDisclaimer = isAirlineDisclaimer && (
      <>
        <Box>
          <DisclaimerText>
            ‡ Bonus Status Credits will count towards Lifetime Status for this campaign. This includes any bookings already made during the
            campaign period (28 August 2025 - 3 September 2025).
          </DisclaimerText>
        </Box>
        <Box>
          <DisclaimerText>
            Qantas Frequent Flyer members who register and make a new eligible flight booking between 12:01am (AEST) 28 August 2025 and
            11.59pm (AEST) 3 September 2025 for travel between 4 September 2025 and 22 August 2026, will have a choice of either one (1) of
            two (2) available rewards. The choice of either double Qantas Points or double Status Credits must be made before 11.59pm (AEST)
            3 September 2025 via the Qantas App or <DisclaimerLink href={HOTELS_BASE_URL}>qantas.com</DisclaimerLink>. Once the registration
            period is closed, Qantas will not accept changes to members&apos; preferred bonus reward choice. An email may be sent confirming
            successful registration, if you hold a valid email address in your Qantas Frequent Flyer account. An{' '}
            <DisclaimerLink href={`${HOTELS_BASE_URL}/au/en/frequent-flyer/discover-and-join/terms-and-conditions.html#eligibleflights`}>
              Eligible Flight
            </DisclaimerLink>{' '}
            for the purpose of this promotion means a Qantas marketed and operated flight (with a QF flight number on the ticket) on an
            Australian Domestic or International ticket where Qantas Points and Status Credits are normally earned, including flights booked
            as part of a Qantas Holidays package. QF flights operated by Alliance Airlines, National Jet, Airlink, Eastern Australia
            Airlines and Sunstate Airlines are eligible for this offer.{' '}
            <DisclaimerLink href={`${HOTELS_BASE_URL}/au/en/book-a-trip/flights/finnair-a330.html`}>
              QF flights operated by Finnair for Qantas
            </DisclaimerLink>{' '}
            will also be eligible for this offer. Any other flight with a QF flight number on the ticket which is not operated by Qantas
            including Jetstar, Jetstar Japan and other partner airlines including, but not limited to, Emirates and American Airlines will
            not be eligible for this offer. All bookings must be new to be eligible. Any new segments added to an existing PNR during the
            promotional period will not be eligible. Existing PNR for the purpose of this promotion means a PNR booked before 3 September
            2025. Bookings made using flight credits issued prior to 3 September 2025 are eligible for this offer. Flight credits issued on
            or after 3 September 2025 will not be eligible for this offer. Bookings made using eligible flight credits must be made during
            the booking of window 28 August 2025 to 3 September 2025. Any bookings made using flight credits outside of this window will not
            be eligible. For full details, see Qantas Frequent Flyer{' '}
            <DisclaimerLink href={`${HOTELS_BASE_URL}/au/en/frequent-flyer/discover-and-join/terms-and-conditions.html`}>
              terms and conditions
            </DisclaimerLink>
            . Double Qantas Points or double Status Credits will not be earned on some fare types and booking classes. See{' '}
            <DisclaimerLink href={`${HOTELS_BASE_URL}/au/en/frequent-flyer/earn-points/airline-earning-tables.html`}>
              Airline Earning Table
            </DisclaimerLink>{' '}
            for details. The reward chosen will be earned on each eligible flight booked within the offer period and only one (1) reward can
            be chosen per member. If a member registers for multiple rewards or changes their reward, the reward fulfilled will be the last
            reward chosen before 11:59pm (AEST) 3 September 2025. In accordance with Commonwealth policy, this offer is not available to you
            if you are travelling for Commonwealth purposes. It&apos;s a member&apos;s responsibility that they comply with their
            organisation&apos;s policies. Members can request to have the Status Credits or Qantas Points removed, by calling the{' '}
            <DisclaimerLink href={`${HOTELS_BASE_URL}/au/en/support/contact-us/frequent-flyer-membership.html`}>
              Qantas Frequent Flyer Service Centre
            </DisclaimerLink>
            . Double Qantas Points and double Status Credits will be credited within eight weeks after travel. Bonus Status Credits earned
            from this offer do not count towards Loyalty Bonus, Platinum Bonus Reward, Platinum One Bonus Reward and Status
            Accelerator/Challenge earn requirements. Status Credits are not transferable and will be governed by the{' '}
            <DisclaimerLink href={`${HOTELS_BASE_URL}/au/en/frequent-flyer/discover-and-join/terms-and-conditions.html`}>
              Qantas Frequent Flyer terms and conditions
            </DisclaimerLink>
            . Full or part travel of an itinerary is eligible only if you book (regardless of payment date) and travel within the
            promotional dates as listed above. Flights which are ticketed but not booked during the offer period will not be eligible. If
            you cancel your initial qualifying booking or change it to be outside the promotional period, the offer will not apply. This
            offer applies to travel taken by the recipient who registered for the offer. Other passengers in the same booking must register
            separately to be eligible for this offer. Your Qantas Frequent Flyer membership number must be recorded at the time of
            registration and booking to qualify. This offer is not transferable. If you qualify and register for multiple bonus Qantas
            Points and bonus Status Credits offers for the same flight segment, the highest value bonus Qantas Points and bonus Status
            Credits offer will be applied to each individual qualifying segment. Double Qantas Points are based on the{' '}
            <DisclaimerLink href={`${HOTELS_BASE_URL}/au/en/frequent-flyer/discover-and-join/terms-and-conditions.html#jump1`}>
              Base Rate
            </DisclaimerLink>{' '}
            which applies, or the{' '}
            <DisclaimerLink href={`${HOTELS_BASE_URL}/au/en/frequent-flyer/discover-and-join/terms-and-conditions.html#jump25`}>
              Minimum Points Guarantee
            </DisclaimerLink>{' '}
            if applicable and for the purpose of this promotion does not include any{' '}
            <DisclaimerLink href={`${HOTELS_BASE_URL}/au/en/frequent-flyer/status-and-clubs/status.html`}>Status Bonus</DisclaimerLink>
            that may apply for tier members. Only one double Qantas Points and double Status Credits offer can be used per individual
            qualifying segment of your itinerary. Double Qantas Points and/or double Status Credits may be earned on multiple eligible
            flight bookings made during the promotion period. This offer cannot be used in conjunction with any other Points or Status
            Credits offers offered by Qantas. Flights are subject to availability and any applicable fare conditions. Flights can be booked
            via the Qantas website including the Qantas App, a travel agent, or Qantas Contact Centres. Fees and charges may apply and
            cannot be waived as part of the offer, including any applicable change or cancellation fees. Flight gift vouchers purchased
            during the promotion but not redeemed for an eligible flight booking during the promotion period will not be eligible.
          </DisclaimerText>
        </Box>
      </>
    );

    const travelInsuranceTerms = TRAVEL_INSURANCE_CROSS_SELL_ENABLED
      ? isConfirmationPage && (
          <Flex>
            <TravelInsuranceDisclaimer />
          </Flex>
        )
      : null;

    const feesDisclaimer = (
      <DisclaimerText data-testid="fees-disclaimer-text">
        † Includes fee payable in local currency direct to hotel. Prices in AUD are approx and based on today&#39;s exchange rate.
      </DisclaimerText>
    );

    const descriptionAndRatingsDisclaimer = (
      <DisclaimerText data-testid="description-ratings-disclaimer-text">
        Descriptions and ratings featured are based on information provided by hotels and/or other service providers, distinguished
        accordingly. Ratings are either self-assigned by the service provider or obtained through Expedia (displayed as circles{' '}
        <Icon name="circle" color="brightSun" size={12} mb={rem('1px')} aria-label="Circle icon" />
        <Icon name="circle" color="brightSun" size={12} mb={rem('1px')} aria-label="Circle icon" />
        <Icon name="circle" color="brightSun" size={12} mb={rem('1px')} aria-label="Circle icon" />
        ), or by independent third parties (displayed as stars{' '}
        <Icon name="star" color="brightSun" size={12} mb={rem('1px')} aria-label="Star icon" />
        <Icon name="star" color="brightSun" size={12} mb={rem('1px')} aria-label="Star icon" />
        <Icon name="star" color="brightSun" size={12} mb={rem('1px')} aria-label="Star icon" />) Any facilities shown as included are
        subject to change by the service provider. Qantas does not guarantee any particular standard or class of accommodation or other
        service provided by a third party.
      </DisclaimerText>
    );

    const tripAdvisorDisclaimer = (
      <Flex pt={4} flexWrap={'wrap'} gap={1} data-testid="trip-advisor-disclaimer-text">
        <DisclaimerText>Hotel ratings and reviews provided by</DisclaimerText>
        <TripAdvisorLogo src={TRIP_ADVISOR_IMG_BRANDING_URL} alt="Trip-Advisor Logo" role="img" aria-label="Trip-Advisor Logo" />
      </Flex>
    );

    const qffFinalTerms = (
      <DisclaimerText data-testid="qff-final-terms-text">
        {campaignTermsAndConditions ? (
          <Markup content={`^ ${campaignTermsAndConditions} &nbsp;`} />
        ) : showBlueBannerTermsInQffFinalTerms ? (
          <Flex flexDirection="column">
            <Box mb={3}>^ {blueBannerCampaignDisclaimer}</Box>
            <Box>^ {blueBannerAdditionalDisclaimer}</Box>
          </Flex>
        ) : (
          <>
            ^ {qffTerms} {qffDepositPay} {qffFaqs}
          </>
        )}
      </DisclaimerText>
    );

    const blueBannerDisclaimer = (
      <>
        {showBlueBannerDisclaimer && (
          <Flex flexDirection="column">
            {blueBannerCampaignDisclaimer && (
              <DisclaimerText
                marginBottom={3}
                data-testid="blue-banner-disclaimer-text"
                dangerouslySetInnerHTML={{ __html: blueBannerCampaignDisclaimer }}
                color={themeGet('colors.greys.steel')}
              />
            )}

            {blueBannerAdditionalDisclaimer && (
              <DisclaimerText
                marginBottom={3}
                data-testid="blue-banner-additional-disclaimer-text"
                dangerouslySetInnerHTML={{ __html: blueBannerAdditionalDisclaimer }}
                color={themeGet('colors.greys.steel')}
              />
            )}
          </Flex>
        )}
      </>
    );

    const depositPayDisclaimer = (
      <DisclaimerText data-testid="deposit-pay-disclaimer-text">
        ^* A minimum 20% deposit, payable using a payment card or using Qantas Points, is required to secure your booking. Deposit payment
        terms will only be permitted for bookings with a free cancellation option, within Australia. Final payment of the balance owing is
        payable 7 days prior to the end of the free cancellation window for the booking, and will be automatically charged to the card
        details provided at the time of booking. Where payment of the balance is not made within the required timeframe, or the payment card
        provided is declined, the booking will be cancelled, and the deposit will be refunded in full. To amend a Deposit Pay booking, the
        original booking will need to be cancelled before the end of the free cancellation window to be eligible for a refund of your
        deposit and a new booking will need to be placed. No changes can be made to a Deposit Pay booking until full payment is received.
        Deposit Pay is available at the discretion of Qantas, and may be removed or revoked at any time. Payment of a deposit indicates
        acceptance of these conditions.
      </DisclaimerText>
    );

    const result: DisclaimerSection[] = [];

    if (POINTS_EARN_ENABLED) {
      result.push({
        id: 'points-disclaimers',
        logo: <DisclaimerItemLogo src={qantasQff} alt="Qantas QFF Logo" role="img" aria-label="Qantas QFF Logo" />,
        items: [
          qffDisclaimer,
          qffFinalTerms,
          qffPointsPay,
          qffBonusPoints,
          savingsDisclaimer,
          yieldifyCampaignDisclaimer,
          airlineDisclaimer,
          standardCampaignDisclaimer,
          depositPayDisclaimer,
          blueBannerDisclaimer,
        ],
      });

      result.push({
        id: 'business-rewards-disclaimers',
        logo: (
          <DisclaimerItemLogo
            src={qantasBusinessRewards}
            alt="Qantas Business Reward Logo"
            role="img"
            aria-label="Qantas Business Reward Logo"
          />
        ),
        items: [qbrDisclaimer],
      });
    } else {
      const items = [savingsDisclaimer];
      result.push({ id: 'nopoints-disclaimers', items });
    }

    result.push({
      id: 'misc-disclaimers',
      items: [travelInsuranceTerms, feesDisclaimer, descriptionAndRatingsDisclaimer, tripAdvisorDisclaimer],
    });

    return result;
  }, [
    isCampaignTermsEnabled,
    isYieldifyCampaignEnabled,
    yieldifyCampaignDisclaimerText,
    isAirlineDisclaimer,
    isConfirmationPage,
    campaignTermsAndConditions,
    showBlueBannerTermsInQffFinalTerms,
    blueBannerCampaignDisclaimer,
    blueBannerAdditionalDisclaimer,
    showBlueBannerDisclaimer,
    emitInteractionEvent,
    standardCampaignDisclaimerText,
  ]);

  return (
    <PageBlock py={15} bg="white">
      <Container data-print-style="hidden">
        <Heading.h2 display="block" fontSize="base" fontWeight="bold" mb={3} mt={5}>
          Important information
        </Heading.h2>
        {sections.map((section) => (
          <Flex key={section.id} data-testid={section.id} flexDirection={['column', 'row']} alignItems="flex-start">
            {section.logo}
            <Box>
              {section.items.map((item, i) => (
                <Box key={i} mb={2}>
                  {item}
                </Box>
              ))}
            </Box>
          </Flex>
        ))}
      </Container>
    </PageBlock>
  );
};

export default Disclaimer;
