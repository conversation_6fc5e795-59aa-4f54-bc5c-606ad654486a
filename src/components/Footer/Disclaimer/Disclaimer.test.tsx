import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import { userEvent } from '@testing-library/user-event';
import { useDataLayer } from 'hooks/useDataLayer';
import '@testing-library/jest-dom';
import { mocked } from 'test-utils';
import Disclaimer from './Disclaimer';
import { getCampaignTermsAndConditions } from 'store/campaign/campaignSelectors';
import { getPathName } from 'store/router/routerSelectors';
import * as config from 'config';
import useCampaignTerms from 'hooks/optimizely/useCampaignTerms';
import useYieldifyCampaign from 'hooks/optimizely/useYieldifyCampaign';
import useStandardCampaign from 'hooks/optimizely/useStandardCampaign';
import { renderWithProviders } from 'test-utils/reactUtils';

jest.mock('config');
jest.mock('store/campaign/campaignSelectors', () => ({ getCampaignTermsAndConditions: jest.fn() }));
jest.mock('store/router/routerSelectors');
jest.mock('store/user/userSelectors');
jest.mock('hooks/optimizely/useCampaignTerms');
jest.mock('hooks/optimizely/useYieldifyCampaign');
jest.mock('hooks/optimizely/useStandardCampaign');

jest.mock('hooks/useDataLayer');
const emitInteractionEvent = jest.fn();

const pointsPlusPayTermsAndConditions =
  '* Qantas Frequent Flyer members can redeem Qantas Points when booking hotel accommodation through qantas.com/hotels or holiday packages through qantas.com/holidays, using Points Plus Pay. Members cannot redeem points for additional charges paid to the hotel for extras (including cots, breakfasts and other incidentals) on check-in or check-out (as applicable). Points Plus Pay allows you to choose the number of Qantas Points you redeem above the specified minimum level of 5,000 and pay for the remainder of the booking value with an Accepted Payment Card (including VISA, MasterCard or American Express). Points Plus Pay is not available for Classic Hotel Rewards.';
const termsAndConditions = 'special terms and conditions for this campaign';
const blueBannerCampaignDisclaimer = 'Blue Banner disclaimer';
const blueBannerAdditionalDisclaimer = 'Blue Banner additional disclaimer';
const yieldifyCampaignDisclaimer = 'Yieldify campaign disclaimer';
const standardCampaignDisclaimer = 'Local campaign disclaimer';

beforeEach(() => {
  jest.clearAllMocks();
  emitInteractionEvent.mockClear();
  mocked(useDataLayer).mockReturnValue({ emitInteractionEvent });
  mocked(getCampaignTermsAndConditions).mockReturnValue(null);
  mocked(getPathName).mockReturnValue('/');
  mocked(useYieldifyCampaign).mockReturnValue({
    isReady: true,
    isYieldifyCampaignEnabled: false,
    disclaimer: '',
  });
  mocked(useCampaignTerms).mockReturnValue({
    isReady: true,
    isCampaignTermsEnabled: false,
    disclaimer: '',
  });
  mocked(useStandardCampaign).mockReturnValue({
    isReady: true,
    isStandardCampaignEnabled: true,
    termsAndConditions: '',
    additionalTermsAndConditions: '',
    replaceQffFinalTerms: '',
  });

  Object.assign(config, jest.requireActual('config'));
});

describe('with POINTS_EARN_ENABLED off', () => {
  beforeEach(() => {
    Object.assign(config, { POINTS_EARN_ENABLED: false });
  });

  it('does not render points disclaimer', async () => {
    renderWithProviders(<Disclaimer />);
    await waitFor(() => expect(screen.queryByTestId('points-disclaimers')).not.toBeInTheDocument());
  });

  it('does not render qbr disclaimer', async () => {
    renderWithProviders(<Disclaimer />);
    await waitFor(() => expect(screen.queryByTestId('qbr-disclaimer-text')).not.toBeInTheDocument());
  });

  it('shows the savings disclaimer', async () => {
    renderWithProviders(<Disclaimer />);
    expect(await screen.findByText(/Saving is off the hotel's generally available rate/i)).toBeInTheDocument();
  });
});

describe('with POINTS_EARN_ENABLED on', () => {
  beforeEach(() => {
    Object.assign(config, { POINTS_EARN_ENABLED: true });
  });

  it('displays the qffDisclaimer', async () => {
    renderWithProviders(<Disclaimer />);
    expect(
      await screen.findByText(
        /You must be a Qantas Frequent Flyer member to earn and redeem points\. Membership and points are subject to the Qantas Frequent Flyer program terms and conditions\./i,
      ),
    ).toBeInTheDocument();
  });

  describe('qffFinalTerms', () => {
    describe('when the Global Campaign and blue banner campaign are OFF', () => {
      it('displays the qffTerms', async () => {
        renderWithProviders(<Disclaimer />);
        expect(await screen.findByText(/Qantas Frequent Flyer members will earn 3 Qantas Points per A\$1 spent/i)).toBeInTheDocument();
      });

      it('displays the qffDepositPay', async () => {
        renderWithProviders(<Disclaimer />);
        expect(
          await screen.findByText(/Deposit Pay is available on selected properties that offer a free cancellation window/i),
        ).toBeInTheDocument();
      });

      it('displays the qffFaqs', async () => {
        renderWithProviders(<Disclaimer />);
        expect(await screen.findByRole('link', { name: /FAQ page/i })).toBeInTheDocument();
      });
    });

    describe('when the the Global Campaign is OFF, the Blue Banner Campaign is ON', () => {
      describe('and the variable replaceQffFinalTerms is TRUE', () => {
        beforeEach(() => {
          mocked(useStandardCampaign).mockReturnValue({
            isReady: true,
            isStandardCampaignEnabled: true,
            termsAndConditions: blueBannerCampaignDisclaimer,
            additionalTermsAndConditions: blueBannerAdditionalDisclaimer,
            replaceQffFinalTerms: true,
          });
        });

        it('displays its disclaimer', async () => {
          renderWithProviders(<Disclaimer />);
          expect(await screen.findByText('^ ' + blueBannerCampaignDisclaimer)).toBeInTheDocument();
          expect(await screen.findByText('^ ' + blueBannerAdditionalDisclaimer)).toBeInTheDocument();
        });

        it('does NOT display the blueBannerDisclaimer', async () => {
          renderWithProviders(<Disclaimer />);
          await waitFor(() => expect(screen.queryByTestId('blue-banner-disclaimer-text')).not.toBeInTheDocument());
          await waitFor(() => expect(screen.queryByTestId('blue-banner-additional-disclaimer-text')).not.toBeInTheDocument());
        });
      });

      describe('and the variable replaceQffFinalTerms is FALSE', () => {
        beforeEach(() => {
          mocked(useStandardCampaign).mockReturnValue({
            isReady: true,
            isStandardCampaignEnabled: true,
            termsAndConditions: blueBannerCampaignDisclaimer,
            additionalTermsAndConditions: blueBannerAdditionalDisclaimer,
            replaceQffFinalTerms: false,
          });
        });

        it('displays the qffTerms', async () => {
          renderWithProviders(<Disclaimer />);
          expect(await screen.findByText(/Qantas Frequent Flyer members will earn 3 Qantas Points per A\$1 spent/i)).toBeInTheDocument();
          expect(
            await screen.findByText(/Deposit Pay is available on selected properties that offer a free cancellation window/i),
          ).toBeInTheDocument();
          expect(await screen.findByRole('link', { name: /FAQ page/i })).toBeInTheDocument();
        });

        it('displays the blueBannerDisclaimer', async () => {
          renderWithProviders(<Disclaimer />);
          expect(await screen.findByText(blueBannerCampaignDisclaimer)).toBeInTheDocument();
          expect(await screen.findByText(blueBannerAdditionalDisclaimer)).toBeInTheDocument();
        });
      });
    });

    describe('when the the Global Campaign and Blue Banner Campaign are ON', () => {
      beforeEach(() => {
        mocked(getCampaignTermsAndConditions).mockReturnValue(termsAndConditions);
        mocked(useStandardCampaign).mockReturnValue({
          isReady: true,
          isStandardCampaignEnabled: true,
          termsAndConditions: blueBannerCampaignDisclaimer,
          additionalTermsAndConditions: blueBannerAdditionalDisclaimer,
          replaceQffFinalTerms: true,
        });
      });

      it('displays its disclaimer', async () => {
        renderWithProviders(<Disclaimer />);
        expect(await screen.findByText('^ ' + termsAndConditions)).toBeInTheDocument();
      });
    });
  });

  describe('qffPointsPay', () => {
    it('displays the points plus pay terms and conditions', async () => {
      renderWithProviders(<Disclaimer />);
      expect(await screen.findByText(pointsPlusPayTermsAndConditions)).toBeInTheDocument();
    });

    it('has a points plus pay link for conditions', async () => {
      renderWithProviders(<Disclaimer />);
      const link = await screen.findByRole('link', { name: /View full terms and conditions here/i });
      expect(link).toBeInTheDocument();
      expect(link).toHaveAttribute('href', 'http://test/hotels/hotels-and-airbnb-terms-and-conditions');
    });

    it('emits a gtm event when clicked', async () => {
      renderWithProviders(<Disclaimer />);
      const link = await screen.findByRole('link', { name: /View full terms and conditions here/i });
      await userEvent.click(link);

      expect(emitInteractionEvent).toHaveBeenCalledWith({
        type: 'Points Plus Pay Conditions',
        value: 'View Full Terms and Conditions Link Selected',
      });
    });
  });

  it('displays the qffBonusPoints', async () => {
    renderWithProviders(<Disclaimer />);
    expect(await screen.findByText(/500 bonus Qantas Points will be awarded to Qantas Frequent Flyer members/i)).toBeInTheDocument();
    expect(await screen.findByRole('link', { name: /^qantas\.com\/airbnb$/i })).toBeInTheDocument();
  });

  it('displays savingsDisclaimer', async () => {
    renderWithProviders(<Disclaimer />);
    expect(await screen.findByText(/Saving is off the hotel's generally available rate/i)).toBeInTheDocument();
  });

  describe('yieldifyCampaignDisclaimer', () => {
    describe('when the qantas-hotels-yieldify-campaign feature is OFF', () => {
      it('does NOT render it', async () => {
        renderWithProviders(<Disclaimer />);
        await waitFor(() => expect(screen.queryByTestId('yieldify-campaign-disclaimer-text')).not.toBeInTheDocument());
      });
    });

    describe('when the qantas-hotels-yieldify-campaign feature is ON', () => {
      beforeEach(() => {
        mocked(useYieldifyCampaign).mockReturnValue({
          isReady: true,
          isYieldifyCampaignEnabled: true,
          disclaimer: yieldifyCampaignDisclaimer,
        });
      });

      it('renders it', async () => {
        renderWithProviders(<Disclaimer />);
        expect(await screen.findByText(yieldifyCampaignDisclaimer)).toBeInTheDocument();
      });
    });
  });

  describe('standardCampaignDisclaimer', () => {
    describe('when the qantas-hotels-campaign-terms feature is OFF', () => {
      it('does NOT display it', async () => {
        renderWithProviders(<Disclaimer />);
        await waitFor(() => expect(screen.queryByTestId('local-campaign-disclaimer-alternative-text')).not.toBeInTheDocument());
      });
    });

    describe('when the qantas-hotels-campaign-terms feature is ON', () => {
      beforeEach(() => {
        mocked(useCampaignTerms).mockReturnValue({
          isReady: true,
          isCampaignTermsEnabled: true,
          disclaimer: standardCampaignDisclaimer,
        });
      });
      it('displays the alternative disclaimer text', async () => {
        renderWithProviders(<Disclaimer />);
        expect(await screen.findByText(standardCampaignDisclaimer)).toBeInTheDocument();
      });
    });
  });

  it('shows the deposit pay disclaimer', async () => {
    renderWithProviders(<Disclaimer />);
    expect(await screen.findByText(/\^*A minimum 20% deposit, payable using a payment card or using Qantas Points/i)).toBeInTheDocument();
  });
});

describe('qbrDisclaimer', () => {
  beforeEach(() => {
    Object.assign(config, { POINTS_EARN_ENABLED: true });
  });

  it('has the expected hrefs', async () => {
    renderWithProviders(<Disclaimer />);
    const businessRewardLink = await screen.findByRole('link', { name: /Conditions apply/i });
    expect(businessRewardLink).toHaveAttribute('href', config.QCOM_TERMS_AND_CONDITIONS_URL);

    const qantasHotelLink = await screen.findByRole('link', { name: /^qantas\.com\/hotels$/i });
    expect(qantasHotelLink).toHaveAttribute('href', `${config.HOTELS_BASE_URL}/hotels`);
  });

  it('emits a gtm event for business reward conditions when clicked', async () => {
    renderWithProviders(<Disclaimer />);
    const businessRewardLink = await screen.findByRole('link', { name: /Conditions apply/i });
    await userEvent.click(businessRewardLink);

    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Business Rewards Conditions',
      value: 'Conditions Apply Link Selected',
    });
  });
});

describe('<TravelInsuranceDisclaimer />', () => {
  describe('when on the Booking Confirmation page', () => {
    beforeEach(() => {
      mocked(getPathName).mockReturnValue('/bookings/123');
      Object.assign(config, { TRAVEL_INSURANCE_CROSS_SELL_ENABLED: true });
    });

    describe('TRAVEL_INSURANCE_CROSS_SELL_ENABLED true', () => {
      it('renders <TravelInsuranceDisclaimer />', async () => {
        renderWithProviders(<Disclaimer />);
        expect(await screen.findByTestId('travel-insurance-disclaimer')).toBeInTheDocument();
        expect(await screen.findByTestId('travel-insurance-points-disclaimer')).toBeInTheDocument();
      });
    });

    describe('with TRAVEL_INSURANCE_CROSS_SELL_ENABLED false', () => {
      beforeEach(() => {
        Object.assign(config, { TRAVEL_INSURANCE_CROSS_SELL_ENABLED: false });
      });

      it('does NOT render <TravelInsuranceDisclaimer />', async () => {
        renderWithProviders(<Disclaimer />);
        await waitFor(() => expect(screen.queryByTestId('travel-insurance-disclaimer')).not.toBeInTheDocument());
        await waitFor(() => expect(screen.queryByTestId('travel-insurance-points-disclaimer')).not.toBeInTheDocument());
      });
    });
  });

  describe('when NOT on the Booking Confirmation page', () => {
    beforeEach(() => {
      mocked(getPathName).mockReturnValue('/search/list');
    });

    it('does NOT render <TravelInsuranceDisclaimer />', async () => {
      renderWithProviders(<Disclaimer />);
      await waitFor(() => expect(screen.queryByTestId('travel-insurance-disclaimer')).not.toBeInTheDocument());
      await waitFor(() => expect(screen.queryByTestId('travel-insurance-points-disclaimer')).not.toBeInTheDocument());
    });
  });
});

describe('Disclaimer component - Additional comprehensive tests', () => {
  beforeEach(() => {
    Object.assign(config, { POINTS_EARN_ENABLED: true });
    mocked(getPathName).mockReturnValue('/');
  });

  it('displays the "Important information" heading', async () => {
    renderWithProviders(<Disclaimer />);
    expect(await screen.findByRole('heading', { name: /Important information/i })).toBeInTheDocument();
  });

  it('displays the Qantas QFF Logo for points disclaimers when POINTS_EARN_ENABLED is true', async () => {
    renderWithProviders(<Disclaimer />);
    expect(await screen.findByRole('img', { name: /Qantas QFF Logo/i })).toBeInTheDocument();
  });

  it('displays the Qantas Business Reward Logo for business rewards disclaimers when POINTS_EARN_ENABLED is true', async () => {
    renderWithProviders(<Disclaimer />);
    expect(await screen.findByRole('img', { name: /Qantas Business Reward Logo/i })).toBeInTheDocument();
  });

  it('does not display any logos when POINTS_EARN_ENABLED is false', async () => {
    Object.assign(config, { POINTS_EARN_ENABLED: false });
    renderWithProviders(<Disclaimer />);
    await waitFor(() => expect(screen.queryByRole('img', { name: /Qantas QFF Logo/i })).not.toBeInTheDocument());
    await waitFor(() => expect(screen.queryByRole('img', { name: /Qantas Business Reward Logo/i })).not.toBeInTheDocument());
  });

  it('displays the fees disclaimer', async () => {
    renderWithProviders(<Disclaimer />);
    expect(
      await screen.findByText(
        /Includes fee payable in local currency direct to hotel\. Prices in AUD are approx and based on today's exchange rate\./i,
      ),
    ).toBeInTheDocument();
  });

  it('displays the description and ratings disclaimer', async () => {
    renderWithProviders(<Disclaimer />);
    expect(await screen.findByText(/Descriptions and ratings featured are based on information provided by hotels/i)).toBeInTheDocument();
    // Assuming the previous fix for Circle/Star icons has been applied in the component source
    expect(screen.getAllByLabelText('Circle icon').length).toBeGreaterThan(0);
    expect(screen.getAllByLabelText('Star icon').length).toBeGreaterThan(0);
  });

  it('displays the Trip Advisor disclaimer and logo', async () => {
    renderWithProviders(<Disclaimer />);
    expect(await screen.findByText(/Hotel ratings and reviews provided by/i)).toBeInTheDocument();
    expect(await screen.findByRole('img', { name: /Trip-Advisor Logo/i })).toBeInTheDocument();
  });

  it('emits a GTM event when the FAQ page link is clicked', async () => {
    renderWithProviders(<Disclaimer />);
    const faqLink = await screen.findByRole('link', { name: /FAQ page/i });
    await userEvent.click(faqLink);
    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'FAQ',
      value: 'View FAQ Page Link Selected',
    });
  });

  it('emits a GTM event when the Airbnb homepage link is clicked', async () => {
    renderWithProviders(<Disclaimer />);
    const airbnbLink = await screen.findByRole('link', { name: /^qantas\.com\/airbnb$/i });
    await userEvent.click(airbnbLink);
    expect(emitInteractionEvent).toHaveBeenCalledWith({
      type: 'Terms and Conditions',
      value: 'AirBnb Selected',
    });
  });

  it('renders blue banner disclaimers when replaceQffFinalTerms is false and isStandardCampaignEnabled is true', async () => {
    mocked(useStandardCampaign).mockReturnValue({
      isReady: true,
      isStandardCampaignEnabled: true,
      termsAndConditions: 'Blue Banner Campaign A',
      additionalTermsAndConditions: 'Blue Banner Campaign B',
      replaceQffFinalTerms: false,
    });
    renderWithProviders(<Disclaimer />);
    expect(await screen.findByText('Blue Banner Campaign A')).toBeInTheDocument();
    expect(await screen.findByText('Blue Banner Campaign B')).toBeInTheDocument();
  });

  it('does not render blue banner disclaimers when isStandardCampaignEnabled is false', async () => {
    mocked(useStandardCampaign).mockReturnValue({
      isReady: true,
      isStandardCampaignEnabled: false,
      termsAndConditions: 'Blue Banner Campaign A',
      additionalTermsAndConditions: 'Blue Banner Campaign B',
      replaceQffFinalTerms: false,
    });
    renderWithProviders(<Disclaimer />);
    await waitFor(() => expect(screen.queryByText('Blue Banner Campaign A')).not.toBeInTheDocument());
    await waitFor(() => expect(screen.queryByText('Blue Banner Campaign B')).not.toBeInTheDocument());
  });

  it('ensures correct accessibility attributes for logos', async () => {
    renderWithProviders(<Disclaimer />);
    const qffLogo = await screen.findByRole('img', { name: /Qantas QFF Logo/i });
    expect(qffLogo).toHaveAttribute('alt', 'Qantas QFF Logo');
    expect(qffLogo).toHaveAttribute('aria-label', 'Qantas QFF Logo');

    const qbrLogo = await screen.findByRole('img', { name: /Qantas Business Reward Logo/i });
    expect(qbrLogo).toHaveAttribute('alt', 'Qantas Business Reward Logo');
    expect(qbrLogo).toHaveAttribute('aria-label', 'Qantas Business Reward Logo');

    const tripAdvisorLogo = await screen.findByRole('img', { name: /Trip-Advisor Logo/i });
    expect(tripAdvisorLogo).toHaveAttribute('alt', 'Trip-Advisor Logo');
    expect(tripAdvisorLogo).toHaveAttribute('aria-label', 'Trip-Advisor Logo');
  });

  it('renders disclaimers inside accessible sections based on the structure', async () => {
    renderWithProviders(<Disclaimer />);
    const importantInfoHeading = screen.getByRole('heading', { name: /Important information/i });
    expect(importantInfoHeading).toBeInTheDocument();
    expect(screen.getByTestId('points-disclaimers')).toBeInTheDocument();
    expect(screen.getByTestId('business-rewards-disclaimers')).toBeInTheDocument();
    expect(screen.getByTestId('misc-disclaimers')).toBeInTheDocument();
  });

  it('renders points disclaimers first when POINTS_EARN_ENABLED is true', async () => {
    renderWithProviders(<Disclaimer />);
    const pointsSection = screen.getByTestId('points-disclaimers');
    const businessRewardsSection = screen.getByTestId('business-rewards-disclaimers');
    const miscSection = screen.getByTestId('misc-disclaimers');

    expect(pointsSection.compareDocumentPosition(businessRewardsSection)).toBe(Node.DOCUMENT_POSITION_FOLLOWING);
    expect(businessRewardsSection.compareDocumentPosition(miscSection)).toBe(Node.DOCUMENT_POSITION_FOLLOWING);
  });

  it('renders no points disclaimers section when POINTS_EARN_ENABLED is false', async () => {
    Object.assign(config, { POINTS_EARN_ENABLED: false });
    renderWithProviders(<Disclaimer />);
    await waitFor(() => expect(screen.queryByTestId('points-disclaimers')).not.toBeInTheDocument());
    await waitFor(() => expect(screen.queryByTestId('business-rewards-disclaimers')).not.toBeInTheDocument());
    expect(screen.getByTestId('nopoints-disclaimers')).toBeInTheDocument();
  });
});
