import { renderHook } from '@testing-library/react-hooks';
import { useDecision } from '@optimizely/react-sdk';
import useStandardCampaign from './useStandardCampaign';

jest.mock('@optimizely/react-sdk', () => ({
  useDecision: jest.fn(),
}));

const defaultVariables = {
  additionalTermsAndConditions: 'terms',
  priceMessages: 'priceMessages',
  replaceQffFinalTerms: 'replaceQffFinalTerms',
  termsAndConditions: 'termsAndConditions',
  title: 'title',
  message: 'message',
};

const defaultMockDecision = {
  enabled: false,
  variables: defaultVariables,
};

describe('useStandardCampaign', () => {
  beforeEach(() => {
    (useDecision as jest.Mock).mockReturnValue([defaultMockDecision, false]);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('returns default state when optimizely is not ready', () => {
    (useDecision as jest.Mock).mockReturnValue([{ enabled: false, variables: defaultVariables }, false]);

    const { result } = renderHook(() => useStandardCampaign());

    expect(result.current).toEqual({
      isReady: false,
      isStandardCampaignEnabled: false,
      ...defaultVariables,
    });
  });

  it('returns isStandardCampaignEnabled as true when when feature flag is on', () => {
    (useDecision as jest.Mock).mockReturnValue([{ enabled: true, variables: defaultVariables }, true]);

    const { result } = renderHook(() => useStandardCampaign());

    expect(result.current.isStandardCampaignEnabled).toBeTruthy();
  });

  it('returns isStandardCampaignEnabled as false when feature flag is off', () => {
    const { result } = renderHook(() => useStandardCampaign());

    expect(result.current.isStandardCampaignEnabled).toBeFalsy();
  });
});
