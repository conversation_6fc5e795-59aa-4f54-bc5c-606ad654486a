import { useDecision } from '@optimizely/react-sdk';

type ReturnVariables = {
  message: string;
  additionalTermsAndConditions?: string;
  priceMessages?: string;
  replaceQffFinalTerms?: boolean;
  termsAndConditions?: string;
  title?: string;
};

type Result = {
  isReady: boolean;
  isStandardCampaignEnabled: boolean;
} & ReturnVariables;

const FEATURE_FLAG_NAME = 'qantas-hotels-blue-banner-message';

const useStandardCampaign = (): Result => {
  const [decision, isReady] = useDecision(FEATURE_FLAG_NAME, {
    autoUpdate: true,
  });

  const variables = decision.variables as ReturnVariables;

  return {
    isReady,
    isStandardCampaignEnabled: decision.enabled,
    message: variables.message,
    additionalTermsAndConditions: variables.additionalTermsAndConditions,
    priceMessages: variables.priceMessages,
    replaceQffFinalTerms: variables.replaceQffFinalTerms,
    termsAndConditions: variables.termsAndConditions,
    title: variables.title,
  };
};

export default useStandardCampaign;
