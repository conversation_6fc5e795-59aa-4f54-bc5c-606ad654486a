import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { useDispatch } from 'react-redux';
import { createInstance, OptimizelyProvider } from '@optimizely/react-sdk';
import { useRouter } from 'next/router';
import { v4 as uuidv4 } from 'uuid';
import { OptimizelyProviderWrapper, resetOptimizelyInstance } from './optimizely';
import getIsVipUser from 'lib/getIsVipUser';
import getUserGroup from 'lib/getUserGroup';
import getOptimizelyUserId from 'lib/getOptimizelyUserId';
import { optimizelyFeatureDecision } from 'store/ui/uiActions';

jest.mock('uuid');
jest.mock('react-redux', () => ({
  useDispatch: jest.fn(),
}));

jest.mock('@optimizely/react-sdk', () => ({
  createInstance: jest.fn(() => ({
    notificationCenter: {
      addNotificationListener: jest.fn(),
      removeNotificationListener: jest.fn(),
    },
  })),
  OptimizelyProvider: jest.fn(({ children }) => <div data-testid="mock-optimizely-provider">{children}</div>),
  enums: {
    NOTIFICATION_TYPES: {
      DECISION: 'DECISION',
    },
  },
}));

jest.mock('next/router', () => ({
  useRouter: jest.fn(),
}));

jest.mock('config', () => ({
  OPTIMIZELY_DATAFILE: 'mock-sdk-key',
}));
jest.mock('config/constants', () => ({
  OPTIMIZELY_EXPERIMENT_FLAGS: ['flag1', 'flag2'],
}));

jest.mock('lib/getIsVipUser', () => jest.fn());
jest.mock('lib/getUserGroup', () => jest.fn());
jest.mock('lib/getOptimizelyUserId', () => jest.fn());
jest.mock('store/ui/uiActions', () => ({
  optimizelyFeatureDecision: jest.fn(),
}));

const mockDecisionsFlag1 = {
  userId: 'test-user-id',
  decisionInfo: {
    flagKey: 'flag1',
    ruleKey: 'rule',
    variationKey: 'var',
    enabled: false,
  },
};
const mockDecisionsFlag2 = {
  userId: 'test-user-id',
  decisionInfo: {
    flagKey: 'flag2',
    ruleKey: 'rule',
    variationKey: 'var',
    enabled: true,
  },
};

describe('OptimizelyProviderWrapper', () => {
  const mockDispatch = jest.fn();
  const mockAddListener = jest.fn();
  const mockRemoveListener = jest.fn();
  const mockCreateInstance = createInstance as jest.Mock;
  const mockUseRouter = useRouter as jest.Mock;
  const mockGetIsVipUser = getIsVipUser as jest.Mock;
  const mockGetUserGroup = getUserGroup as jest.Mock;
  const mockGetOptimizelyUserId = getOptimizelyUserId as jest.Mock;
  const mockOptimizelyFeatureDecision = optimizelyFeatureDecision as unknown as jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();

    uuidv4.mockReturnValue('random-id');
    resetOptimizelyInstance();
    (useDispatch as jest.Mock).mockReturnValue(mockDispatch);
    mockCreateInstance.mockReturnValue({
      notificationCenter: {
        addNotificationListener: mockAddListener,
        removeNotificationListener: mockRemoveListener,
      },
    });
    mockUseRouter.mockReturnValue({ isReady: true });
    mockGetOptimizelyUserId.mockReturnValue('test-user-id');
    mockGetUserGroup.mockReturnValue('test-user-group');
    mockGetIsVipUser.mockReturnValue(false);
  });

  it('renders children inside OptimizelyProvider', () => {
    render(
      <OptimizelyProviderWrapper>
        <span>Test Child</span>
      </OptimizelyProviderWrapper>,
    );
    expect(screen.getByTestId('mock-optimizely-provider')).toBeInTheDocument();
    expect(screen.getByText('Test Child')).toBeInTheDocument();
  });

  it('calls createInstance only once per lifecycle', () => {
    render(
      <OptimizelyProviderWrapper>
        <span>First</span>
      </OptimizelyProviderWrapper>,
    );
    render(
      <OptimizelyProviderWrapper>
        <span>Second</span>
      </OptimizelyProviderWrapper>,
    );
    expect(mockCreateInstance).toHaveBeenCalledTimes(1);
  });

  it('passes correct user attributes when all values are present', () => {
    const mockOptimizelyProvider = OptimizelyProvider as jest.Mock;
    mockGetOptimizelyUserId.mockReturnValue('user-123');
    mockGetUserGroup.mockReturnValue('gold');
    mockGetIsVipUser.mockReturnValue(true);

    render(
      <OptimizelyProviderWrapper>
        <span>Child</span>
      </OptimizelyProviderWrapper>,
    );

    expect(mockOptimizelyProvider).toHaveBeenCalledWith(
      expect.objectContaining({
        user: {
          id: 'user-123',
          attributes: {
            user_group: 'gold',
            is_vip: true,
          },
        },
      }),
      {},
    );
  });

  it('uses uuid fallback for user id if optimizelyUserId is undefined', () => {
    const mockOptimizelyProvider = OptimizelyProvider as jest.Mock;
    mockGetOptimizelyUserId.mockReturnValue(undefined);

    render(
      <OptimizelyProviderWrapper>
        <span>Child</span>
      </OptimizelyProviderWrapper>,
    );

    expect(mockOptimizelyProvider).toHaveBeenCalledWith(
      expect.objectContaining({
        user: expect.objectContaining({ id: 'random-id' }),
      }),
      {},
    );
  });

  it('removes notification listener on unmount', () => {
    const { unmount } = render(
      <OptimizelyProviderWrapper>
        <span>Unmount Test</span>
      </OptimizelyProviderWrapper>,
    );
    unmount();
    expect(mockRemoveListener).toHaveBeenCalled();
  });

  it('does not dispatch if flagKey is not in experimentFlags', async () => {
    render(
      <OptimizelyProviderWrapper>
        <span>Child</span>
      </OptimizelyProviderWrapper>,
    );
    mockAddListener.mock.calls[0][1]({
      userId: 'test-user-id',
      decisionInfo: {
        flagKey: 'non-experiment-flag',
        ruleKey: 'rule',
        variationKey: 'var',
        enabled: true,
      },
    });
    await waitFor(() => {
      expect(mockDispatch).not.toHaveBeenCalled();
    });
  });

  it('dispatches only when all experiment flags are processed', async () => {
    resetOptimizelyInstance();
    mockGetOptimizelyUserId.mockReturnValue('test-user-id');
    render(
      <OptimizelyProviderWrapper>
        <span>Child</span>
      </OptimizelyProviderWrapper>,
    );
    const callback = mockAddListener.mock.calls[0][1];
    callback(mockDecisionsFlag1);
    await waitFor(() => {
      expect(mockDispatch).not.toHaveBeenCalled();
    });
    callback(mockDecisionsFlag2);
    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalledTimes(1);
    });
    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalledWith(
        mockOptimizelyFeatureDecision({
          fxp_variant_string: expect.stringContaining('flag1_var1_rule1,flag2_var2_rule2'),
          enabled_string: 'true,false',
        }),
      );
    });
  });

  it('does not dispatch if isReady is false', async () => {
    mockUseRouter.mockReturnValue({ isReady: false });
    render(
      <OptimizelyProviderWrapper>
        <span>Child</span>
      </OptimizelyProviderWrapper>,
    );
    mockAddListener.mock.calls[0][1](mockDecisionsFlag1);
    await waitFor(() => {
      expect(mockDispatch).not.toHaveBeenCalled();
    });
  });

  it('does not dispatch if optimizelyUserId is null', async () => {
    mockGetOptimizelyUserId.mockReturnValue(null);
    render(
      <OptimizelyProviderWrapper>
        <span>Child</span>
      </OptimizelyProviderWrapper>,
    );
    mockAddListener.mock.calls[0][1](mockDecisionsFlag1);
    await waitFor(() => {
      expect(mockDispatch).not.toHaveBeenCalled();
    });
  });

  it('does not dispatch twice for the same flagKey', async () => {
    render(
      <OptimizelyProviderWrapper>
        <span>Child</span>
      </OptimizelyProviderWrapper>,
    );
    const callback = mockAddListener.mock.calls[0][1];
    callback(mockDecisionsFlag1);
    callback(mockDecisionsFlag2);
    callback(mockDecisionsFlag1);
    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalledTimes(1);
    });
  });

  it('handles enabled false correctly in analytics payload', async () => {
    render(
      <OptimizelyProviderWrapper>
        <span>Child</span>
      </OptimizelyProviderWrapper>,
    );
    const callback = mockAddListener.mock.calls[0][1];
    callback(mockDecisionsFlag1);
    callback(mockDecisionsFlag2);

    await waitFor(() => {
      expect(mockDispatch).toHaveBeenCalledWith(
        mockOptimizelyFeatureDecision({
          fxp_variant_string: expect.stringContaining('flag1_var_rule'),
          enabled: false,
        }),
      );
    });
  });
});
