import React, { ReactNode, useEffect, useRef } from 'react';
import { useDispatch } from 'react-redux';
import { createInstance, OptimizelyProvider, enums, ReactSDKClient } from '@optimizely/react-sdk';
import { useRouter } from 'next/router';
import { kebabCase } from 'lodash';
import { v4 as uuidv4 } from 'uuid';
import { OPTIMIZELY_DATAFILE } from 'config';
import { OPTIMIZELY_EXPERIMENT_FLAGS } from 'config/constants';
import getIsVipUser from 'lib/getIsVipUser';
import getUserGroup from 'lib/getUserGroup';
import getOptimizelyUserId from 'lib/getOptimizelyUserId';
import { optimizelyFeatureDecision } from 'store/ui/uiActions';
import { OptimizelyFeatureDecision } from 'types/events';

interface OptimizelyDecisionNotificationPayload {
  type: string;
  userId: string;
  attributes: Record<string, string>;
  decisionInfo: {
    flagKey: string;
    ruleKey: string;
    variationKey: string;
    enabled: boolean;
  };
}

// Create the Optimizely instance *outside* the component
// so it's only created once per application lifecycle.
// This also avoids recreating it on every render of the wrapper.
let optimizely: ReactSDKClient | null = null;

const getOptimizelyInstance = () => {
  if (!optimizely) {
    optimizely = createInstance({
      eventBatchSize: 100,
      eventFlushInterval: 3000,
      odpOptions: {
        disabled: true,
      },
      datafileOptions: {
        autoUpdate: true,
        updateInterval: 60000,
      },
      sdkKey: OPTIMIZELY_DATAFILE,
      logLevel: 'none',
    });
  }
  return optimizely;
};

// Export for testing purposes
export const resetOptimizelyInstance = () => {
  optimizely = null;
};

export const OptimizelyProviderWrapper = React.forwardRef<HTMLDivElement, { children: ReactNode }>((props, ref) => {
  const { children } = props;
  const dispatch = useDispatch();
  const { isReady } = useRouter();

  const optimizelyUserId = getOptimizelyUserId();
  const ghUserGroup = getUserGroup();
  const isVipUser = getIsVipUser();

  const processedFlagsRef = useRef(new Set<string>());
  const concatenatedKeysRef = useRef<string[]>([]);
  const enabledValuesRef = useRef<string[]>([]);
  const optimizelyInstance = getOptimizelyInstance();

  const experimentFlags: string[] = OPTIMIZELY_EXPERIMENT_FLAGS;

  useEffect(() => {
    const expectedExperimentsCount = experimentFlags.length;

    const listenerId = optimizelyInstance.notificationCenter.addNotificationListener(
      enums.NOTIFICATION_TYPES.DECISION,
      (payload: OptimizelyDecisionNotificationPayload) => {
        const { decisionInfo } = payload;
        const { flagKey, ruleKey, variationKey, enabled } = decisionInfo;

        const shouldTrack = experimentFlags.includes(flagKey);
        if (isReady && optimizelyUserId && shouldTrack && !processedFlagsRef.current.has(flagKey)) {
          const concatenatedKeys = [flagKey, variationKey, ruleKey].map(kebabCase).join('_');
          concatenatedKeysRef.current.push(concatenatedKeys);
          enabledValuesRef.current.push(enabled ? 'true' : 'false');
          processedFlagsRef.current.add(flagKey);

          if (processedFlagsRef.current.size === expectedExperimentsCount) {
            const fxp_variant_string = concatenatedKeysRef.current.join(',');
            const enabled_string: string = enabledValuesRef.current.join(',');

            const analyticsPayload: OptimizelyFeatureDecision = {
              fxp_variant_string,
              enabled_string,
            };

            dispatch(optimizelyFeatureDecision(analyticsPayload));
          }
        }
      },
    );

    return () => {
      optimizelyInstance.notificationCenter.removeNotificationListener(listenerId);
    };
  }, [dispatch, isReady, optimizelyUserId, optimizelyInstance, experimentFlags]);

  return (
    <div ref={ref}>
      <OptimizelyProvider
        optimizely={optimizelyInstance}
        user={{
          id: optimizelyUserId || uuidv4(),
          attributes: {
            user_group: ghUserGroup,
            is_vip: isVipUser,
          },
        }}
      >
        {children}
      </OptimizelyProvider>
    </div>
  );
});
