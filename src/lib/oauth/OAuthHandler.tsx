import { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useDispatch, useStore } from 'react-redux';
import authenticateUserOAuth from './authenticateUserOAuth';
import { useOAuthState } from './useOAuthState';

export const OAuthHandler = () => {
  const { route, asPath, replace } = useRouter();
  const dispatch = useDispatch();
  const { getState } = useStore();
  const [state] = useOAuthState();

  useEffect(() => {
    if (!asPath.startsWith('/auth')) window.localStorage.setItem(state, JSON.stringify(asPath));
  }, [state, asPath]);

  // Might be running this too often with this
  useEffect(() => {
    if (route !== '/auth') {
      authenticateUserOAuth(dispatch, getState);
      const currentURL = new URL(window.location.href);
      if (currentURL.searchParams.has('loginSuccess')) {
        currentURL.searchParams.delete('loginSuccess');
        replace(currentURL.pathname + currentURL.search, undefined, { shallow: true });
      }
    }
  }, [route, dispatch, getState, replace]);

  return null;
};
