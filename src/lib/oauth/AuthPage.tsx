import React, { useEffect } from 'react';
import { useRouter } from 'next/router';
import { QFF_AUTH_API_URL } from 'config';
import styled from '@emotion/styled';
import { keyframes } from '@emotion/core';
import { getLogoffQFF } from './logout';
import Cookies from 'js-cookie';
import { COOKIE_NAMES } from 'lib/enums/cookies';
import type { NextPage } from 'next';
import { useOAuthState } from './useOAuthState';
import { lslClientId } from 'lib/qffAuth/getUrls';

const Backdrop = styled.div`
  position: fixed;
  inset: 0;
  z-index: 9999999;
  background: white;

  display: flex;
  align-items: center;
  justify-content: center;
`;

const spin = keyframes`
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(1turn);
  }
`;

const Spinner = styled.div`
  width: 30px;
  height: 30px;

  border: 2px solid rgba(0, 0, 0, 0.05);
  border-width: 2px;
  border-radius: 50%;
  border-left-color: #e40000;

  vertical-align: middle;

  animation: ${spin} 1.1s linear infinite;
`;

const AuthPage: NextPage = () => {
  const { pathname, query: { state, code, error }, replace } = useRouter(); // prettier-ignore

  const [savedState] = useOAuthState();

  useEffect(() => {
    const handle = async () => {
      if (pathname !== '/auth') return;

      if (typeof state !== 'string') return replace('/');

      let previousRoute = '/';

      try {
        const savedRoute = localStorage.getItem(savedState);
        previousRoute = savedRoute ? JSON.parse(savedRoute) : '/';
      } catch (error) {
        // adding this check to fix a Type error for when there is no saved routes/ or error for when the storage is not available
      }
      const redirectRoute = previousRoute.startsWith('/') ? previousRoute : '/';

      if (typeof error === 'string' && error !== '') {
        if (error === 'login_required') {
          // Removing any instances of `qff_wl_sess` cookie to be safe
          Cookies.remove(COOKIE_NAMES.QL_WL_SESSION);
          Cookies.remove(COOKIE_NAMES.QL_WL_SESSION, { domain: 'qantas.com', path: '/' });
          Cookies.remove(COOKIE_NAMES.QL_WL_SESSION, { domain: '.qantas.com', path: '/' });
        }
        return replace(redirectRoute);
      }
      if (decodeURIComponent(state) !== savedState) {
        return replace(redirectRoute);
      }
      if (typeof code !== 'string') {
        return replace(redirectRoute);
      }
      const url = new URL(`${QFF_AUTH_API_URL}/callback`);
      url.searchParams.append('code', code);
      url.searchParams.append('state', state);
      url.searchParams.append('client_id', lslClientId);

      const response = await fetch(url.toString(), { credentials: 'include' });

      if (!response.ok && process.env.NEXT_PUBLIC_ENVIRONMENT === 'production') {
        await getLogoffQFF();
      }

      const redirectURL = new URL(redirectRoute, window.location.origin);
      redirectURL.searchParams.append('loginSuccess', '1');

      return replace(redirectURL.pathname + redirectURL.search);
    };

    handle();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [code, state]);

  return (
    <Backdrop>
      <Spinner />
    </Backdrop>
  );
};

export default AuthPage;
