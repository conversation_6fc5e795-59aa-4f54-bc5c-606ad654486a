import { optimizelyFeatureDecision } from 'store/ui/uiActions';
import { createOptimizelyFeatureDecisionEvent, createHotelsOptimizelyFeatureDecisionEventPayload } from '@qantasexperiences/analytics';

const emitOptimizelyFeatureDecisionEvent = ({ payload }) => {
  const optimizelyFeatureDecisionEvent = createOptimizelyFeatureDecisionEvent(
    createHotelsOptimizelyFeatureDecisionEventPayload({
      fxp_variant_string: payload.fxp_variant_string,
      enabled: payload.enabled_string,
    }),
  );

  return optimizelyFeatureDecisionEvent;
};

// eslint-disable-next-line
// @ts-ignore
export default { [optimizelyFeatureDecision]: emitOptimizelyFeatureDecisionEvent };
