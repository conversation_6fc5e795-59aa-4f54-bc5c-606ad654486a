import { createMiddleware } from 'redux-beacon';
import GoogleTagManager from '@redux-beacon/google-tag-manager';
import configureMockStore from 'redux-mock-store';
import { routerMiddleware } from 'connected-react-router';
import emitOptimizelyFeatureDecisionEvent from './emitOptimizelyFeatureDecisionEvent';
import { optimizelyFeatureDecision } from 'store/ui/uiActions';

const defaultInitialState = { router: { action: 'push' } };

const middleware = createMiddleware(emitOptimizelyFeatureDecisionEvent, GoogleTagManager());
const mockStore = configureMockStore([middleware, routerMiddleware]);
const createStore = (initialState) => mockStore({ ...defaultInitialState, ...initialState });

const payload = {
  fxp_variant_string: 'qantas-hotels-available-rooms-message_some-rule_variation-a',
  enabled_string: 'true',
};
let store;

declare global {
  interface Window {
    dataLayer?: unknown[];
  }
}

beforeEach(() => {
  store = createStore({});
  window.dataLayer = [];
});

describe('emitOptimizelyFeatureDecisionEvent', () => {
  it('returns the correct dataLayer', () => {
    store.dispatch(optimizelyFeatureDecision(payload));

    expect(window.dataLayer).toMatchObject([
      {
        event: 'optimizely_feature_decision',
        optimizely: {
          fxp_variant_string: 'qantas-hotels-available-rooms-message_some-rule_variation-a',
          enabled: 'true',
        },
      },
    ]);
  });
});
