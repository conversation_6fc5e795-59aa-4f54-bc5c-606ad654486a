import { Decimal } from 'decimal.js';
import * as selectors from './campaignSelectors';

jest.mock('store/search/searchSelectors');
jest.mock('store/property/propertySelectors');
jest.mock('store/router/routerSelectors');

const state = {
  campaign: {
    campaign: {
      messages: {
        title: 'double points campaign',
        message: 'earn 6 points per dollar',
        callToAction: {
          url: 'qantas.com',
          label: 'Learn more',
          tracking: {
            label: 'label',
            action: 'action',
            category: 'category',
          },
        },
        termsAndConditions: 'terms and conditions apply',
        payWithToggleMessage: '20% off',
        priceMessages: {
          cash: 'cash price message',
          points: 'points price message',
        },
      },
      originalTier: {
        tiers: [{ min: 0, rate: 0.00598, max: 150 }],
      },
      defaultSash: 'Triple Points',
    },
  },
};

describe('getCampaign', () => {
  it('returns the campaign', () => {
    expect(selectors.getCampaign(state)).toEqual(state.campaign.campaign);
  });
});

describe('getCampaignIsEnabled()', () => {
  it('returns the isEnabled value', () => {
    expect(selectors.getCampaignIsEnabled(state)).toEqual(state.campaign.campaign.isEnabled);
  });
});

describe('getCampaignDefaultSash()', () => {
  it('returns the defaultSash value', () => {
    expect(selectors.getCampaignDefaultSash(state)).toEqual(state.campaign.campaign.defaultSash);
  });
});

describe('getCampaignMessages', () => {
  it('returns the campaign messages', () => {
    expect(selectors.getCampaignMessages(state)).toEqual(state.campaign.campaign.messages);
  });
});

describe('getCampaignTitle()', () => {
  it('returns title', () => {
    expect(selectors.getCampaignTitle(state)).toEqual(state.campaign.campaign.messages.title);
  });
});

describe('getCampaignMessage()', () => {
  it('returns message', () => {
    expect(selectors.getCampaignMessage(state)).toEqual(state.campaign.campaign.messages.message);
  });
});

describe('getCampaignTermsAndConditions()', () => {
  it('returns termsAndConditions', () => {
    expect(selectors.getCampaignTermsAndConditions(state)).toEqual(state.campaign.campaign.messages.termsAndConditions);
  });
});

describe('getPointsStrikethroughMessage()', () => {
  it('returns pointsStrikethroughMessage', () => {
    expect(selectors.getPointsStrikethroughMessage(state)).toEqual(state.campaign.campaign.messages.pointsStrikethroughMessage);
  });
});

describe('getPayWithToggleMessage()', () => {
  it('returns payWithToggleMessage', () => {
    expect(selectors.getPayWithToggleMessage(state)).toEqual(state.campaign.campaign.messages.payWithToggleMessage);
  });
});

describe('getCampaignPriceMessages()', () => {
  it('returns price messages', () => {
    expect(selectors.getCampaignPriceMessages(state)).toEqual(state.campaign.campaign.messages.priceMessages);
  });

  describe('without campaign price messages', () => {
    it('returns empty price messages', () => {
      expect(selectors.getCampaignPriceMessages({ campaign: {} })).toEqual({ cash: null, points: null });
    });
  });
});

describe('getCampaignUrl()', () => {
  it('returns cta url', () => {
    expect(selectors.getCampaignUrl(state)).toEqual(state.campaign.campaign.messages.callToAction.url);
  });
});

describe('getCampaignUrlLabel()', () => {
  it('returns cta label', () => {
    expect(selectors.getCampaignUrlLabel(state)).toEqual(state.campaign.campaign.messages.callToAction.label);
  });
});

describe('getCampaignOriginalTiers()', () => {
  it('returns original points tiers', () => {
    const parsedTiers = [{ min: new Decimal(0), rate: new Decimal(0.00598), max: new Decimal(150) }];
    expect(selectors.getCampaignOriginalTiers(state)).toEqual(parsedTiers);
  });
});

describe('getCampaignTracking()', () => {
  it('returns the tracking values', () => {
    expect(selectors.getCampaignTracking(state)).toEqual(state.campaign.campaign.messages.callToAction.tracking);
  });
});
